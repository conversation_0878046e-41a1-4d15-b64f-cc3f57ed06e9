# CAD分类标注工具 V4

## 版本说明

CAD分类标注工具V4是基于V2完整版的精简版本，只包含程序运行必需的核心文件。

## 主要功能

### 1. CAD文件处理
- 支持DXF格式CAD文件读取
- 自动识别墙体、门窗、栏杆等图层
- 智能分组和分类

### 2. 墙体填充功能
- **V2增强版墙体填充** - 处理重叠、间隙和缺失端头
- **交互式填充** - 逐步控制填充过程
- **自动填充** - 一键完成填充

### 3. 空腔识别
- 自动识别墙体内部的空腔区域
- 支持复杂形状的空腔检测
- 可视化空腔显示

### 4. 实体分类标注
- 自动标注：墙体、门窗、栏杆等
- 手动标注：支持自定义分类
- 重新分类：支持已标注组的重新分类

### 5. 可视化界面
- 详细视图：显示当前处理组
- 全图概览：显示所有实体和填充效果
- 组列表：显示所有实体组及其状态

## 文件说明

### 核心程序文件
- `main_enhanced_with_v2_fill.py` - 主程序入口（V2墙体填充集成版）
- `main_enhanced.py` - 增强版主程序
- `wall_fill_processor_enhanced_v2.py` - V2增强版墙体填充处理器
- `interactive_wall_fill_window.py` - 交互式填充窗口
- `cad_data_processor.py` - CAD数据处理核心
- `cad_visualizer.py` - 可视化组件

### 启动脚本
- `启动V2版本.bat` - 启动V2版本主程序
- `启动交互式填充.bat` - 启动交互式填充功能
- `启动增强版V2.bat` - 启动增强版V2
- `环境诊断.bat` - 环境诊断工具

### 配置文件
- `requirements.txt` - Python依赖包列表

## 安装和使用

### 1. 环境要求
- Python 3.7+
- Windows操作系统

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
双击以下任一启动脚本：
- `启动V2版本.bat` - 推荐使用
- `启动增强版V2.bat`
- `启动交互式填充.bat`

### 4. 环境诊断
如果遇到问题，运行 `环境诊断.bat` 检查环境配置。

## 使用流程

### 1. 加载CAD文件
- 点击"选择文件夹"选择包含DXF文件的文件夹
- 程序会自动扫描并显示可处理的文件

### 2. 开始处理
- 点击"开始处理"按钮
- 程序会自动进行实体识别、分组和标注

### 3. 墙体填充（可选）
- 点击"墙体自动填充"进行自动填充
- 或选择交互式填充进行逐步控制
- 点击"保存填充"将填充结果应用到全图

### 4. 手动标注
- 在实体组列表中查看未标注的组
- 使用分类按钮进行手动标注
- 支持跳过和重新分类

### 5. 保存结果
- 点击"手动保存数据集"保存标注结果
- 可选择同时保存分类图片

## 状态说明

### 实体组状态
- **自动标注** (蓝色) - 程序自动识别的组
- **已标注** (绿色) - 手动标注完成的组
- **标注中** (红色) - 当前正在处理的组
- **待处理** (棕色) - 等待手动处理的组
- **未标注** (红色) - 尚未处理的组
- **重新标注** (紫色) - 重新分类的组

### 墙体填充状态
- **填充区域** - 墙体填充的实体区域
- **空腔区域** - 墙体内部的空腔区域

## 快捷键

- `Ctrl+O` - 选择文件夹
- `Ctrl+S` - 保存数据集
- `Ctrl+Q` - 退出程序

## 故障排除

### 常见问题

1. **程序无法启动**
   - 运行 `环境诊断.bat` 检查环境
   - 确保已安装所有依赖包

2. **CAD文件无法读取**
   - 检查文件格式是否为DXF
   - 确保文件路径不包含特殊字符

3. **墙体填充失败**
   - 检查墙体线条是否形成封闭区域
   - 尝试使用交互式填充模式

4. **可视化显示异常**
   - 检查matplotlib后端配置
   - 确保显示驱动正常

### 技术支持

如遇到其他问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 系统显示设置是否正常
4. 文件路径是否包含中文字符

## 版本历史

### V4 (当前版本)
- 精简版本，只包含运行必需文件
- 修复"标注中"状态显示问题
- 优化坐标验证逻辑
- 改进UI响应性

### V3
- 完整版本，包含所有开发文件
- 包含测试脚本和修复报告

### V2
- 增强版墙体填充
- 交互式填充功能
- 空腔识别功能

### V1
- 基础版本
- 基本CAD处理和分类功能

## 许可证

本软件仅供学习和研究使用。 