#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试组列表显示修复
"""

import sys
import os

def test_v2_group_list_inheritance():
    """测试V2版本的组列表继承"""
    try:
        print("测试V2版本的组列表继承:")
        
        # 检查V2版本是否正确继承了父类的组列表功能
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复
        checks = [
            ("调用父类组列表创建", "super()._create_group_list(parent)"),
            ("调用父类组列表更新", "super().update_group_list()"),
            ("移除自定义Canvas", "group_canvas" not in content),
            ("移除自定义滚动框架", "scrollable_frame" not in content),
            ("移除自定义组行", "_create_group_row" not in content),
            ("移除自定义填充按钮", "_create_fill_button" not in content),
            ("保留V2类定义", "class EnhancedCADAppV2"),
            ("保留填充状态管理", "group_fill_status")
        ]
        
        for check_name, pattern in checks:
            if isinstance(pattern, bool):
                # 对于布尔检查
                if pattern:
                    print(f"✓ {check_name}: 已修复")
                else:
                    print(f"✗ {check_name}: 仍存在问题")
                    return False
            else:
                # 对于字符串模式检查
                if pattern in content:
                    print(f"✓ {check_name}: 已实现")
                else:
                    print(f"✗ {check_name}: 未找到")
                    return False
        
        print("✓ V2版本组列表继承检查通过")
        return True
        
    except Exception as e:
        print(f"✗ V2版本组列表继承测试失败: {e}")
        return False

def test_parent_class_fill_button_improvements():
    """测试父类的填充按钮改进"""
    try:
        print("测试父类的填充按钮改进:")
        
        # 检查父类是否包含了填充按钮的改进
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("填充状态符号", "●填充"),
            ("可填充符号", "□填充"),
            ("待标注符号", "▢填充"),
            ("颜色符号获取", "_get_color_symbol"),
            ("填充列颜色设置", "_set_fill_column_color"),
            ("填充标签获取", "_get_fill_tag"),
            ("Treeview结构保持", "ttk.Treeview"),
            ("填充列定义", "'填充'"),
            ("组列表更新", "def update_group_list"),
            ("点击事件处理", "on_group_click")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 父类填充按钮改进检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 父类填充按钮改进测试失败: {e}")
        return False

def test_treeview_structure():
    """测试Treeview结构"""
    try:
        print("测试Treeview结构:")
        
        # 检查父类的Treeview结构
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_create_group_list方法
        group_list_start = content.find("def _create_group_list")
        if group_list_start == -1:
            print("✗ _create_group_list方法未找到")
            return False
        
        group_list_end = content.find("def ", group_list_start + 1)
        if group_list_end == -1:
            group_list_end = len(content)
        
        group_list_content = content[group_list_start:group_list_end]
        
        # 检查Treeview结构
        treeview_checks = [
            ("Treeview创建", "ttk.Treeview"),
            ("列定义", "columns ="),
            ("填充列", "'填充'"),
            ("列标题设置", "heading"),
            ("列宽设置", "column"),
            ("滚动条", "Scrollbar"),
            ("双击绑定", "bind('<Double-1>'"),
            ("单击绑定", "bind('<Button-1>'")
        ]
        
        for check_name, pattern in treeview_checks:
            if pattern in group_list_content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ Treeview结构检查通过")
        return True
        
    except Exception as e:
        print(f"✗ Treeview结构测试失败: {e}")
        return False

def test_fill_button_functionality():
    """测试填充按钮功能"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试填充按钮功能:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查填充相关方法
        fill_methods = [
            '_get_group_fill_status',
            '_get_fill_tag',
            '_set_fill_column_color',
            '_get_color_symbol',
            'fill_group',
            'on_group_click'
        ]
        
        for method in fill_methods:
            if hasattr(app, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                root.destroy()
                return False
        
        # 检查Treeview是否存在
        if hasattr(app, 'group_tree'):
            print("✓ group_tree (Treeview) 存在")
        else:
            print("✗ group_tree (Treeview) 不存在")
            root.destroy()
            return False
        
        root.destroy()
        print("✓ 填充按钮功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 填充按钮功能测试失败: {e}")
        return False

def test_v2_app_functionality():
    """测试V2应用功能"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        print("测试V2应用功能:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 检查继承的方法
        inherited_methods = [
            '_create_group_list',
            'update_group_list',
            '_get_group_fill_status',
            'fill_group',
            'on_group_click'
        ]
        
        for method in inherited_methods:
            if hasattr(app, method):
                print(f"✓ 继承方法 {method} 存在")
            else:
                print(f"✗ 继承方法 {method} 不存在")
                root.destroy()
                return False
        
        # 检查V2特有功能
        v2_methods = [
            'auto_fill_walls',
            '_start_interactive_fill'
        ]
        
        for method in v2_methods:
            if hasattr(app, method):
                print(f"✓ V2方法 {method} 存在")
            else:
                print(f"✗ V2方法 {method} 不存在")
                root.destroy()
                return False
        
        # 检查填充状态管理
        if hasattr(app, 'group_fill_status'):
            print("✓ 填充状态管理存在")
        else:
            print("✗ 填充状态管理不存在")
            root.destroy()
            return False
        
        root.destroy()
        print("✓ V2应用功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ V2应用功能测试失败: {e}")
        return False

def test_code_syntax():
    """测试代码语法"""
    try:
        print("测试代码语法:")
        
        # 测试父类语法
        import py_compile
        try:
            py_compile.compile('main_enhanced.py', doraise=True)
            print("✓ main_enhanced.py 语法正确")
        except py_compile.PyCompileError as e:
            print(f"✗ main_enhanced.py 语法错误: {e}")
            return False
        
        # 测试V2版本语法
        try:
            py_compile.compile('main_enhanced_with_v2_fill.py', doraise=True)
            print("✓ main_enhanced_with_v2_fill.py 语法正确")
        except py_compile.PyCompileError as e:
            print(f"✗ main_enhanced_with_v2_fill.py 语法错误: {e}")
            return False
        
        print("✓ 代码语法检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 代码语法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试组列表显示修复...")
    print("=" * 60)
    
    tests = [
        ("V2版本组列表继承", test_v2_group_list_inheritance),
        ("父类填充按钮改进", test_parent_class_fill_button_improvements),
        ("Treeview结构", test_treeview_structure),
        ("填充按钮功能", test_fill_button_functionality),
        ("V2应用功能", test_v2_app_functionality),
        ("代码语法", test_code_syntax)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 组列表显示修复测试全部通过！")
        print("\n📋 修复总结:")
        print("✅ 恢复了正确的Treeview组列表显示")
        print("✅ 保留了填充按钮的符号改进")
        print("✅ V2版本正确继承了父类功能")
        print("✅ 移除了导致灰色显示的自定义UI")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
