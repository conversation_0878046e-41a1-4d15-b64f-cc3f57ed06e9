#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
显示控制模块测试脚本
测试显示控制器的各项功能
"""

import sys
import time
from datetime import datetime

# 导入显示控制模块
try:
    from display_controller import DisplayController, DataChangeType, DataChangeEvent
    from display_integration import DisplayIntegrationHelper, BatchDisplayMonitor
    print("✅ 显示控制模块导入成功")
except ImportError as e:
    print(f"❌ 显示控制模块导入失败: {e}")
    sys.exit(1)


class MockVisualizer:
    """模拟可视化器"""
    
    def __init__(self):
        self.current_color_scheme = {}
        self.update_count = 0
    
    def visualize_overview(self, **kwargs):
        """模拟全图预览更新"""
        self.update_count += 1
        print(f"🎨 模拟可视化器：全图预览更新 #{self.update_count}")
        print(f"   参数: {list(kwargs.keys())}")
    
    def visualize_entity_group(self, group_data, category_mapping=None):
        """模拟组详细视图更新"""
        self.update_count += 1
        print(f"🔍 模拟可视化器：组详细视图更新 #{self.update_count}")
        print(f"   组数据: {len(group_data) if group_data else 0} 个实体")


class MockCanvas:
    """模拟画布"""
    
    def __init__(self):
        self.draw_count = 0
    
    def draw(self):
        """模拟画布绘制"""
        self.draw_count += 1
        print(f"🖼️ 模拟画布：绘制 #{self.draw_count}")


class MockColorSystem:
    """模拟配色系统"""
    
    def __init__(self):
        self.current_color_scheme = {
            'wall': '#FFA500',
            'door': '#42A5F5',
            'window': '#29B6F6',
            'other': '#BDBDBD'
        }
    
    def get_category_color(self, category):
        """获取类别颜色"""
        return self.current_color_scheme.get(category, '#808080')


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试1: 基本功能测试")
    print("=" * 50)
    
    # 创建模拟组件
    visualizer = MockVisualizer()
    canvas = MockCanvas()
    color_system = MockColorSystem()
    
    # 创建显示控制器
    controller = DisplayController(visualizer, canvas, color_system)
    
    # 测试数据变化通知
    test_data = [
        {'type': 'LINE', 'layer': 'WALL'},
        {'type': 'CIRCLE', 'layer': 'DOOR'}
    ]
    
    controller.notify_data_change(
        DataChangeType.DXF_FILE_DATA,
        test_data,
        "test_basic_functionality"
    )
    
    # 等待批量更新完成
    time.sleep(0.2)
    
    # 检查结果
    stats = controller.get_display_stats()
    print(f"📊 显示统计: {stats}")
    
    assert stats['update_count'] > 0, "应该有更新发生"
    assert visualizer.update_count > 0, "可视化器应该被调用"
    assert canvas.draw_count > 0, "画布应该被刷新"
    
    print("✅ 基本功能测试通过")


def test_color_scheme_change():
    """测试配色方案变化"""
    print("\n🧪 测试2: 配色方案变化测试")
    print("=" * 50)
    
    # 创建显示控制器
    controller = DisplayController()
    controller.enable_debug_mode(True)
    
    # 测试配色方案变化
    new_scheme = {
        'wall': '#FF0000',
        'door': '#00FF00',
        'window': '#0000FF'
    }
    
    controller.notify_data_change(
        DataChangeType.COLOR_SCHEME,
        new_scheme,
        "test_color_scheme_change"
    )
    
    # 测试重复的配色方案（应该被跳过）
    controller.notify_data_change(
        DataChangeType.COLOR_SCHEME,
        new_scheme,
        "test_color_scheme_change_duplicate"
    )
    
    time.sleep(0.2)
    
    stats = controller.get_display_stats()
    print(f"📊 显示统计: {stats}")
    
    print("✅ 配色方案变化测试通过")


def test_batch_updates():
    """测试批量更新"""
    print("\n🧪 测试3: 批量更新测试")
    print("=" * 50)
    
    visualizer = MockVisualizer()
    canvas = MockCanvas()
    controller = DisplayController(visualizer, canvas)
    
    # 使用批量监控
    with BatchDisplayMonitor(controller) as monitor:
        # 连续发送多个变化通知
        for i in range(5):
            monitor.notify_change(
                DataChangeType.GROUP_DATA,
                f"group_data_{i}",
                f"test_batch_updates_{i}"
            )
    
    # 检查批量更新结果
    stats = controller.get_display_stats()
    print(f"📊 显示统计: {stats}")

    # 批量更新应该只触发一次显示更新周期（可能包含全图预览和详细视图）
    assert stats['update_count'] == 1, f"批量更新应该只触发一次更新周期，实际: {stats['update_count']}"
    # 可视化器可能被调用多次（全图预览 + 详细视图），但应该是合理的次数
    assert visualizer.update_count <= 3, f"可视化器调用次数应该合理，实际: {visualizer.update_count}"
    
    print("✅ 批量更新测试通过")


def test_integration_helper():
    """测试集成辅助功能"""
    print("\n🧪 测试4: 集成辅助功能测试")
    print("=" * 50)
    
    # 创建模拟应用
    class MockApp:
        def __init__(self):
            self.visualizer = MockVisualizer()
            self.canvas = MockCanvas()
            self.current_color_scheme = {'wall': '#000000'}
    
    app = MockApp()
    
    # 使用集成辅助创建控制器
    controller = DisplayIntegrationHelper.create_integrated_controller(app)
    
    # 测试控制器是否正确设置
    assert controller.visualizer is not None, "可视化器应该被设置"
    assert controller.canvas is not None, "画布应该被设置"
    assert controller.color_system is not None, "配色系统应该被设置"
    
    print("✅ 集成辅助功能测试通过")


def test_change_detection():
    """测试变化检测"""
    print("\n🧪 测试5: 变化检测测试")
    print("=" * 50)
    
    controller = DisplayController()
    controller.enable_debug_mode(True)
    
    # 测试相同数据的重复通知（应该被跳过）
    test_data = [{'type': 'LINE', 'layer': 'WALL'}]
    
    # 第一次通知
    controller.notify_data_change(
        DataChangeType.GROUP_DATA,
        test_data,
        "test_change_detection_1"
    )
    
    # 第二次通知相同数据（应该被跳过）
    controller.notify_data_change(
        DataChangeType.GROUP_DATA,
        test_data,
        "test_change_detection_2"
    )
    
    # 第三次通知不同数据
    test_data_2 = [{'type': 'CIRCLE', 'layer': 'DOOR'}]
    controller.notify_data_change(
        DataChangeType.GROUP_DATA,
        test_data_2,
        "test_change_detection_3"
    )
    
    time.sleep(0.2)
    
    stats = controller.get_display_stats()
    print(f"📊 显示统计: {stats}")
    
    print("✅ 变化检测测试通过")


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试6: 错误处理测试")
    print("=" * 50)
    
    # 创建有问题的模拟组件
    class BrokenVisualizer:
        def visualize_overview(self, **kwargs):
            raise Exception("模拟可视化器错误")
    
    controller = DisplayController(BrokenVisualizer())
    controller.enable_debug_mode(True)
    
    # 发送通知，应该能处理错误而不崩溃
    controller.notify_data_change(
        DataChangeType.DXF_FILE_DATA,
        [{'type': 'LINE'}],
        "test_error_handling"
    )
    
    time.sleep(0.2)
    
    stats = controller.get_display_stats()
    print(f"📊 显示统计: {stats}")
    
    print("✅ 错误处理测试通过")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始显示控制模块测试")
    print("=" * 60)
    
    try:
        test_basic_functionality()
        test_color_scheme_change()
        test_batch_updates()
        test_integration_helper()
        test_change_detection()
        test_error_handling()
        
        print("\n🎉 所有测试通过！")
        print("=" * 60)
        print("✅ 显示控制模块功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
