#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试layer类型错误修复
验证：
1. 安全的layer处理方法
2. 中文字体配置
3. 墙体识别功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_layer_fix():
    """测试layer类型错误修复"""
    print("=" * 80)
    print("🧪 测试layer类型错误修复")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        print("✅ 成功导入墙体填充处理器")
        
        # 创建处理器实例
        processor = EnhancedWallFillProcessorV2()
        
        # 检查安全方法是否存在
        if hasattr(processor, '_safe_get_layer_name'):
            print("✅ _safe_get_layer_name 方法存在")
        else:
            print("❌ _safe_get_layer_name 方法缺失")
            return False
        
        # 测试不同类型的layer值
        test_cases = [
            # (entity, expected_result, description)
            ({'layer': 'WALL'}, 'wall', '字符串layer'),
            ({'layer': 123}, '123', '整数layer'),
            ({'layer': 45.67}, '45.67', '浮点数layer'),
            ({'layer': None}, '', 'None layer'),
            ({'layer': ''}, '', '空字符串layer'),
            ({}, '', '缺失layer'),
        ]
        
        print("\n📋 测试不同类型的layer值:")
        for entity, expected, description in test_cases:
            try:
                result = processor._safe_get_layer_name(entity)
                if expected in result.lower():
                    print(f"  ✅ {description}: {entity.get('layer', 'missing')} -> '{result}'")
                else:
                    print(f"  ❌ {description}: 期望包含 '{expected}', 实际 '{result}'")
                    return False
            except Exception as e:
                print(f"  ❌ {description}: 异常 {e}")
                return False
        
        # 测试墙体实体识别
        print("\n🔧 测试墙体实体识别...")
        test_entities = [
            {'type': 'LINE', 'points': [(0, 0), (100, 0)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 0), (100, 100)], 'layer': 123},  # 整数layer
            {'type': 'LINE', 'points': [(100, 100), (0, 100)], 'layer': 45.67},  # 浮点layer
            {'type': 'LINE', 'points': [(0, 100), (0, 0)], 'layer': None},  # None layer
        ]
        
        try:
            wall_entities = processor.identify_wall_entities(test_entities)
            print(f"✅ 成功识别墙体实体: {len(wall_entities)} 个")
            
            if len(wall_entities) > 0:
                print("✅ 墙体实体识别功能正常")
            else:
                print("⚠️ 未识别到墙体实体（可能是正常的，因为测试数据layer不匹配）")
                
        except Exception as e:
            print(f"❌ 墙体实体识别失败: {e}")
            return False
        
        print("\n✅ layer类型错误修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chinese_font_fix():
    """测试中文字体修复"""
    print("\n" + "=" * 80)
    print("🎨 测试中文字体修复")
    print("=" * 80)
    
    try:
        import tkinter as tk
        from interactive_wall_fill_window import InteractiveWallFillWindow
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        print("✅ 成功导入交互式窗口模块")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        # 创建测试数据
        test_entities = [
            {'type': 'LINE', 'points': [(0, 0), (100, 0)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 0), (100, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 100), (0, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(0, 100), (0, 0)], 'layer': 'WALL'},
        ]
        
        processor = EnhancedWallFillProcessorV2()
        
        print("🔧 测试中文字体配置...")
        
        # 创建交互式窗口实例（但不显示）
        window = InteractiveWallFillWindow(root, test_entities, processor)
        
        # 检查字体配置
        if hasattr(window, 'chinese_font'):
            print(f"✅ 中文字体配置成功: {window.chinese_font}")
        else:
            print("❌ 中文字体配置失败")
            root.destroy()
            return False
        
        # 检查字体配置方法
        if hasattr(window, '_setup_chinese_font'):
            print("✅ _setup_chinese_font 方法存在")
        else:
            print("❌ _setup_chinese_font 方法缺失")
            root.destroy()
            return False
        
        # 测试matplotlib字体设置
        import matplotlib.pyplot as plt
        current_font = plt.rcParams.get('font.sans-serif', [])
        print(f"✅ matplotlib字体设置: {current_font}")
        
        root.destroy()
        print("✅ 中文字体修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 中文字体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始layer类型错误修复测试...")
    
    # 执行所有测试
    tests = [
        ("Layer类型处理测试", test_layer_fix),
        ("中文字体修复测试", test_chinese_font_fix)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！layer类型错误已成功修复。")
        print("\n📝 修复内容:")
        print("1. ✅ 添加 _safe_get_layer_name 方法处理混合数据类型")
        print("2. ✅ 修复 wall_fill_processor_enhanced_v2.py 中的layer访问")
        print("3. ✅ 配置中文字体支持，减少字体警告")
        print("4. ✅ 保持识别逻辑不变，只修复类型错误")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    print("=" * 80)
