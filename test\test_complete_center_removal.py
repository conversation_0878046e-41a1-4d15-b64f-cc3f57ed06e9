#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试圆弧椭圆中心点移除
"""

import sys
import os
import math

def test_all_center_point_removals():
    """测试所有中心点移除"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试所有中心点移除:")
        
        processor = CADDataProcessor()
        
        # 创建测试实体
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,
            'layer': 'test'
        }
        
        test_circle = {
            'type': 'CIRCLE',
            'center': (200, 200),
            'radius': 30,
            'layer': 'test'
        }
        
        test_ellipse = {
            'type': 'ELLIPSE',
            'center': (300, 300),
            'major_axis': (40, 0),
            'ratio': 0.5,
            'layer': 'test'
        }
        
        test_line = {
            'type': 'LINE',
            'points': [(400, 400), (500, 500)],
            'layer': 'test'
        }
        
        test_point = (150, 150)
        
        # 测试1: 实体中心点获取（应该返回None）
        print("\n1. 测试实体中心点获取:")
        for entity_name, entity in [('圆弧', test_arc), ('圆形', test_circle), ('椭圆', test_ellipse)]:
            center = processor._get_entity_center(entity)
            if center is None:
                print(f"✓ {entity_name}中心点获取: None (正确)")
            else:
                print(f"✗ {entity_name}中心点获取: {center} (错误)")
                return False
        
        # 测试2: 快速距离检查（应该使用端点方法）
        print("\n2. 测试快速距离检查:")
        quick_distances = [
            ('圆弧-直线', processor._quick_distance_check(test_arc, test_line)),
            ('圆形-直线', processor._quick_distance_check(test_circle, test_line)),
            ('椭圆-直线', processor._quick_distance_check(test_ellipse, test_line))
        ]
        
        for name, distance in quick_distances:
            if isinstance(distance, (int, float)) and distance > 0:
                print(f"✓ {name}快速距离: {distance:.2f} (使用端点方法)")
            else:
                print(f"✗ {name}快速距离: {distance} (可能仍使用中心点)")
                return False
        
        # 测试3: 圆形到圆形距离（应该使用采样点）
        print("\n3. 测试圆形到圆形距离:")
        circle_distance = processor._circle_to_circle_distance(test_circle, test_circle)
        if circle_distance == 0.0:
            print(f"✓ 圆形到圆形距离: {circle_distance} (使用采样点)")
        else:
            print(f"✗ 圆形到圆形距离: {circle_distance} (可能有问题)")
            return False
        
        # 测试4: 圆弧相交检测（应该使用采样点）
        print("\n4. 测试圆弧相交检测:")
        intersects = processor._arc_intersects_line(test_arc, test_line)
        print(f"✓ 圆弧相交检测: {intersects} (使用采样点方法)")
        
        # 测试5: 点到圆形距离（应该使用采样点）
        print("\n5. 测试点到圆形距离:")
        point_circle_distance = processor._point_to_circle_distance(test_point, test_circle)
        if isinstance(point_circle_distance, (int, float)) and point_circle_distance > 0:
            print(f"✓ 点到圆形距离: {point_circle_distance:.2f} (使用采样点)")
        else:
            print(f"✗ 点到圆形距离: {point_circle_distance} (可能有问题)")
            return False
        
        # 测试6: 点到圆弧距离（应该使用采样点）
        print("\n6. 测试点到圆弧距离:")
        point_arc_distance = processor._point_to_arc_distance(test_point, test_arc)
        if isinstance(point_arc_distance, (int, float)) and point_arc_distance > 0:
            print(f"✓ 点到圆弧距离: {point_arc_distance:.2f} (使用采样点)")
        else:
            print(f"✗ 点到圆弧距离: {point_arc_distance} (可能有问题)")
            return False
        
        # 测试7: 椭圆到圆形距离（应该使用采样点）
        print("\n7. 测试椭圆到圆形距离:")
        ellipse_circle_distance = processor._ellipse_to_circle_distance(test_ellipse, test_circle)
        if isinstance(ellipse_circle_distance, (int, float)) and ellipse_circle_distance > 0:
            print(f"✓ 椭圆到圆形距离: {ellipse_circle_distance:.2f} (使用采样点)")
        else:
            print(f"✗ 椭圆到圆形距离: {ellipse_circle_distance} (可能有问题)")
            return False
        
        # 测试8: 椭圆到椭圆距离（应该不使用中心点回退）
        print("\n8. 测试椭圆到椭圆距离:")
        ellipse_ellipse_distance = processor._ellipse_to_ellipse_distance(test_ellipse, test_ellipse)
        if ellipse_ellipse_distance == 0.0:
            print(f"✓ 椭圆到椭圆距离: {ellipse_ellipse_distance} (使用采样点)")
        else:
            print(f"✗ 椭圆到椭圆距离: {ellipse_ellipse_distance} (可能有问题)")
            return False
        
        print("\n✓ 所有中心点移除测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 中心点移除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sampling_methods():
    """测试采样点方法"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试采样点方法:")
        
        processor = CADDataProcessor()
        
        # 测试圆形采样点
        test_circle = {
            'type': 'CIRCLE',
            'center': (100, 100),
            'radius': 50,
            'layer': 'test'
        }
        
        circle_points = processor._sample_circle_points(test_circle, num_points=8)
        print(f"✓ 圆形采样点数量: {len(circle_points)}")
        
        # 验证采样点在圆周上
        center = test_circle['center']
        radius = test_circle['radius']
        for i, point in enumerate(circle_points[:3]):  # 检查前3个点
            distance_to_center = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
            if abs(distance_to_center - radius) < 0.01:
                print(f"✓ 圆形采样点{i+1}: {point} 距离圆心 {distance_to_center:.2f}")
            else:
                print(f"✗ 圆形采样点{i+1}: {point} 距离圆心 {distance_to_center:.2f} (应该是{radius})")
                return False
        
        # 测试圆弧采样点
        test_arc = {
            'type': 'ARC',
            'center': (200, 200),
            'radius': 30,
            'start_angle': 0,
            'end_angle': math.pi/2,  # 90度
            'layer': 'test'
        }
        
        arc_points = processor._sample_arc_points(test_arc, num_points=8)
        print(f"✓ 圆弧采样点数量: {len(arc_points)}")
        
        # 验证采样点在圆弧上
        center = test_arc['center']
        radius = test_arc['radius']
        for i, point in enumerate(arc_points[:3]):  # 检查前3个点
            distance_to_center = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
            if abs(distance_to_center - radius) < 0.01:
                print(f"✓ 圆弧采样点{i+1}: {point} 距离圆心 {distance_to_center:.2f}")
            else:
                print(f"✗ 圆弧采样点{i+1}: {point} 距离圆心 {distance_to_center:.2f} (应该是{radius})")
                return False
        
        # 测试椭圆采样点
        test_ellipse = {
            'type': 'ELLIPSE',
            'center': (300, 300),
            'major_axis': (40, 0),
            'ratio': 0.5,
            'layer': 'test'
        }
        
        ellipse_points = processor._sample_ellipse_points(test_ellipse, num_points=16)
        print(f"✓ 椭圆采样点数量: {len(ellipse_points)}")
        
        print("✓ 采样点方法测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 采样点方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_grouping_without_centers():
    """测试不使用中心点的分组"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试不使用中心点的分组:")
        
        processor = CADDataProcessor()
        
        # 创建测试场景：圆弧、圆形、椭圆和直线
        test_entities = [
            # 圆弧
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 50,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'test'
            },
            # 接触圆弧的直线
            {
                'type': 'LINE',
                'points': [(150, 100), (200, 100)],  # 从圆弧端点延伸
                'layer': 'test'
            },
            # 圆形
            {
                'type': 'CIRCLE',
                'center': (300, 300),
                'radius': 30,
                'layer': 'test'
            },
            # 接触圆形的直线
            {
                'type': 'LINE',
                'points': [(330, 300), (380, 300)],  # 从圆形边缘延伸
                'layer': 'test'
            },
            # 椭圆
            {
                'type': 'ELLIPSE',
                'center': (500, 500),
                'major_axis': (40, 0),
                'ratio': 0.5,
                'layer': 'test'
            },
            # 远离的直线（不应该被分组）
            {
                'type': 'LINE',
                'points': [(1000, 1000), (1100, 1100)],
                'layer': 'test'
            }
        ]
        
        # 执行分组
        groups = processor.group_entities(test_entities, distance_threshold=20, debug=False)
        
        print(f"分组结果: {len(groups)} 个组")
        for i, group in enumerate(groups):
            print(f"  组 {i+1}: {len(group)} 个实体")
            for entity in group:
                if entity['type'] == 'ARC':
                    print(f"    圆弧: 中心{entity['center']}")
                elif entity['type'] == 'CIRCLE':
                    print(f"    圆形: 中心{entity['center']}")
                elif entity['type'] == 'ELLIPSE':
                    print(f"    椭圆: 中心{entity['center']}")
                elif entity['type'] == 'LINE':
                    print(f"    直线: {entity['points']}")
        
        # 验证分组结果
        if len(groups) >= 3:
            # 应该至少有3个组：圆弧+直线、圆形+直线、椭圆、远离直线
            print("✓ 分组正确，使用采样点而非中心点")
            return True
        else:
            print("✗ 分组可能仍在使用中心点")
            return False
        
    except Exception as e:
        print(f"✗ 分组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始全面测试圆弧椭圆中心点移除...")
    print("=" * 60)
    
    tests = [
        ("所有中心点移除", test_all_center_point_removals),
        ("采样点方法", test_sampling_methods),
        ("不使用中心点的分组", test_grouping_without_centers)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧椭圆中心点完全移除测试全部通过！")
        print("\n📋 完整修改总结:")
        print("✅ _get_entity_center: 圆弧椭圆返回None")
        print("✅ _quick_distance_check: 使用端点而非中心点")
        print("✅ _circle_to_circle_distance: 使用采样点")
        print("✅ _arc_intersects_line: 使用采样点")
        print("✅ _point_to_circle_distance: 使用采样点")
        print("✅ _point_to_arc_distance: 使用采样点")
        print("✅ _ellipse_to_circle_distance: 不使用圆心")
        print("✅ _ellipse_to_ellipse_distance: 不使用中心点回退")
        print("✅ 最后回退逻辑: 圆弧椭圆不使用中心点")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
