#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一墙体填充处理器
实现自动填充和手动填充的统一逻辑：
1. 输入范围均为所有自动标注或者手动标注的墙体组
2. 识别依据为每个组内的所有实体
3. 容错处理：使用手动填充的方式
4. 空腔检测：读取所有组的信息，识别是否有组的轮廓完全在另外一个组的轮廓内
"""

import numpy as np
from shapely.geometry import Polygon, LineString, Point
from shapely.ops import unary_union, polygonize
from shapely.geometry import MultiLineString

class UnifiedWallFillProcessor:
    """统一墙体填充处理器"""
    
    def __init__(self):
        # 墙体图层模式（与V2处理器保持一致）
        self.wall_layer_patterns = [
            '墙', '墙体', '结构墙', '承重墙', '隔墙', '内墙', '外墙', '分隔墙', '主墙', '次墙',
            'wall', 'walls', 'partition', 'struct', 'structure', 'bearing', 'load',
            'exterior', 'interior', 'divider', 'separator',
            'A-WALL', 'AWALL', 'A_WALL', 'WALL_', '_WALL', 'STR-WALL', 'ARCH-WALL',
            'PARTITION', 'STRUCT-WALL', 'BEARING-WALL', 'LOAD-WALL',
            'masonry', 'concrete', 'brick', '砖墙', '混凝土', '砌体',
            '01-墙', '02-墙', 'L-墙', 'S-墙', 'A-墙', 'AR-墙'
        ]
        
        # 处理参数
        self.connection_threshold = 20  # 连接阈值
        self.min_polygon_area = 10.0   # 最小多边形面积
        self.tolerance = 1.0           # 容差
    
    def process_unified_wall_filling(self, entities):
        """统一的墙体填充处理流程"""
        try:
            print("开始统一墙体填充处理...")
            
            # 1. 统一的墙体实体识别
            wall_entities = self.identify_wall_entities(entities)
            
            if not wall_entities:
                print("未找到墙体实体")
                return []
            
            print(f"识别到 {len(wall_entities)} 个墙体实体")
            
            # 2. 统一的墙体分组
            wall_groups = self.group_wall_entities(wall_entities)
            
            if not wall_groups:
                print("未找到有效的墙体组")
                return []
            
            print(f"分组得到 {len(wall_groups)} 个墙体组")
            
            # 3. 统一的容错处理和填充
            filled_groups = []
            for i, group in enumerate(wall_groups):
                print(f"处理墙体组 {i+1}/{len(wall_groups)}, 实体数量: {len(group)}")
                
                # 使用统一的容错处理方式
                fill_result = self.process_single_group_unified(group, i)
                
                if fill_result:
                    filled_groups.append({
                        'wall_group': group,
                        'fill_polygons': [fill_result],
                        'cavities': [],
                        'group_index': i
                    })
                    print(f"  墙体组 {i+1} 填充成功")
                else:
                    print(f"  墙体组 {i+1} 填充失败")
            
            # 4. 统一的空腔检测
            filled_groups = self.detect_cavities_unified(filled_groups)
            
            print(f"最终创建了 {len(filled_groups)} 个填充组")
            return filled_groups
            
        except Exception as e:
            print(f"统一墙体填充处理失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _safe_get_layer_name(self, entity):
        """安全地获取实体的图层名称"""
        try:
            layer_raw = entity.get('layer', '')
            # 确保layer是字符串类型
            if isinstance(layer_raw, (int, float)):
                return str(layer_raw).lower()
            elif isinstance(layer_raw, str):
                return layer_raw.lower()
            else:
                return str(layer_raw).lower() if layer_raw else ''
        except Exception as e:
            print(f"⚠️ 获取图层名称失败: {e}")
            return ''

    def identify_wall_entities(self, entities):
        """统一的墙体实体识别"""
        wall_entities = []
        
        # 基于图层名称识别
        for entity in entities:
            layer = self._safe_get_layer_name(entity)
            if any(pattern.lower() in layer for pattern in self.wall_layer_patterns):
                wall_entities.append(entity)
        
        print(f"按图层识别到 {len(wall_entities)} 个墙体实体")
        
        # 如果图层识别的实体数量不足，启用几何关系识别
        if len(wall_entities) < 5:
            print("按图层识别的墙体实体较少，尝试基于几何关系识别...")
            geometric_walls = self._identify_walls_by_geometry(entities)
            if len(geometric_walls) > len(wall_entities):
                wall_entities = geometric_walls
                print(f"几何识别到 {len(wall_entities)} 个墙体实体")
        
        return wall_entities
    
    def _identify_walls_by_geometry(self, entities):
        """基于几何关系识别墙体实体"""
        wall_entities = []
        
        # 收集所有LINE类型的实体
        line_entities = [e for e in entities if e.get('type') == 'LINE']
        
        if len(line_entities) < 3:
            return wall_entities
        
        # 分析线段连接关系
        connected_groups = self._find_connected_line_groups(line_entities)
        
        # 选择最大的封闭组作为墙体
        for group in connected_groups:
            if len(group) >= 3:  # 至少3个线段才能形成封闭区域
                if self._is_closed_line_group(group):
                    wall_entities.extend(group)
                    break  # 选择第一个封闭的大组作为墙体
        
        return wall_entities
    
    def _find_connected_line_groups(self, line_entities):
        """查找连接的线段组"""
        groups = []
        used_entities = set()
        
        for i, entity in enumerate(line_entities):
            if i in used_entities:
                continue
            
            # 创建新组
            group = [entity]
            used_entities.add(i)
            
            # 查找连接的实体
            self._find_connected_entities_recursive(line_entities, entity, group, used_entities)
            
            if len(group) >= 2:
                groups.append(group)
        
        return groups
    
    def _find_connected_entities_recursive(self, all_entities, current_entity, group, used_entities):
        """递归查找连接的实体"""
        if 'points' not in current_entity or len(current_entity['points']) < 2:
            return
        
        current_points = current_entity['points']
        current_start = current_points[0]
        current_end = current_points[-1]
        
        for i, entity in enumerate(all_entities):
            if i in used_entities or 'points' not in entity or len(entity['points']) < 2:
                continue
            
            entity_points = entity['points']
            entity_start = entity_points[0]
            entity_end = entity_points[-1]
            
            # 检查是否连接
            if (self._points_close(current_start, entity_start, self.tolerance) or
                self._points_close(current_start, entity_end, self.tolerance) or
                self._points_close(current_end, entity_start, self.tolerance) or
                self._points_close(current_end, entity_end, self.tolerance)):
                
                group.append(entity)
                used_entities.add(i)
                # 递归查找更多连接的实体
                self._find_connected_entities_recursive(all_entities, entity, group, used_entities)
    
    def _points_close(self, p1, p2, tolerance):
        """检查两点是否接近"""
        try:
            return abs(p1[0] - p2[0]) <= tolerance and abs(p1[1] - p2[1]) <= tolerance
        except:
            return False
    
    def _is_closed_line_group(self, group):
        """检查线段组是否封闭"""
        if len(group) < 3:
            return False
        
        # 统计所有端点的连接次数
        endpoint_count = {}
        
        for entity in group:
            if 'points' in entity and len(entity['points']) >= 2:
                start_point = tuple(entity['points'][0])
                end_point = tuple(entity['points'][-1])
                
                endpoint_count[start_point] = endpoint_count.get(start_point, 0) + 1
                endpoint_count[end_point] = endpoint_count.get(end_point, 0) + 1
        
        # 封闭图形中，每个端点都应该连接偶数次
        for count in endpoint_count.values():
            if count % 2 != 0:
                return False
        
        return True
    
    def group_wall_entities(self, wall_entities):
        """统一的墙体实体分组"""
        if not wall_entities:
            return []
        
        groups = []
        used_entities = set()
        
        for i, entity in enumerate(wall_entities):
            if i in used_entities:
                continue
            
            # 创建新组
            group = [entity]
            used_entities.add(i)
            
            # 查找连接的实体
            self._find_connected_entities_recursive(wall_entities, entity, group, used_entities)
            
            if len(group) >= 2:  # 至少需要2个实体才能形成封闭区域
                groups.append(group)
        
        return groups
    
    def process_single_group_unified(self, group, group_index):
        """统一的单组填充处理"""
        try:
            # 提取线段
            segments = []
            for entity in group:
                if 'points' in entity and len(entity['points']) >= 2:
                    segments.append(entity['points'])
            
            if not segments:
                return None
            
            # 使用统一的填充逻辑（手动填充的方式）
            result = self._unified_fill_logic(segments)
            return result
            
        except Exception as e:
            print(f"组 {group_index+1} 填充处理失败: {e}")
            return None
    
    def _unified_fill_logic(self, segments):
        """统一的填充逻辑"""
        try:
            # 创建LineString对象
            lines = []
            for seg in segments:
                if len(seg) >= 2:
                    line = LineString(seg)
                    lines.append(line)
            
            if not lines:
                return None
            
            # 尝试构建多边形
            polygons = list(polygonize(lines))
            
            if polygons:
                # 选择最大的多边形
                polygons.sort(key=lambda p: p.area, reverse=True)
                largest_polygon = polygons[0]
                
                # 检查面积是否满足最小要求
                if largest_polygon.area >= self.min_polygon_area:
                    return largest_polygon
            
            return None
            
        except Exception as e:
            print(f"统一填充逻辑失败: {e}")
            return None

    def detect_cavities_unified(self, filled_groups):
        """统一的空腔检测（基于组间包含关系）"""
        try:
            print("开始统一空腔检测...")

            if len(filled_groups) < 2:
                print("组数量不足，无需检测空腔")
                return filled_groups

            # 检查每个组是否完全包含在另一个组内
            for i, group_i in enumerate(filled_groups):
                if not group_i['fill_polygons']:
                    continue

                polygon_i = group_i['fill_polygons'][0]
                if not polygon_i or not hasattr(polygon_i, 'area'):
                    continue

                for j, group_j in enumerate(filled_groups):
                    if i == j or not group_j['fill_polygons']:
                        continue

                    polygon_j = group_j['fill_polygons'][0]
                    if not polygon_j or not hasattr(polygon_j, 'area'):
                        continue

                    # 检查polygon_i是否完全在polygon_j内
                    try:
                        if polygon_j.contains(polygon_i):
                            # 检查面积比例，确保不是几乎相同的多边形
                            area_ratio = polygon_i.area / polygon_j.area
                            if area_ratio < 0.9:  # 面积小于外轮廓的90%
                                print(f"检测到空腔：组 {i+1} 完全在组 {j+1} 内，面积比例: {area_ratio:.2f}")

                                # 将组i标记为空腔
                                filled_groups[i] = {
                                    'wall_group': group_i['wall_group'],
                                    'fill_polygons': [],  # 空腔不填充
                                    'cavities': [polygon_i],  # 标记为空腔
                                    'is_cavity': True,
                                    'group_index': group_i.get('group_index', i)
                                }

                                # 从组j中扣除空腔区域
                                try:
                                    subtracted_polygon = polygon_j.difference(polygon_i)
                                    if not subtracted_polygon.is_empty:
                                        if subtracted_polygon.geom_type == 'Polygon':
                                            filled_groups[j]['fill_polygons'] = [subtracted_polygon]
                                        elif subtracted_polygon.geom_type == 'MultiPolygon':
                                            # 选择面积最大的多边形
                                            largest_poly = max(subtracted_polygon.geoms, key=lambda p: p.area)
                                            filled_groups[j]['fill_polygons'] = [largest_poly]
                                        print(f"  已从组 {j+1} 中扣除空腔区域")
                                    else:
                                        print(f"  警告：扣除空腔后组 {j+1} 为空")
                                except Exception as e:
                                    print(f"  扣除空腔时出错: {e}")

                                break  # 一个组只能是一个空腔
                    except Exception as e:
                        print(f"检查包含关系时出错: {e}")
                        continue

            # 统计结果
            cavity_count = sum(1 for group in filled_groups if group.get('is_cavity', False))
            fill_count = sum(1 for group in filled_groups if group['fill_polygons'])

            print(f"空腔检测完成：{cavity_count} 个空腔，{fill_count} 个填充区域")
            return filled_groups

        except Exception as e:
            print(f"统一空腔检测失败: {e}")
            import traceback
            traceback.print_exc()
            return filled_groups

    def is_closed_wall_group(self, group):
        """检查墙体组是否封闭"""
        return self._is_closed_line_group(group)
