#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI创建
"""

import sys
import os
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ui_creation():
    """测试UI创建"""
    print("=== 测试UI创建 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建应用
        root = tk.Tk()
        root.title("UI创建测试")
        root.geometry("800x600")
        
        print(f"📋 创建应用...")
        app = EnhancedCADAppV2(root)
        
        # 强制更新窗口
        root.update()
        
        print(f"  应用创建完成")
        print(f"  file_combo 存在: {hasattr(app, 'file_combo')}")
        print(f"  file_combo 值: {app.file_combo}")
        
        if hasattr(app, 'file_combo') and app.file_combo:
            print(f"  file_combo 类型: {type(app.file_combo)}")
            print(f"  file_combo 父控件: {app.file_combo.master}")
            
            # 测试设置值
            try:
                app.file_combo['values'] = ["测试1", "测试2", "测试3"]
                app.file_combo.set("测试1")
                print(f"  设置测试值成功")
                print(f"  当前值: {app.file_combo.get()}")
                print(f"  选项: {app.file_combo['values']}")
            except Exception as e:
                print(f"  设置测试值失败: {e}")
        else:
            print(f"  ❌ file_combo 不存在或为None")
            
            # 检查是否有其他相关属性
            print(f"  检查其他属性:")
            for attr in dir(app):
                if 'combo' in attr.lower() or 'file' in attr.lower():
                    value = getattr(app, attr)
                    if not callable(value):
                        print(f"    {attr}: {value}")
        
        # 显示窗口一段时间
        print(f"\n👀 显示窗口3秒...")
        
        def close_window():
            print("关闭窗口")
            root.quit()
        
        root.after(3000, close_window)
        root.mainloop()
        
        root.destroy()
        
        return app.file_combo is not None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_ui_creation():
    """测试手动UI创建"""
    print("\n=== 测试手动UI创建 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建应用
        root = tk.Tk()
        root.title("手动UI创建测试")
        root.geometry("400x300")
        
        print(f"📋 创建应用...")
        app = EnhancedCADAppV2(root)
        
        # 强制更新窗口
        root.update()
        
        print(f"  初始 file_combo: {app.file_combo}")
        
        # 如果file_combo不存在，手动创建
        if not app.file_combo:
            print(f"  手动创建file_combo...")
            
            # 创建一个简单的框架
            test_frame = tk.Frame(root)
            test_frame.pack(fill='x', padx=10, pady=10)
            
            tk.Label(test_frame, text="测试文件选择:").pack(anchor='w')
            
            # 创建下拉菜单
            from tkinter import ttk
            app.file_combo = ttk.Combobox(test_frame, state='readonly', width=30)
            app.file_combo.pack(fill='x', pady=5)
            
            print(f"  手动创建完成: {app.file_combo}")
            
            # 测试设置值
            app.file_combo['values'] = ["文件1.dxf (未处理, 未标注)", "文件2.dxf (处理中, 未标注)", "文件3.dxf (已完成, 标注完成)"]
            app.file_combo.set("文件1.dxf (未处理, 未标注)")
            
            print(f"  设置测试值完成")
            print(f"  当前值: {app.file_combo.get()}")
            print(f"  选项数: {len(app.file_combo['values'])}")
        
        # 显示窗口一段时间
        print(f"\n👀 显示窗口3秒...")
        
        def close_window():
            print("关闭窗口")
            root.quit()
        
        root.after(3000, close_window)
        root.mainloop()
        
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("UI创建测试")
    print("=" * 30)
    print("目标: 验证UI控件是否正确创建")
    print()
    
    tests = [
        ("UI创建", test_ui_creation),
        ("手动UI创建", test_manual_ui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 30)
    print(f"测试结果: {passed}/{total} 项通过")

if __name__ == "__main__":
    main()
