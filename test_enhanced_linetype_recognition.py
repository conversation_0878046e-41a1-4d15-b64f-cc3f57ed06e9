#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的虚线识别功能
验证门窗图层虚线是否能正确被识别和分组
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_linetype_recognition():
    """测试增强的虚线识别功能"""
    print("🧪 测试增强的虚线识别功能")
    print("=" * 80)
    
    # 导入处理器
    from cad_data_processor import CADDataProcessor
    
    # 创建处理器实例
    processor = CADDataProcessor()
    
    # 创建模拟的DXF文档和实体
    print("📋 创建测试数据...")
    
    # 模拟线型字典
    processor.linetype_dict = {
        'CONTINUOUS': None,
        'DASHED': None,
        'DASH': None,
        'DASHDOT': None,
        'DOT': None,
        'HIDDEN': None,
        'CENTER': None,
        'PHANTOM': None,
        'ACAD_ISO02W100': None,  # ISO虚线
        'ACAD_ISO03W100': None,  # ISO点划线
    }
    
    # 模拟图层线型映射
    processor.layer_linetype_mapping = {
        'A-WINDOW': {
            'linetype_name': 'DASHED',
            'linetype_entity': None,
            'is_dashed': True
        },
        'A-DOOR': {
            'linetype_name': 'DASH',
            'linetype_entity': None,
            'is_dashed': True
        },
        'WINDOW-DASH': {
            'linetype_name': 'DASHDOT',
            'linetype_entity': None,
            'is_dashed': True
        },
        'DOOR-ISO': {
            'linetype_name': 'ACAD_ISO02W100',
            'linetype_entity': None,
            'is_dashed': True
        },
        'WALL': {
            'linetype_name': 'CONTINUOUS',
            'linetype_entity': None,
            'is_dashed': False
        }
    }
    
    # 设置DXF版本和比例
    processor.dxf_version = "AC1015"  # AutoCAD 2000
    processor.global_ltscale = 1.0
    processor.psltscale = 1.0
    
    print("✅ 测试数据创建完成")
    
    # 测试用例
    test_cases = [
        {
            'name': '门窗图层虚线1',
            'layer': 'A-WINDOW',
            'entity_linetype': 'BYLAYER',
            'expected_effective': 'DASHED',
            'expected_dashed': True
        },
        {
            'name': '门窗图层虚线2',
            'layer': 'A-DOOR',
            'entity_linetype': 'BYLAYER',
            'expected_effective': 'DASH',
            'expected_dashed': True
        },
        {
            'name': '直接指定虚线',
            'layer': 'WALL',
            'entity_linetype': 'DASHDOT',
            'expected_effective': 'DASHDOT',
            'expected_dashed': True
        },
        {
            'name': 'ISO虚线类型',
            'layer': 'DOOR-ISO',
            'entity_linetype': 'BYLAYER',
            'expected_effective': 'ACAD_ISO02W100',
            'expected_dashed': True
        },
        {
            'name': '连续线（对照组）',
            'layer': 'WALL',
            'entity_linetype': 'CONTINUOUS',
            'expected_effective': 'CONTINUOUS',
            'expected_dashed': False
        }
    ]
    
    print(f"\n🔍 开始测试 {len(test_cases)} 个用例...")
    print("=" * 80)
    
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['name']}")
        print(f"   图层: {case['layer']}")
        print(f"   实体线型: {case['entity_linetype']}")
        
        # 创建模拟实体
        class MockEntity:
            def __init__(self, layer, linetype):
                self.dxf = MockDXF(layer, linetype)
        
        class MockDXF:
            def __init__(self, layer, linetype):
                self.layer = layer
                self.linetype = linetype
        
        mock_entity = MockEntity(case['layer'], case['entity_linetype'])
        
        # 测试线型信息提取
        try:
            linetype_info = processor._extract_enhanced_linetype_info(mock_entity)
            
            effective_linetype = linetype_info['linetype']
            is_dashed = linetype_info['is_dashed']
            
            print(f"   结果:")
            print(f"     有效线型: {effective_linetype}")
            print(f"     是否虚线: {is_dashed}")
            print(f"     线型比例: {linetype_info['linetype_scale']}")
            
            # 验证结果
            if (effective_linetype == case['expected_effective'] and 
                is_dashed == case['expected_dashed']):
                print(f"   ✅ 测试通过")
                success_count += 1
            else:
                print(f"   ❌ 测试失败")
                print(f"     期望有效线型: {case['expected_effective']}")
                print(f"     期望虚线状态: {case['expected_dashed']}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 测试总结:")
    print(f"   总用例数: {len(test_cases)}")
    print(f"   成功数: {success_count}")
    print(f"   失败数: {len(test_cases) - success_count}")
    print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
    
    if success_count == len(test_cases):
        print("🎉 所有测试用例通过！虚线识别功能正常")
    else:
        print("⚠️ 部分测试用例失败，需要进一步检查")

def test_door_window_grouping():
    """测试门窗虚线分组功能"""
    print("\n🧪 测试门窗虚线分组功能")
    print("=" * 80)
    
    # 创建包含虚线的门窗测试实体
    test_entities = [
        # 门窗图层的虚线实体
        {
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'linetype': 'DASHED',
            'is_dashed': True,
            'points': [[100, 100], [200, 100]]
        },
        {
            'type': 'LINE', 
            'layer': 'A-WINDOW',
            'linetype': 'DASHED',
            'is_dashed': True,
            'points': [[200, 100], [200, 150]]
        },
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'linetype': 'DASH',
            'is_dashed': True,
            'points': [[300, 100], [400, 100]]
        },
        # 墙体图层的连续线（对照组）
        {
            'type': 'LINE',
            'layer': 'WALL',
            'linetype': 'CONTINUOUS',
            'is_dashed': False,
            'points': [[0, 0], [100, 0]]
        }
    ]
    
    print(f"📋 创建测试实体: {len(test_entities)} 个")
    for i, entity in enumerate(test_entities):
        print(f"   实体 {i+1}: 图层={entity['layer']}, 线型={entity['linetype']}, 虚线={entity['is_dashed']}")
    
    # 导入处理器
    from cad_data_processor import CADDataProcessor
    processor = CADDataProcessor()
    
    # 测试门窗图层识别
    print(f"\n🔍 测试门窗图层识别...")
    door_window_layers = processor._detect_special_layers(
        test_entities, 
        processor.door_window_layer_patterns, 
        debug=True, 
        layer_type="门窗"
    )
    
    print(f"📊 识别结果:")
    print(f"   门窗图层: {door_window_layers}")
    
    # 检查虚线实体是否被正确识别
    door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
    dashed_door_window_entities = [e for e in door_window_entities if e.get('is_dashed', False)]
    
    print(f"   门窗实体总数: {len(door_window_entities)}")
    print(f"   门窗虚线实体数: {len(dashed_door_window_entities)}")
    
    if len(dashed_door_window_entities) > 0:
        print("✅ 门窗虚线实体被正确识别")
        for entity in dashed_door_window_entities:
            print(f"     - 图层: {entity['layer']}, 线型: {entity['linetype']}")
    else:
        print("❌ 门窗虚线实体未被识别")
    
    print("=" * 80)

def main():
    """主函数"""
    print("🚀 增强虚线识别功能测试")
    print("目标：修复门窗图层虚线未正确分组的问题")
    print("=" * 80)
    
    # 测试1：增强线型识别
    test_enhanced_linetype_recognition()
    
    # 测试2：门窗虚线分组
    test_door_window_grouping()
    
    print("\n🎯 测试完成")
    print("如果所有测试通过，说明虚线识别修复成功！")

if __name__ == "__main__":
    main()
