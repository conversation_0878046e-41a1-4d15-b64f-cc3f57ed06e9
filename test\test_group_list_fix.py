#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试组列表显示修复
"""

import tkinter as tk
from tkinter import ttk

def test_treeview_format():
    """测试TreeView格式"""
    root = tk.Tk()
    root.title("组列表格式测试")
    root.geometry("600x400")
    
    # 创建Treeview控件
    columns = ('状态', '类型', '实体数', '填充')
    tree = ttk.Treeview(root, columns=columns, show='tree headings', height=10)
    
    # 设置列标题
    tree.heading('#0', text='组ID', anchor='w')
    tree.heading('状态', text='状态', anchor='center')
    tree.heading('类型', text='类型', anchor='center')
    tree.heading('实体数', text='实体数', anchor='center')
    tree.heading('填充', text='填充', anchor='center')
    
    # 设置列宽
    tree.column('#0', width=60, minwidth=50)
    tree.column('状态', width=80, minwidth=60)
    tree.column('类型', width=100, minwidth=80)
    tree.column('实体数', width=60, minwidth=50)
    tree.column('填充', width=80, minwidth=60)
    
    # 模拟真实的groups_info数据结构
    groups_info = [
        {'status': 'auto_labeled', 'type': 'wall', 'entity_count': 28},
        {'status': 'auto_labeled', 'type': 'wall', 'entity_count': 11},
        {'status': 'auto_labeled', 'type': 'door_window', 'entity_count': 5},
        {'status': 'labeling', 'type': '', 'entity_count': 56},
    ]

    # 模拟类型映射
    category_mapping = {
        'wall': '墙体',
        'door_window': '门窗',
        '': '待标注'
    }

    # 模拟填充状态
    group_fill_status = {0: True}  # 第一个组已填充
    
    # 模拟获取填充状态的函数
    def get_fill_status(group_index):
        if group_index in group_fill_status and group_fill_status[group_index]:
            return "●填充"
        # 检查是否可以填充（已标注的组）
        group_info = groups_info[group_index]
        status = group_info['status']
        if status in ['labeled', 'auto_labeled', 'relabeled']:
            return "□填充"
        else:
            return "▢填充"

    # 插入测试数据 - 模拟真实的处理逻辑
    for i, group_info in enumerate(groups_info):
        group_id = f"组{i+1}"
        status = group_info['status']
        group_type = group_info.get('type', '')
        entity_count = group_info['entity_count']

        # 根据状态选择标签和显示文本（与父类保持一致）
        if status == 'auto_labeled':
            status_text = '自动标注'
            tag = 'auto_labeled'
            type_text = category_mapping.get(group_type, group_type)
        elif status == 'labeled':
            status_text = '已标注'
            tag = 'labeled'
            type_text = category_mapping.get(group_type, group_type)
        elif status == 'relabeled':
            status_text = '重新标注'
            tag = 'relabeled'
            type_text = category_mapping.get(group_type, group_type)
        elif status == 'pending':
            status_text = '待处理'
            tag = 'pending'
            type_text = '待标注'
        elif status == 'labeling':
            status_text = '标注中'
            tag = 'labeling'
            type_text = '待标注'
        else:
            status_text = '未标注'
            tag = 'unlabeled'
            type_text = '待标注'

        # 获取填充状态
        fill_status = get_fill_status(i)

        # 使用正确的格式：text=group_id, values=(status_text, type_text, entity_count, fill_status)
        item_id = tree.insert('', 'end', text=group_id,
                             values=(status_text, type_text, entity_count, fill_status),
                             tags=(tag,))

        print(f"插入组{i+1}: {group_id} | {status_text} | {type_text} | {entity_count} | {fill_status}")
    
    # 配置颜色标签（与主程序保持一致）
    tree.tag_configure('unlabeled', foreground='red')
    tree.tag_configure('labeling', foreground='red')
    tree.tag_configure('labeled', foreground='green')
    tree.tag_configure('auto_labeled', foreground='blue')
    tree.tag_configure('relabeled', foreground='purple')
    tree.tag_configure('pending', foreground='brown')
    
    # 布局
    tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    # 添加说明标签
    info_label = tk.Label(root, text="测试组列表显示格式 - 检查各列是否对齐正确", 
                         font=('Arial', 10), fg='blue')
    info_label.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_treeview_format()
