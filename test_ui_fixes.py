#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI修复：
1. 修复红框重复显示问题
2. 将索引图移动到组状态列表位置
3. 删除组状态列表
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_fixes():
    """测试UI修复"""
    try:
        print("=" * 60)
        print("测试UI修复：红框、索引图移动、删除组状态列表")
        print("=" * 60)
        
        # 测试1：检查红框重复显示修复
        print("\n1. 检查红框重复显示修复...")
        
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            viz_content = f.read()
            
        # 检查是否注释了重复的高亮显示
        if '# self._highlight_current_annotation_group' in viz_content:
            print("✅ 已注释重复的高亮显示方法")
        else:
            print("❌ 未找到注释的高亮显示方法")
            
        # 检查是否注释了原来的图例显示
        if '# self._create_enhanced_legend' in viz_content:
            print("✅ 已注释原来的图例显示")
        else:
            print("❌ 未找到注释的图例显示")
            
        # 测试2：检查主程序文件的修改
        print("\n2. 检查主程序文件修改...")
        
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否删除了组状态栏相关代码
        if '_create_group_status_panel' not in content:
            print("✅ 已删除组状态栏创建方法")
        else:
            print("❌ 仍然存在组状态栏创建方法")
            
        # 检查是否添加了索引图相关代码
        if '_create_legend_panel' in content:
            print("✅ 找到索引图创建方法")
        else:
            print("❌ 未找到索引图创建方法")
            
        if '_update_legend_display' in content:
            print("✅ 找到索引图更新方法")
        else:
            print("❌ 未找到索引图更新方法")
            
        if '_draw_legend_content' in content:
            print("✅ 找到索引图绘制方法")
        else:
            print("❌ 未找到索引图绘制方法")
            
        # 检查是否修改了容器名称
        if 'legend_container' in content:
            print("✅ 找到索引图容器")
        else:
            print("❌ 未找到索引图容器")
            
        # 测试3：检查方法调用链
        print("\n3. 检查方法调用链...")
        
        # 检查update_group_list是否调用了索引图更新
        if 'self._update_legend_display()' in content:
            print("✅ 组列表更新时会同步更新索引图")
        else:
            print("❌ 组列表更新时未同步更新索引图")
            
        # 检查是否移除了组状态栏更新调用
        if 'self._update_group_status_panel()' not in content:
            print("✅ 已移除组状态栏更新调用")
        else:
            print("❌ 仍然存在组状态栏更新调用")
            
        # 测试4：检查UI布局修改
        print("\n4. 检查UI布局修改...")
        
        # 检查是否创建了索引图相关组件
        if 'self.legend_fig' in content and 'self.legend_ax' in content:
            print("✅ 找到索引图matplotlib组件")
        else:
            print("❌ 未找到索引图matplotlib组件")
            
        # 检查是否移除了TreeView相关代码
        if 'self.group_tree_overview' not in content:
            print("✅ 已移除右侧TreeView组件")
        else:
            print("❌ 仍然存在右侧TreeView组件")
            
        print("\n" + "=" * 60)
        print("UI修复检查完成")
        print("=" * 60)
        
        # 总结
        print("\n📋 修复总结：")
        print("1. ✅ 修复了红框重复显示问题（注释了重复的高亮方法）")
        print("2. ✅ 将索引图移动到右侧框（替换了组状态列表）")
        print("3. ✅ 删除了组状态列表相关代码")
        print("4. ✅ 移除了原来概览图中的图例显示")
        
        print("\n🎯 预期效果：")
        print("- 全图概览中只显示一个红框（正在标注组）")
        print("- 右侧框显示索引图，包含组状态和实体类别信息")
        print("- 不再有重复的组状态列表")
        print("- 索引图会随着组状态变化实时更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_compatibility():
    """测试导入兼容性"""
    try:
        print("\n" + "=" * 60)
        print("测试导入兼容性")
        print("=" * 60)
        
        # 测试导入主程序
        try:
            import ast
            
            with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
                main_content = f.read()
            
            # 解析语法
            ast.parse(main_content)
            print("✅ 主程序文件语法正确")
            
        except SyntaxError as e:
            print(f"❌ 主程序文件语法错误: {e}")
            return False
            
        # 测试导入可视化器
        try:
            with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
                viz_content = f.read()
            
            # 解析语法
            ast.parse(viz_content)
            print("✅ 可视化器文件语法正确")
            
        except SyntaxError as e:
            print(f"❌ 可视化器文件语法错误: {e}")
            return False
            
        print("✅ 所有文件导入兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 导入兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始UI修复测试...")
    
    # 测试UI修复
    test1_result = test_ui_fixes()
    
    # 测试导入兼容性
    test2_result = test_import_compatibility()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"UI修复测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"导入兼容性测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！UI修复已成功实现。")
        print("\n📝 使用说明：")
        print("1. 启动程序后，全图概览中只会显示一个红框（正在标注组）")
        print("2. 右侧'2.实体全图概览'框下方显示索引图")
        print("3. 索引图包含组状态和实体类别的颜色说明")
        print("4. 不再有重复的组状态列表")
    else:
        print("\n❌ 部分测试失败，请检查修改内容。")
    
    print("=" * 60)
