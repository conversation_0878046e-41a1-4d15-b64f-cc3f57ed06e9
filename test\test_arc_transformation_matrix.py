#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的圆弧变换矩阵功能
"""

import sys
import os
import math
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Arc, Circle
import matplotlib.patches as patches

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import TransformationMatrix

def test_transformation_matrix():
    """测试变换矩阵基本功能"""
    print("=== 测试变换矩阵基本功能 ===")
    
    # 测试点
    test_point = (1.0, 0.0)
    
    # 测试1: 纯旋转 (90度)
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    rotation = 90
    scale = (1.0, 1.0)
    
    matrix = TransformationMatrix(base_point, rotation, scale)
    transformed = matrix.apply(test_point)
    print(f"旋转90度: {test_point} → {transformed}")
    print(f"预期: (0, 1), 实际: ({transformed[0]:.3f}, {transformed[1]:.3f})")
    
    # 测试2: 镜像变换 (X轴镜像)
    scale = (1.0, -1.0)
    matrix = TransformationMatrix(base_point, 0, scale)
    transformed = matrix.apply(test_point)
    is_mirrored = matrix.is_mirrored()
    print(f"X轴镜像: {test_point} → {transformed}, 镜像检测: {is_mirrored}")
    
    # 测试3: 复合变换 (镜像 + 旋转)
    scale = (-1.0, 1.0)
    rotation = 45
    matrix = TransformationMatrix(base_point, rotation, scale)
    transformed = matrix.apply(test_point)
    is_mirrored = matrix.is_mirrored()
    print(f"Y轴镜像+45度旋转: {test_point} → {transformed}, 镜像检测: {is_mirrored}")

def create_test_arc_data():
    """创建测试圆弧数据"""
    class MockArc:
        def __init__(self, center_x, center_y, radius, start_angle, end_angle):
            self.dxf = MockDxf(center_x, center_y, radius, start_angle, end_angle)
            self.doc = MockDoc()
    
    class MockDxf:
        def __init__(self, center_x, center_y, radius, start_angle, end_angle):
            self.center = MockPoint(center_x, center_y)
            self.radius = radius
            self.start_angle = start_angle
            self.end_angle = end_angle
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    class MockDoc:
        def __init__(self):
            self.header = {'$ANGDIR': 0}  # 逆时针
    
    # 创建测试圆弧：中心(0,0)，半径1，从0度到90度
    return MockArc(0, 0, 1, 0, 90)

def test_arc_transformations():
    """测试圆弧变换的各种情况"""
    print("\n=== 测试圆弧变换 ===")
    
    # 导入处理器
    from cad_data_processor import CADDataProcessor
    processor = CADDataProcessor()
    
    # 创建测试圆弧
    arc = create_test_arc_data()
    
    # 测试变换场景
    test_cases = [
        {
            'name': '无变换',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (1.0, 1.0)
        },
        {
            'name': '平移',
            'base_point': type('obj', (object,), {'x': 2, 'y': 3})(),
            'rotation': 0,
            'scale': (1.0, 1.0)
        },
        {
            'name': '旋转45度',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 45,
            'scale': (1.0, 1.0)
        },
        {
            'name': 'X轴镜像',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (1.0, -1.0)
        },
        {
            'name': 'Y轴镜像',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (-1.0, 1.0)
        },
        {
            'name': '双轴镜像',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (-1.0, -1.0)
        },
        {
            'name': '镜像+旋转',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 90,
            'scale': (1.0, -1.0)
        },
        {
            'name': '缩放',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (2.0, 1.5)
        }
    ]
    
    results = []
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        
        # 创建变换矩阵
        matrix = TransformationMatrix(case['base_point'], case['rotation'], case['scale'])
        
        # 应用变换
        try:
            result = processor._transform_arc(arc, matrix)
            results.append({
                'name': case['name'],
                'original': {
                    'center': (arc.dxf.center.x, arc.dxf.center.y),
                    'radius': arc.dxf.radius,
                    'start_angle': arc.dxf.start_angle,
                    'end_angle': arc.dxf.end_angle
                },
                'transformed': result,
                'is_mirrored': matrix.is_mirrored()
            })
            
            print(f"原始: 中心({arc.dxf.center.x}, {arc.dxf.center.y}), "
                  f"半径={arc.dxf.radius}, 角度={arc.dxf.start_angle}°-{arc.dxf.end_angle}°")
            print(f"变换后: 中心({result['center'][0]:.2f}, {result['center'][1]:.2f}), "
                  f"半径={result['radius']:.2f}, 角度={result['start_angle']:.1f}°-{result['end_angle']:.1f}°")
            print(f"镜像检测: {matrix.is_mirrored()}")
            
        except Exception as e:
            print(f"变换失败: {e}")
            import traceback
            traceback.print_exc()
    
    return results

def visualize_arc_transformations(results):
    """可视化圆弧变换结果"""
    print("\n=== 生成可视化图表 ===")
    
    # 创建子图
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    for i, result in enumerate(results[:8]):  # 最多显示8个
        ax = axes[i]
        ax.set_xlim(-3, 3)
        ax.set_ylim(-3, 3)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(result['name'])
        
        # 绘制原始圆弧（蓝色虚线）
        orig = result['original']
        orig_arc = Arc(orig['center'], 2*orig['radius'], 2*orig['radius'],
                      theta1=orig['start_angle'], theta2=orig['end_angle'],
                      edgecolor='blue', linestyle='--', linewidth=1, alpha=0.5)
        ax.add_patch(orig_arc)
        
        # 绘制变换后圆弧（红色实线）
        trans = result['transformed']
        trans_arc = Arc(trans['center'], 2*trans['radius'], 2*trans['radius'],
                       theta1=trans['start_angle'], theta2=trans['end_angle'],
                       edgecolor='red', linewidth=2)
        ax.add_patch(trans_arc)
        
        # 标记中心点
        ax.plot(orig['center'][0], orig['center'][1], 'bo', markersize=4, alpha=0.5)
        ax.plot(trans['center'][0], trans['center'][1], 'ro', markersize=4)
        
        # 添加镜像标记
        if result['is_mirrored']:
            ax.text(0.02, 0.98, '镜像', transform=ax.transAxes, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                   verticalalignment='top', fontsize=8)
    
    # 隐藏多余的子图
    for i in range(len(results), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('arc_transformation_test.png', dpi=150, bbox_inches='tight')
    print("可视化图表已保存为: arc_transformation_test.png")
    plt.show()

def main():
    """主测试函数"""
    print("圆弧变换矩阵测试")
    print("=" * 50)
    
    # 测试基础变换矩阵
    test_transformation_matrix()
    
    # 测试圆弧变换
    results = test_arc_transformations()
    
    # 生成可视化
    if results:
        try:
            visualize_arc_transformations(results)
        except Exception as e:
            print(f"可视化生成失败: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
