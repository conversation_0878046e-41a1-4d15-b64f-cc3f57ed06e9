#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件夹扫描问题
"""

import sys
import os
import tempfile
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_folder_with_dxf():
    """创建包含DXF文件的测试文件夹"""
    print("=== 创建测试文件夹 ===")
    
    # 创建临时文件夹
    temp_dir = tempfile.mkdtemp(prefix="dxf_test_")
    print(f"测试文件夹: {temp_dir}")
    
    # 创建测试DXF文件
    test_files = [
        'test1.dxf',
        'test2.DXF',  # 大写扩展名
        'test3.dwg',
        'test4.txt',  # 非CAD文件
        'test5.dxf'
    ]
    
    for file_name in test_files:
        file_path = os.path.join(temp_dir, file_name)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# 测试文件: {file_name}\n")
            f.write("# 模拟DXF内容\n")
            f.write("SECTION\nENTITIES\nLINE\n0,0\n10,10\nENDSEC\nEOF\n")
    
    print(f"创建了 {len(test_files)} 个测试文件:")
    for file_name in test_files:
        print(f"  - {file_name}")
    
    return temp_dir

def test_folder_scanning_directly():
    """直接测试文件夹扫描逻辑"""
    print("\n=== 直接测试文件夹扫描逻辑 ===")
    
    # 创建测试文件夹
    test_folder = create_test_folder_with_dxf()
    
    try:
        # 模拟扫描逻辑
        all_files = []
        supported_extensions = ['.dxf', '.dwg']
        
        print(f"扫描文件夹: {test_folder}")
        print(f"文件夹存在: {os.path.exists(test_folder)}")
        print(f"是目录: {os.path.isdir(test_folder)}")
        
        if os.path.exists(test_folder) and os.path.isdir(test_folder):
            files_in_folder = os.listdir(test_folder)
            print(f"文件夹中的文件: {files_in_folder}")
            
            for file_name in files_in_folder:
                file_path = os.path.join(test_folder, file_name)
                print(f"检查文件: {file_name}")
                
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file_name.lower())
                    print(f"  扩展名: '{ext}'")
                    
                    if ext in supported_extensions:
                        all_files.append(file_path)
                        print(f"  ✅ 找到CAD文件: {file_name}")
                    else:
                        print(f"  ⏭️ 跳过非CAD文件: {file_name}")
                else:
                    print(f"  ⏭️ 跳过非文件: {file_name}")
        
        print(f"\n扫描结果: 找到 {len(all_files)} 个CAD文件")
        for file_path in all_files:
            print(f"  - {os.path.basename(file_path)}")
        
        return len(all_files) > 0
        
    finally:
        # 清理测试文件夹
        import shutil
        try:
            shutil.rmtree(test_folder)
            print(f"\n🧹 测试文件夹已清理: {test_folder}")
        except:
            print(f"\n⚠️ 请手动清理测试文件夹: {test_folder}")

def test_app_folder_scanning():
    """测试应用的文件夹扫描"""
    print("\n=== 测试应用的文件夹扫描 ===")
    
    # 创建测试文件夹
    test_folder = create_test_folder_with_dxf()
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = EnhancedCADAppV2(root)
        
        # 设置文件夹路径
        app.current_folder = test_folder
        print(f"设置应用文件夹: {app.current_folder}")
        
        # 执行扫描
        print("开始扫描...")
        app._scan_folder_files()
        
        # 检查结果
        print(f"扫描结果: 找到 {len(app.all_files)} 个CAD文件")
        print(f"文件状态: {len(app.file_status)} 个文件状态")
        
        for file_path in app.all_files:
            file_name = os.path.basename(file_path)
            print(f"  - {file_name}: {app.file_status.get(file_name, '无状态')}")
        
        root.destroy()
        return len(app.all_files) > 0
        
    except Exception as e:
        print(f"❌ 应用扫描测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试文件夹
        import shutil
        try:
            shutil.rmtree(test_folder)
            print(f"\n🧹 测试文件夹已清理: {test_folder}")
        except:
            print(f"\n⚠️ 请手动清理测试文件夹: {test_folder}")

def test_folder_var_issue():
    """测试folder_var可能的问题"""
    print("\n=== 测试folder_var问题 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 测试不同的folder_var值
        test_cases = [
            "",  # 空字符串
            "C:\\不存在的文件夹",  # 不存在的路径
            "C:\\Windows\\System32\\notepad.exe",  # 文件路径而不是文件夹
            "C:\\Windows",  # 存在的文件夹但没有DXF文件
        ]
        
        for i, test_path in enumerate(test_cases):
            print(f"\n测试用例 {i+1}: '{test_path}'")
            
            app.folder_var.set(test_path)
            folder = app.folder_var.get()
            
            print(f"folder_var.get(): '{folder}'")
            print(f"路径存在: {os.path.exists(folder) if folder else False}")
            print(f"是目录: {os.path.isdir(folder) if folder else False}")
            
            if folder:
                app.current_folder = folder
                try:
                    app._scan_folder_files()
                    print(f"扫描结果: {len(app.all_files)} 个文件")
                except Exception as e:
                    print(f"扫描失败: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ folder_var测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件夹扫描问题诊断")
    print("=" * 60)
    
    tests = [
        ("直接扫描逻辑测试", test_folder_scanning_directly),
        ("应用扫描测试", test_app_folder_scanning),
        ("folder_var问题测试", test_folder_var_issue)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件夹扫描逻辑正常。")
    else:
        print("⚠️ 部分测试失败，需要检查具体问题。")
    
    print("\n💡 如果问题仍然存在，请检查:")
    print("1. 文件夹路径是否正确")
    print("2. DXF文件扩展名是否正确（.dxf 或 .dwg）")
    print("3. 文件夹权限是否足够")
    print("4. 文件夹中是否确实包含DXF文件")

if __name__ == "__main__":
    main()
