#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配色方案修复
"""

import sys
import os

def test_color_scheme_files():
    """测试配色方案文件完整性"""
    try:
        print("测试配色方案文件完整性:")
        
        # 需要的所有颜色
        required_colors = {
            'background', 'wall', 'door_window', 'railing', 'furniture',
            'bed', 'sofa', 'cabinet', 'dining_table', 'appliance',
            'stair', 'elevator', 'dimension', 'room_label', 'column',
            'other', 'fill', 'text', 'current_group', 'labeled_group',
            'wall_fill', 'door_window_fill', 'railing_fill', 'furniture_fill',
            'bed_fill', 'sofa_fill', 'cabinet_fill', 'dining_table_fill',
            'appliance_fill', 'stair_fill', 'elevator_fill', 'dimension_fill',
            'room_label_fill', 'column_fill', 'other_fill'
        }
        
        # 检查配色方案目录
        color_schemes_dir = "color_schemes"
        if not os.path.exists(color_schemes_dir):
            print(f"✗ 配色方案目录不存在: {color_schemes_dir}")
            return False
        
        # 获取所有配色文件
        color_files = [f for f in os.listdir(color_schemes_dir) if f.endswith('.txt')]
        print(f"✓ 找到 {len(color_files)} 个配色文件: {color_files}")
        
        all_files_valid = True
        
        for filename in color_files:
            filepath = os.path.join(color_schemes_dir, filename)
            print(f"\n检查文件: {filename}")
            
            # 读取配色文件
            scheme_colors = set()
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and '=' in line:
                            key, value = line.split('=', 1)
                            scheme_colors.add(key.strip())
                
                # 检查是否包含所有必需颜色
                missing_colors = required_colors - scheme_colors
                extra_colors = scheme_colors - required_colors
                
                if missing_colors:
                    print(f"  ✗ 缺少颜色: {missing_colors}")
                    all_files_valid = False
                else:
                    print(f"  ✓ 包含所有必需颜色 ({len(scheme_colors)}个)")
                
                if extra_colors:
                    print(f"  ⚠ 额外颜色: {extra_colors}")
                
                print(f"  📊 颜色统计: 需要{len(required_colors)}个, 实际{len(scheme_colors)}个")
                
            except Exception as e:
                print(f"  ✗ 读取文件失败: {e}")
                all_files_valid = False
        
        if all_files_valid:
            print(f"\n✓ 所有配色方案文件都完整")
            return True
        else:
            print(f"\n✗ 部分配色方案文件不完整")
            return False
        
    except Exception as e:
        print(f"✗ 配色方案文件测试失败: {e}")
        return False

def test_default_color_scheme():
    """测试默认配色方案"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试默认配色方案:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查默认配色方案
        default_scheme = app.default_color_scheme
        current_scheme = app.current_color_scheme
        
        print(f"✓ 默认配色方案包含 {len(default_scheme)} 种颜色")
        print(f"✓ 当前配色方案包含 {len(current_scheme)} 种颜色")
        
        # 检查填充颜色
        fill_colors = [key for key in default_scheme.keys() if key.endswith('_fill')]
        print(f"✓ 填充颜色数量: {len(fill_colors)}")
        
        # 列出所有填充颜色
        print("填充颜色列表:")
        for color in sorted(fill_colors):
            print(f"  {color}: {default_scheme[color]}")
        
        root.destroy()
        print("✓ 默认配色方案检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 默认配色方案测试失败: {e}")
        return False

def test_v2_color_scheme():
    """测试V2版本配色方案"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        print("测试V2版本配色方案:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 检查V2版本的配色方案
        default_scheme = app.default_color_scheme
        current_scheme = app.current_color_scheme
        
        print(f"✓ V2默认配色方案包含 {len(default_scheme)} 种颜色")
        print(f"✓ V2当前配色方案包含 {len(current_scheme)} 种颜色")
        
        # 检查填充颜色
        fill_colors = [key for key in default_scheme.keys() if key.endswith('_fill')]
        print(f"✓ V2填充颜色数量: {len(fill_colors)}")
        
        # 检查是否有缺失的颜色
        required_colors = {
            'wall_fill', 'door_window_fill', 'railing_fill', 'furniture_fill',
            'bed_fill', 'sofa_fill', 'cabinet_fill', 'dining_table_fill',
            'appliance_fill', 'stair_fill', 'elevator_fill', 'dimension_fill',
            'room_label_fill', 'column_fill', 'other_fill'
        }
        
        scheme_colors = set(default_scheme.keys())
        missing_colors = required_colors - scheme_colors
        
        if missing_colors:
            print(f"✗ V2版本缺少颜色: {missing_colors}")
            root.destroy()
            return False
        else:
            print("✓ V2版本包含所有必需的填充颜色")
        
        root.destroy()
        print("✓ V2版本配色方案检查通过")
        return True
        
    except Exception as e:
        print(f"✗ V2版本配色方案测试失败: {e}")
        return False

def test_color_scheme_loading():
    """测试配色方案加载"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试配色方案加载:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 加载配色方案
        app.load_color_schemes()
        
        # 检查加载的配色方案
        loaded_schemes = app.color_schemes
        print(f"✓ 加载了 {len(loaded_schemes)} 个配色方案")
        
        for scheme_name in loaded_schemes:
            scheme = loaded_schemes[scheme_name]
            print(f"  {scheme_name}: {len(scheme)} 种颜色")
        
        root.destroy()
        print("✓ 配色方案加载检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色方案加载测试失败: {e}")
        return False

def test_startup_without_errors():
    """测试启动时无错误"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        import io
        import sys
        
        print("测试启动时无错误:")
        
        # 捕获标准输出
        old_stdout = sys.stdout
        sys.stdout = captured_output = io.StringIO()
        
        try:
            # 创建临时根窗口（不显示）
            root = tk.Tk()
            root.withdraw()
            
            app = EnhancedCADAppV2(root)
            
            # 恢复标准输出
            sys.stdout = old_stdout
            output = captured_output.getvalue()
            
            # 检查是否有错误信息
            if "配色方案缺少颜色" in output:
                print(f"✗ 启动时仍有配色错误:")
                print(output)
                root.destroy()
                return False
            else:
                print("✓ 启动时无配色错误")
            
            root.destroy()
            print("✓ 启动测试通过")
            return True
            
        except Exception as e:
            sys.stdout = old_stdout
            print(f"✗ 启动测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"✗ 启动测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试配色方案修复...")
    print("=" * 60)
    
    tests = [
        ("配色方案文件完整性", test_color_scheme_files),
        ("默认配色方案", test_default_color_scheme),
        ("V2版本配色方案", test_v2_color_scheme),
        ("配色方案加载", test_color_scheme_loading),
        ("启动时无错误", test_startup_without_errors)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 配色方案修复测试全部通过！")
        print("\n📋 修复总结:")
        print("✅ 更新了所有配色方案文件，添加了缺失的填充颜色")
        print("✅ 深色主题、护眼绿色、123主题都已完整")
        print("✅ 启动时不再显示配色错误信息")
        print("✅ 所有35种颜色都已包含")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
