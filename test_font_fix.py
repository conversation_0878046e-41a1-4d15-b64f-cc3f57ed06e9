#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试中文字体修复
"""

import sys
import os
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

def test_chinese_fonts():
    """测试中文字体显示"""
    print("=" * 60)
    print("测试中文字体显示")
    print("=" * 60)
    
    # 测试字体列表
    chinese_font_names = [
        'Microsoft YaHei',
        'SimHei', 
        'SimSun',
        'KaiTi',
        'FangSong',
        'Microsoft JhengHei',
        'PingFang SC',
        'Hiragino Sans GB',
        'Source Han Sans CN',
        'Noto Sans CJK SC'
    ]
    
    print("\n🔍 检查系统可用的中文字体：")
    available_fonts = []
    
    for font_name in chinese_font_names:
        try:
            # 测试字体是否可用
            test_font = fm.FontProperties(family=font_name)
            available_fonts.append(font_name)
            print(f"✅ {font_name} - 可用")
        except Exception as e:
            print(f"❌ {font_name} - 不可用: {e}")
    
    if not available_fonts:
        print("\n⚠️ 没有找到可用的中文字体，将使用系统默认配置")
        # 设置matplotlib全局字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        return None
    else:
        print(f"\n✅ 找到 {len(available_fonts)} 个可用中文字体")
        return available_fonts[0]  # 返回第一个可用字体

def test_legend_display():
    """测试索引图显示"""
    print("\n" + "=" * 60)
    print("测试索引图中文显示")
    print("=" * 60)
    
    try:
        # 设置中文字体
        best_font = test_chinese_fonts()
        
        # 创建测试图形
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('white')
        
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.axis('off')
        
        # 设置字体
        if best_font:
            font_prop = fm.FontProperties(family=best_font)
            print(f"\n🎨 使用字体: {best_font}")
        else:
            font_prop = fm.FontProperties()
            print(f"\n🎨 使用系统默认字体")
        
        # 测试中文文本显示
        y_pos = 9
        line_height = 1
        
        # 标题
        ax.text(1, y_pos, '━━ 组状态 ━━', fontproperties=font_prop, fontsize=12, weight='bold')
        y_pos -= line_height
        
        # 状态项目
        status_items = [
            ('正在标注 (12个实体)', '#FF0000'),
            ('已标注 (25个实体)', '#00AA00'),
            ('自动标注 (8个实体)', '#0066CC'),
            ('未标注 (15个实体)', '#D3D3D3')
        ]
        
        for text, color in status_items:
            # 绘制颜色块
            ax.add_patch(plt.Rectangle((1, y_pos-0.2), 0.5, 0.4, facecolor=color, alpha=0.8))
            # 绘制文字
            ax.text(2, y_pos, text, fontproperties=font_prop, fontsize=10, va='center')
            y_pos -= line_height
        
        y_pos -= 0.5  # 增加间距
        
        # 类别标题
        ax.text(1, y_pos, '━━ 实体类别 ━━', fontproperties=font_prop, fontsize=12, weight='bold')
        y_pos -= line_height
        
        # 类别项目
        category_items = [
            ('■ 墙体', '#8B4513'),
            ('■ 门窗', '#FFD700'),
            ('■ 其他', '#808080')
        ]
        
        for text, color in category_items:
            # 绘制颜色块
            ax.add_patch(plt.Rectangle((1, y_pos-0.2), 0.5, 0.4, facecolor=color, alpha=0.8))
            # 绘制文字
            ax.text(2, y_pos, text, fontproperties=font_prop, fontsize=10, va='center')
            y_pos -= line_height
        
        # 保存测试图片
        plt.tight_layout()
        plt.savefig('font_test.png', dpi=150, bbox_inches='tight')
        print("\n✅ 测试图片已保存为 font_test.png")
        
        # 显示图形（如果在交互环境中）
        try:
            plt.show(block=False)
            print("✅ 测试图形已显示")
        except:
            print("⚠️ 无法显示图形（非交互环境）")
        
        plt.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_matplotlib_config():
    """测试matplotlib配置"""
    print("\n" + "=" * 60)
    print("测试matplotlib中文配置")
    print("=" * 60)
    
    try:
        # 显示当前字体配置
        print(f"当前字体配置: {plt.rcParams['font.sans-serif']}")
        print(f"Unicode减号: {plt.rcParams['axes.unicode_minus']}")
        
        # 设置中文字体配置
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        print(f"新字体配置: {plt.rcParams['font.sans-serif']}")
        print(f"新Unicode减号: {plt.rcParams['axes.unicode_minus']}")
        
        # 测试简单中文显示
        fig, ax = plt.subplots(figsize=(4, 2))
        ax.text(0.5, 0.5, '测试中文显示', ha='center', va='center', fontsize=14)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        plt.tight_layout()
        plt.savefig('simple_chinese_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 简单中文测试完成，图片保存为 simple_chinese_test.png")
        return True
        
    except Exception as e:
        print(f"❌ matplotlib配置测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始中文字体修复测试...")
    
    # 测试1：matplotlib配置
    test1_result = test_matplotlib_config()
    
    # 测试2：索引图显示
    test2_result = test_legend_display()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"matplotlib配置测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"索引图显示测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 中文字体修复测试通过！")
        print("\n📝 修复说明：")
        print("1. 增强了中文字体检测和设置逻辑")
        print("2. 添加了多种中文字体的备选方案")
        print("3. 设置了matplotlib全局中文字体配置")
        print("4. 优化了字体属性的使用方式")
        print("\n🎯 预期效果：")
        print("- 右侧索引图中的中文文字应该正常显示")
        print("- 不再出现方框字符")
        print("- 支持多种系统中文字体")
    else:
        print("\n❌ 部分测试失败，请检查系统字体配置。")
    
    print("=" * 60)
