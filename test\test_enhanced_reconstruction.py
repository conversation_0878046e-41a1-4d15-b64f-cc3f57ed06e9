#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的重构方法
验证基于完整图形截取的圆弧椭圆重构功能
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Arc, Ellipse as MplEllipse
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_arc_reconstruction():
    """测试增强的圆弧重构"""
    print("🔄 测试增强的圆弧重构方法...")
    
    try:
        from arc_ellipse_reconstructor import ArcEllipseReconstructor
        
        # 创建重构器
        reconstructor = ArcEllipseReconstructor(sample_points_count=30)
        
        # 测试用例：包含各种复杂情况的圆弧
        test_arcs = [
            {
                'name': '普通圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (50, 50),
                    'radius': 25,
                    'start_angle': 0,
                    'end_angle': 90,
                    'layer': 'test'
                }
            },
            {
                'name': '跨越0度圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (100, 50),
                    'radius': 20,
                    'start_angle': 270,
                    'end_angle': 45,
                    'layer': 'test'
                }
            },
            {
                'name': '镜像圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (150, 50),
                    'radius': 22,
                    'start_angle': 0,
                    'end_angle': 180,
                    'scale_x': -1.0,
                    'scale_y': 1.0,
                    'layer': 'test'
                }
            },
            {
                'name': '缩放圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (200, 50),
                    'radius': 15,
                    'start_angle': 45,
                    'end_angle': 225,
                    'scale_x': 1.5,
                    'scale_y': 0.8,
                    'layer': 'test'
                }
            }
        ]
        
        success_count = 0
        
        for test_case in test_arcs:
            print(f"\n🔄 测试: {test_case['name']}")
            
            try:
                # 重构圆弧
                reconstructed = reconstructor.reconstruct_arc(test_case['entity'])
                
                # 验证结果
                if reconstructed['type'] == 'POLYLINE' and 'reconstruction_info' in reconstructed:
                    info = reconstructed['reconstruction_info']
                    points = np.array(reconstructed['points'])
                    
                    print(f"  ✅ 重构成功")
                    print(f"    中心点: {info['center']}")
                    print(f"    半径: {info['radius']:.2f}")
                    print(f"    角度范围: {info['start_angle']:.1f}° - {info['end_angle']:.1f}°")
                    print(f"    起点: [{info['start_point'][0]:.2f}, {info['start_point'][1]:.2f}]")
                    print(f"    终点: [{info['end_point'][0]:.2f}, {info['end_point'][1]:.2f}]")
                    print(f"    采样点数: {info['sample_points_count']}")
                    print(f"    镜像处理: {'是' if info.get('is_mirrored', False) else '否'}")
                    
                    # 验证点的合理性
                    center = np.array(info['center'])
                    distances = np.linalg.norm(points - center, axis=1)
                    avg_distance = np.mean(distances)
                    distance_std = np.std(distances)
                    
                    print(f"    平均距离: {avg_distance:.2f}")
                    print(f"    距离标准差: {distance_std:.3f}")
                    
                    if distance_std < 0.5:  # 允许一定的误差
                        print(f"    ✅ 几何验证通过")
                        success_count += 1
                    else:
                        print(f"    ⚠️ 几何验证失败")
                else:
                    print(f"  ❌ 重构失败: 结果格式不正确")
                    
            except Exception as e:
                print(f"  ❌ 重构异常: {e}")
        
        print(f"\n圆弧重构测试结果: {success_count}/{len(test_arcs)} 通过")
        return success_count == len(test_arcs)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_ellipse_reconstruction():
    """测试增强的椭圆重构"""
    print("\n🔄 测试增强的椭圆重构方法...")
    
    try:
        from arc_ellipse_reconstructor import ArcEllipseReconstructor
        
        # 创建重构器
        reconstructor = ArcEllipseReconstructor(sample_points_count=40)
        
        # 测试用例：包含各种复杂情况的椭圆
        test_ellipses = [
            {
                'name': '完整椭圆',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (100, 100),
                    'major_axis': (30, 0),
                    'ratio': 0.6,
                    'start_param': 0,
                    'end_param': 2 * np.pi,
                    'layer': 'test'
                }
            },
            {
                'name': '椭圆弧',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (200, 100),
                    'major_axis': (25, 15),
                    'ratio': 0.7,
                    'start_param': 0,
                    'end_param': np.pi,
                    'layer': 'test'
                }
            },
            {
                'name': '旋转椭圆弧',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (300, 100),
                    'major_axis': (20, 12),
                    'ratio': 0.8,
                    'start_param': np.pi/4,
                    'end_param': 3*np.pi/2,
                    'layer': 'test'
                }
            },
            {
                'name': '镜像椭圆弧',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (150, 200),
                    'major_axis': (18, 10),
                    'ratio': 0.75,
                    'start_param': 0,
                    'end_param': 1.5 * np.pi,
                    'scale_x': 1.0,
                    'scale_y': -1.0,
                    'layer': 'test'
                }
            }
        ]
        
        success_count = 0
        
        for test_case in test_ellipses:
            print(f"\n🔄 测试: {test_case['name']}")
            
            try:
                # 重构椭圆
                reconstructed = reconstructor.reconstruct_ellipse(test_case['entity'])
                
                # 验证结果
                if reconstructed['type'] == 'POLYLINE' and 'reconstruction_info' in reconstructed:
                    info = reconstructed['reconstruction_info']
                    points = np.array(reconstructed['points'])
                    
                    print(f"  ✅ 重构成功")
                    print(f"    中心点: {info['center']}")
                    print(f"    长轴: {info['a']:.2f}, 短轴: {info['b']:.2f}")
                    print(f"    旋转角度: {np.degrees(info['angle']):.1f}°")
                    print(f"    参数范围: {info['start_param']:.2f} - {info['end_param']:.2f}")
                    print(f"    起点: [{info['start_point'][0]:.2f}, {info['start_point'][1]:.2f}]")
                    print(f"    终点: [{info['end_point'][0]:.2f}, {info['end_point'][1]:.2f}]")
                    print(f"    采样点数: {info['sample_points_count']}")
                    print(f"    镜像处理: {'是' if info.get('is_mirrored', False) else '否'}")
                    
                    # 基本验证：检查点数量
                    if len(points) >= 3:
                        print(f"    ✅ 基本验证通过")
                        success_count += 1
                    else:
                        print(f"    ⚠️ 采样点数量不足")
                else:
                    print(f"  ❌ 重构失败: 结果格式不正确")
                    
            except Exception as e:
                print(f"  ❌ 重构异常: {e}")
        
        print(f"\n椭圆重构测试结果: {success_count}/{len(test_ellipses)} 通过")
        return success_count == len(test_ellipses)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 增强重构方法测试")
    print("📐 基于完整图形截取的圆弧椭圆重构")
    print("=" * 60)
    
    # 测试圆弧重构
    arc_success = test_enhanced_arc_reconstruction()
    
    # 测试椭圆重构
    ellipse_success = test_enhanced_ellipse_reconstruction()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"  圆弧重构: {'✅ 通过' if arc_success else '❌ 失败'}")
    print(f"  椭圆重构: {'✅ 通过' if ellipse_success else '❌ 失败'}")
    
    if arc_success and ellipse_success:
        print("🎉 所有测试通过！增强重构方法工作正常。")
        print("\n💡 新方法特点:")
        print("  - 基于完整图形进行精确截取")
        print("  - 通过端点和采样点定位方向")
        print("  - 正确处理镜像和缩放变换")
        print("  - 提供详细的重构信息")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
