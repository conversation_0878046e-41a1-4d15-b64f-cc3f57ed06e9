#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配色系统修复
"""

import sys
import os

def test_export_color_file_button():
    """测试导出配色文件按钮"""
    try:
        print("测试导出配色文件按钮:")
        
        # 检查代码中是否包含了导出配色文件的功能
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("导出文件按钮", "导出文件"),
            ("导出配色方案方法", "export_current_color_scheme"),
            ("文件保存对话框", "asksaveasfilename"),
            ("导出配色文件", "导出配色文件")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 导出配色文件按钮检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 导出配色文件按钮测试失败: {e}")
        return False

def test_apply_color_scheme_fix():
    """测试应用配色方案修复"""
    try:
        print("测试应用配色方案修复:")
        
        # 检查代码中是否包含了修复后的应用配色逻辑
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找apply_color_scheme方法
        apply_method_start = content.find("def apply_color_scheme")
        if apply_method_start == -1:
            print("✗ apply_color_scheme方法未找到")
            return False
        
        apply_method_end = content.find("def ", apply_method_start + 1)
        if apply_method_end == -1:
            apply_method_end = len(content)
        
        apply_method_content = content[apply_method_start:apply_method_end]
        
        # 检查关键修复
        checks = [
            ("只更新全图概览", "ax_overview"),
            ("保存当前内容", "保存当前的全图概览内容"),
            ("设置背景色", "set_facecolor"),
            ("当前组高亮", "current_group"),
            ("已标注组颜色", "labeled_group"),
            ("重新绘制实体", "_draw_entity"),
            ("不清除所有内容", "重新绘制所有组")
        ]
        
        for check_name, pattern in checks:
            if pattern in apply_method_content:
                print(f"✓ {check_name}: 已修复")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 应用配色方案修复检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 应用配色方案修复测试失败: {e}")
        return False

def test_color_scheme_structure():
    """测试配色方案结构"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试配色方案结构:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查配色方案是否包含必要的颜色
        required_colors = [
            'background', 'wall', 'door_window', 'current_group', 
            'labeled_group', 'text', 'other'
        ]
        
        default_scheme = app.default_color_scheme
        
        for color in required_colors:
            if color in default_scheme:
                print(f"✓ 颜色 {color}: {default_scheme[color]}")
            else:
                print(f"✗ 颜色 {color}: 缺失")
                root.destroy()
                return False
        
        root.destroy()
        print("✓ 配色方案结构检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色方案结构测试失败: {e}")
        return False

def test_export_functionality():
    """测试导出功能"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试导出功能:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查导出方法是否存在
        if hasattr(app, 'export_current_color_scheme'):
            print("✓ 导出方法存在")
        else:
            print("✗ 导出方法不存在")
            root.destroy()
            return False
        
        # 检查当前配色方案变量
        if hasattr(app, 'current_color_scheme') and app.current_color_scheme:
            print(f"✓ 当前配色方案已初始化，包含 {len(app.current_color_scheme)} 种颜色")
        else:
            print("✗ 当前配色方案未初始化")
            root.destroy()
            return False
        
        # 检查配色方案变量
        if hasattr(app, 'color_scheme_var'):
            print("✓ 配色方案变量存在")
        else:
            print("✗ 配色方案变量不存在")
            root.destroy()
            return False
        
        root.destroy()
        print("✓ 导出功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 导出功能测试失败: {e}")
        return False

def test_ui_layout():
    """测试UI布局"""
    try:
        print("测试UI布局:")
        
        # 检查代码中的UI布局
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找配色系统创建方法
        color_system_start = content.find("def _create_color_system")
        if color_system_start == -1:
            print("✗ _create_color_system方法未找到")
            return False
        
        color_system_end = content.find("def ", color_system_start + 1)
        if color_system_end == -1:
            color_system_end = len(content)
        
        color_system_content = content[color_system_start:color_system_end]
        
        # 检查UI组件
        ui_checks = [
            ("配色方案下拉菜单", "ttk.Combobox"),
            ("应用配色按钮", "应用配色"),
            ("配色设置按钮", "配色设置"),
            ("保存配色按钮", "保存配色"),
            ("导出文件按钮", "导出文件"),
            ("加载配色按钮", "加载配色")
        ]
        
        for check_name, pattern in ui_checks:
            if pattern in color_system_content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ UI布局检查通过")
        return True
        
    except Exception as e:
        print(f"✗ UI布局测试失败: {e}")
        return False

def test_color_application_logic():
    """测试配色应用逻辑"""
    try:
        print("测试配色应用逻辑:")
        
        # 检查应用配色的逻辑是否正确
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找apply_color_scheme方法
        apply_start = content.find("def apply_color_scheme")
        apply_end = content.find("def ", apply_start + 1)
        if apply_end == -1:
            apply_end = len(content)
        
        apply_content = content[apply_start:apply_end]
        
        # 检查关键逻辑
        logic_checks = [
            ("检查可视化器", "if self.visualizer"),
            ("检查全图概览", "ax_overview"),
            ("检查数据存在", "if hasattr(self, 'all_groups')"),
            ("清除并重绘", "ax_overview.clear()"),
            ("设置背景色", "set_facecolor"),
            ("遍历所有组", "for i, group in enumerate"),
            ("当前组判断", "if i == current_group_index"),
            ("绘制实体", "_draw_entity"),
            ("更新画布", "canvas.draw()"),
            ("错误处理", "except Exception as e")
        ]
        
        for check_name, pattern in logic_checks:
            if pattern in apply_content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 配色应用逻辑检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色应用逻辑测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试配色系统修复...")
    print("=" * 60)
    
    tests = [
        ("导出配色文件按钮", test_export_color_file_button),
        ("应用配色方案修复", test_apply_color_scheme_fix),
        ("配色方案结构", test_color_scheme_structure),
        ("导出功能", test_export_functionality),
        ("UI布局", test_ui_layout),
        ("配色应用逻辑", test_color_application_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 配色系统修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
