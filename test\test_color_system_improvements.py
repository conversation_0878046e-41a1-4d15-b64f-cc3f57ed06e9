#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配色系统改进
"""

import sys
import os

def test_color_system_ui_changes():
    """测试配色系统UI改进"""
    try:
        print("测试配色系统UI改进:")
        
        # 检查代码中是否包含了新的UI组件
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键UI组件
        ui_checks = [
            ("右下角位置", "全图概览专用"),
            ("配色方案下拉菜单", "ttk.Combobox"),
            ("应用配色按钮", "应用配色"),
            ("配色方案选择事件", "on_color_scheme_selected"),
            ("保存配色文件按钮", "保存配色文件"),
            ("下拉菜单更新", "update_color_scheme_combo")
        ]
        
        for check_name, pattern in ui_checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 配色系统UI改进检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色系统UI改进测试失败: {e}")
        return False

def test_color_system_functionality():
    """测试配色系统功能"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试配色系统功能:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查新方法是否存在
        methods = [
            'update_color_scheme_combo',
            'on_color_scheme_selected',
            'apply_color_scheme'
        ]
        
        for method in methods:
            if hasattr(app, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                root.destroy()
                return False
        
        # 检查新属性是否存在
        attributes = [
            'color_scheme_var',
            'color_scheme_combo'
        ]
        
        for attr in attributes:
            if hasattr(app, attr):
                print(f"✓ 属性 {attr} 存在")
            else:
                print(f"✗ 属性 {attr} 不存在")
                root.destroy()
                return False
        
        root.destroy()
        print("✓ 配色系统功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色系统功能测试失败: {e}")
        return False

def test_color_scheme_management():
    """测试配色方案管理"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试配色方案管理:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 测试配色方案初始化
        if hasattr(app, 'color_schemes') and app.color_schemes:
            print(f"✓ 配色方案已初始化，数量: {len(app.color_schemes)}")
        else:
            print("✗ 配色方案未初始化")
            root.destroy()
            return False
        
        # 测试默认配色方案
        if '默认配色' in app.color_schemes:
            print("✓ 默认配色方案存在")
        else:
            print("✗ 默认配色方案不存在")
            root.destroy()
            return False
        
        # 测试配色方案结构
        default_scheme = app.color_schemes['默认配色']
        required_colors = ['background', 'wall', 'door_window', 'fill', 'text']
        
        for color in required_colors:
            if color in default_scheme:
                print(f"✓ 颜色 {color} 存在")
            else:
                print(f"✗ 颜色 {color} 不存在")
                root.destroy()
                return False
        
        root.destroy()
        print("✓ 配色方案管理检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色方案管理测试失败: {e}")
        return False

def test_overview_only_application():
    """测试只针对全图概览的配色应用"""
    try:
        print("测试只针对全图概览的配色应用:")
        
        # 检查代码中是否包含了只针对全图概览的逻辑
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找apply_color_scheme方法
        apply_method_start = content.find("def apply_color_scheme")
        if apply_method_start == -1:
            print("✗ apply_color_scheme方法未找到")
            return False
        
        apply_method_end = content.find("def ", apply_method_start + 1)
        if apply_method_end == -1:
            apply_method_end = len(content)
        
        apply_method_content = content[apply_method_start:apply_method_end]
        
        # 检查关键逻辑
        checks = [
            ("只更新全图概览", "只更新全图概览"),
            ("不影响实体组预览", "不影响实体组预览"),
            ("ax_overview", "ax_overview"),
            ("重绘全图概览", "重绘全图概览")
        ]
        
        for check_name, pattern in checks:
            if pattern in apply_method_content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 只针对全图概览的配色应用检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 只针对全图概览的配色应用测试失败: {e}")
        return False

def test_save_color_file_button():
    """测试保存配色文件按钮"""
    try:
        print("测试保存配色文件按钮:")
        
        # 检查代码中是否包含了保存配色文件的逻辑
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("保存配色文件按钮", "保存配色文件"),
            ("保存选中方案函数", "save_selected_scheme"),
            ("文件保存对话框", "asksaveasfilename"),
            ("配色文件写入", "f.write")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 保存配色文件按钮检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 保存配色文件按钮测试失败: {e}")
        return False

def test_color_system_position():
    """测试配色系统位置"""
    try:
        print("测试配色系统位置:")
        
        # 检查代码中配色系统的位置
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找可视化区域创建
        viz_method_start = content.find("def _create_visualization")
        if viz_method_start == -1:
            print("✗ _create_visualization方法未找到")
            return False
        
        viz_method_end = content.find("def ", viz_method_start + 1)
        if viz_method_end == -1:
            viz_method_end = len(content)
        
        viz_method_content = content[viz_method_start:viz_method_end]
        
        # 检查位置相关的代码
        position_checks = [
            ("下部分框架", "下部分：配色系统"),
            ("配色系统调用", "_create_color_system(color_frame)"),
            ("右下角创建", "在右下角创建配色系统")
        ]
        
        for check_name, pattern in position_checks:
            if pattern in viz_method_content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 配色系统位置检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色系统位置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试配色系统改进...")
    print("=" * 60)
    
    tests = [
        ("配色系统UI改进", test_color_system_ui_changes),
        ("配色系统功能", test_color_system_functionality),
        ("配色方案管理", test_color_scheme_management),
        ("配色系统位置", test_color_system_position),
        ("只针对全图概览的配色应用", test_overview_only_application),
        ("保存配色文件按钮", test_save_color_file_button)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 配色系统改进测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
