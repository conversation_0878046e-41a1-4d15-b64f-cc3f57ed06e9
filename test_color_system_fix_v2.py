#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配色系统修复V2
验证用户反馈的5个问题：
1. 范围过大，没有按照要求只调整红框范围内的内容
2. 图纸上的目前只调整了背景颜色，其他颜色并未随之改变
3. 点击应用配色后应立即更新视图显示
4. 索引图中每个颜色方块增加轮廓，避免颜色与背景颜色一样时不好查看
5. 配色方案中增加高亮显示的颜色调整
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_color_scope_limitation():
    """测试配色范围限制（问题1）"""
    print("=" * 80)
    print("🎯 测试配色范围限制")
    print("=" * 80)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找apply_color_scheme_v2方法
        method_start = content.find("def apply_color_scheme_v2")
        if method_start == -1:
            print("❌ apply_color_scheme_v2方法未找到")
            return False
        
        method_end = content.find("def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查是否移除了全局配色更新
        if "self.visualizer.update_color_scheme" in method_content:
            print("❌ 仍然调用全局配色更新，范围过大")
            return False
        else:
            print("✅ 已移除全局配色更新调用")
        
        # 检查是否只更新红框内容
        if "_update_overview_with_color_scheme" in method_content:
            print("✅ 只更新全图概览（红框内容）")
        else:
            print("❌ 未找到红框内容更新")
            return False
        
        print("✅ 配色范围限制检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配色范围限制测试失败: {e}")
        return False

def test_entity_color_changes():
    """测试实体颜色变化（问题2）"""
    print("\n" + "=" * 80)
    print("🎨 测试实体颜色变化")
    print("=" * 80)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_get_entity_color_from_scheme_enhanced方法
        method_start = content.find("def _get_entity_color_from_scheme_enhanced")
        if method_start == -1:
            print("❌ _get_entity_color_from_scheme_enhanced方法未找到")
            return False
        
        method_end = content.find("def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查关键功能
        features = [
            ("标签颜色映射", "label_color_map"),
            ("图层名称判断", "layer_name"),
            ("实体类型映射", "type_color_map"),
            ("使用配色方案", "current_color_scheme"),
            ("错误处理", "except Exception")
        ]
        
        for feature_name, feature_text in features:
            if feature_text in method_content:
                print(f"✅ {feature_name}: 实现正确")
            else:
                print(f"❌ {feature_name}: 实现缺失")
                return False
        
        # 检查是否在概览更新中使用增强版颜色获取
        overview_method_start = content.find("def _update_overview_with_color_scheme")
        if overview_method_start != -1:
            overview_method_end = content.find("def ", overview_method_start + 1)
            if overview_method_end == -1:
                overview_method_end = len(content)
            
            overview_method_content = content[overview_method_start:overview_method_end]
            
            if "_get_entity_color_from_scheme_enhanced" in overview_method_content:
                print("✅ 全图概览使用增强版颜色获取")
            else:
                print("❌ 全图概览未使用增强版颜色获取")
                return False
        
        print("✅ 实体颜色变化检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 实体颜色变化测试失败: {e}")
        return False

def test_immediate_view_update():
    """测试立即视图更新（问题3）"""
    print("\n" + "=" * 80)
    print("🔄 测试立即视图更新")
    print("=" * 80)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_force_refresh_views方法
        method_start = content.find("def _force_refresh_views")
        if method_start == -1:
            print("❌ _force_refresh_views方法未找到")
            return False
        
        method_end = content.find("def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查关键功能
        features = [
            ("刷新全图概览画布", "canvas_overview.draw_idle"),
            ("刷新索引图画布", "legend_canvas.draw_idle"),
            ("强制更新GUI", "update_idletasks"),
            ("画布事件处理", "flush_events"),
            ("错误处理", "except Exception")
        ]
        
        for feature_name, feature_text in features:
            if feature_text in method_content:
                print(f"✅ {feature_name}: 实现正确")
            else:
                print(f"❌ {feature_name}: 实现缺失")
                return False
        
        # 检查apply_color_scheme_v2是否调用强制刷新
        apply_method_start = content.find("def apply_color_scheme_v2")
        if apply_method_start != -1:
            apply_method_end = content.find("def ", apply_method_start + 1)
            if apply_method_end == -1:
                apply_method_end = len(content)
            
            apply_method_content = content[apply_method_start:apply_method_end]
            
            if "_force_refresh_views" in apply_method_content:
                print("✅ 配色应用后调用强制刷新")
            else:
                print("❌ 配色应用后未调用强制刷新")
                return False
        
        print("✅ 立即视图更新检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 立即视图更新测试失败: {e}")
        return False

def test_legend_color_outlines():
    """测试索引图颜色方块轮廓（问题4）"""
    print("\n" + "=" * 80)
    print("🖼️ 测试索引图颜色方块轮廓")
    print("=" * 80)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有Rectangle的创建
        rectangle_patterns = [
            "add_patch(plt.Rectangle",
            "edgecolor='black'",
            "linewidth=1"
        ]
        
        rectangle_count = 0
        outline_count = 0
        
        for pattern in rectangle_patterns:
            count = content.count(pattern)
            if pattern == "add_patch(plt.Rectangle":
                rectangle_count = count
                print(f"✅ 找到 {count} 个Rectangle创建")
            elif pattern == "edgecolor='black'":
                outline_count = count
                print(f"✅ 找到 {count} 个黑色轮廓设置")
            elif pattern == "linewidth=1":
                linewidth_count = count
                print(f"✅ 找到 {count} 个线宽设置")
        
        # 检查是否所有Rectangle都有轮廓
        if outline_count >= 4:  # 至少应该有状态方块和类别方块的轮廓
            print("✅ 索引图颜色方块已添加轮廓")
        else:
            print(f"❌ 索引图颜色方块轮廓不足，期望至少4个，实际{outline_count}个")
            return False
        
        # 检查具体的轮廓实现
        legend_method_start = content.find("def _draw_legend_content")
        if legend_method_start != -1:
            legend_method_end = content.find("def ", legend_method_start + 1)
            if legend_method_end == -1:
                legend_method_end = len(content)
            
            legend_method_content = content[legend_method_start:legend_method_end]
            
            if "edgecolor='black'" in legend_method_content and "linewidth=1" in legend_method_content:
                print("✅ 索引图绘制方法包含轮廓设置")
            else:
                print("❌ 索引图绘制方法缺少轮廓设置")
                return False
        
        print("✅ 索引图颜色方块轮廓检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 索引图颜色方块轮廓测试失败: {e}")
        return False

def test_highlight_color_addition():
    """测试高亮颜色添加（问题5）"""
    print("\n" + "=" * 80)
    print("🌟 测试高亮颜色添加")
    print("=" * 80)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_init_color_system_v2方法中的高亮颜色设置
        init_method_start = content.find("def _init_color_system_v2")
        if init_method_start == -1:
            print("❌ _init_color_system_v2方法未找到")
            return False
        
        init_method_end = content.find("def ", init_method_start + 1)
        if init_method_end == -1:
            init_method_end = len(content)
        
        init_method_content = content[init_method_start:init_method_end]
        
        # 检查高亮颜色相关功能
        features = [
            ("高亮颜色检查", "'highlight' not in self.current_color_scheme"),
            ("高亮颜色设置", "self.current_color_scheme['highlight']"),
            ("默认配色方案高亮", "self.default_color_scheme['highlight']"),
            ("高亮颜色注释", "5. 配色方案中增加高亮显示的颜色调整"),
            ("高亮颜色默认值", "'highlight': '#FF0000'")
        ]
        
        for feature_name, feature_text in features:
            if feature_text in init_method_content:
                print(f"✅ {feature_name}: 实现正确")
            else:
                print(f"❌ {feature_name}: 实现缺失")
                return False
        
        # 检查高亮颜色在索引图中的使用
        if "highlight_color = getattr(self, 'current_color_scheme', {}).get('highlight'" in content:
            print("✅ 索引图使用高亮颜色")
        else:
            print("❌ 索引图未使用高亮颜色")
            return False
        
        # 检查高亮颜色在全图概览中的使用
        overview_method_start = content.find("def _update_overview_with_color_scheme")
        if overview_method_start != -1:
            overview_method_end = content.find("def ", overview_method_start + 1)
            if overview_method_end == -1:
                overview_method_end = len(content)
            
            overview_method_content = content[overview_method_start:overview_method_end]
            
            if "highlight" in overview_method_content:
                print("✅ 全图概览使用高亮颜色")
            else:
                print("❌ 全图概览未使用高亮颜色")
                return False
        
        print("✅ 高亮颜色添加检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 高亮颜色添加测试失败: {e}")
        return False

def test_overall_integration():
    """测试整体集成"""
    print("\n" + "=" * 80)
    print("🔗 测试整体集成")
    print("=" * 80)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 成功导入增强版CAD应用V2")
        
        # 检查关键方法是否存在
        methods_to_check = [
            'apply_color_scheme_v2',
            '_update_overview_with_color_scheme',
            '_get_entity_color_from_scheme_enhanced',
            '_force_refresh_views',
            '_init_color_system_v2'
        ]
        
        for method_name in methods_to_check:
            if hasattr(EnhancedCADAppV2, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法缺失")
                return False
        
        print("✅ 整体集成检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 整体集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始配色系统修复V2测试...")
    
    # 执行所有测试
    tests = [
        ("配色范围限制", test_color_scope_limitation),
        ("实体颜色变化", test_entity_color_changes),
        ("立即视图更新", test_immediate_view_update),
        ("索引图颜色方块轮廓", test_legend_color_outlines),
        ("高亮颜色添加", test_highlight_color_addition),
        ("整体集成", test_overall_integration)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！配色系统V2已成功修复。")
        print("\n📝 修复内容:")
        print("1. ✅ 配色范围限制：只调整红框范围内的内容")
        print("2. ✅ 实体颜色变化：图纸上的实体颜色随配色方案改变")
        print("3. ✅ 立即视图更新：点击应用配色后立即更新视图显示")
        print("4. ✅ 索引图轮廓：每个颜色方块增加轮廓，避免与背景色混淆")
        print("5. ✅ 高亮颜色：配色方案中增加高亮显示的颜色调整")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    print("=" * 80)
