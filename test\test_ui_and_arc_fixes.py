#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI和圆弧修复
"""

import sys
import os

def test_fill_button_ui_improvements():
    """测试填充按钮UI改进"""
    try:
        print("测试填充按钮UI改进:")
        
        # 检查代码中是否包含了正确的填充按钮UI改进
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("填充状态符号", "●填充"),
            ("可填充符号", "□填充"),
            ("待标注符号", "▢填充"),
            ("颜色符号获取", "_get_color_symbol"),
            ("填充列颜色设置", "_set_fill_column_color"),
            ("填充标签获取", "_get_fill_tag"),
            ("已填充清除确认", "是否清除填充"),
            ("实体类型符号", "color_symbols"),
            ("墙体符号", "'wall': '■'"),
            ("门窗符号", "'door_window': '●'")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 填充按钮UI改进检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 填充按钮UI改进测试失败: {e}")
        return False

def test_arc_mirroring_fix():
    """测试圆弧镜像修复"""
    try:
        print("测试圆弧镜像修复:")
        
        # 检查代码中是否包含了圆弧镜像修复
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("镜像检查", "_check_arc_mirrored"),
            ("镜像角度修复", "_fix_mirrored_arc_angles"),
            ("镜像状态检查", "is_mirrored"),
            ("变换矩阵检查", "transform_matrix"),
            ("行列式计算", "det = matrix"),
            ("角度交换", "fixed_start = end_angle"),
            ("角度补角方法", "360 - end_angle"),
            ("圆心对称方法", "center_angle"),
            ("镜像标记检查", "'mirrored' in entity"),
            ("角度差值检查", "angle_diff")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 圆弧镜像修复检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧镜像修复测试失败: {e}")
        return False

def test_fill_button_functionality():
    """测试填充按钮功能"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试填充按钮功能:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查新方法是否存在
        methods = [
            '_get_fill_tag',
            '_set_fill_column_color',
            '_get_color_symbol'
        ]
        
        for method in methods:
            if hasattr(app, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                root.destroy()
                return False
        
        # 测试颜色符号获取
        test_labels = ['wall', 'door_window', 'furniture', 'other']
        for label in test_labels:
            symbol = app._get_color_symbol(label)
            if symbol:
                print(f"✓ 标签 {label} 的符号: {symbol}")
            else:
                print(f"✗ 标签 {label} 没有符号")
                root.destroy()
                return False
        
        root.destroy()
        print("✓ 填充按钮功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 填充按钮功能测试失败: {e}")
        return False

def test_arc_visualization_functionality():
    """测试圆弧可视化功能"""
    try:
        from cad_visualizer import CADVisualizer
        
        print("测试圆弧可视化功能:")
        
        visualizer = CADVisualizer()
        
        # 检查新方法是否存在
        methods = [
            '_check_arc_mirrored',
            '_fix_mirrored_arc_angles'
        ]
        
        for method in methods:
            if hasattr(visualizer, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                return False
        
        # 测试镜像检查功能
        test_entities = [
            {'mirrored': True},
            {'transform_matrix': [[-1, 0], [0, 1]]},  # 镜像矩阵
            {'start_angle': 0, 'end_angle': 90},      # 正常圆弧
            {}                                        # 空实体
        ]
        
        for i, entity in enumerate(test_entities):
            is_mirrored = visualizer._check_arc_mirrored(entity)
            print(f"✓ 实体 {i+1} 镜像检查: {is_mirrored}")
        
        # 测试角度修复功能
        test_angles = [
            (0, 90),
            (270, 90),
            (180, 0),
            (45, 315)
        ]
        
        for start, end in test_angles:
            fixed_start, fixed_end = visualizer._fix_mirrored_arc_angles(start, end)
            print(f"✓ 角度修复: ({start}, {end}) -> ({fixed_start}, {fixed_end})")
        
        print("✓ 圆弧可视化功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧可视化功能测试失败: {e}")
        return False

def test_ui_symbol_display():
    """测试UI符号显示"""
    try:
        print("测试UI符号显示:")
        
        # 检查符号定义
        symbols = {
            'wall': '■',        # 墙体：黑色方块
            'door_window': '●', # 门窗：红色圆点
            'railing': '▲',     # 栏杆：绿色三角
            'furniture': '♦',   # 家具：蓝色菱形
            'bed': '♥',         # 床：紫色心形
            'sofa': '★',        # 沙发：黄色星形
            'cabinet': '◆',     # 柜子：青色菱形
            'dining_table': '♠', # 餐桌：紫色黑桃
            'appliance': '◉',   # 电器：橙色圆点
            'stair': '▼',       # 楼梯：灰色下三角
            'elevator': '■',    # 电梯：红色方块
            'dimension': '◊',   # 尺寸：绿色空菱形
            'room_label': '○',  # 房间标签：蓝色空圆
            'column': '▪',      # 柱子：黄色小方块
            'other': '□'        # 其他：空方块
        }
        
        print(f"✓ 定义了 {len(symbols)} 种实体类型符号:")
        for label, symbol in symbols.items():
            print(f"  {label}: {symbol}")
        
        # 检查状态符号
        status_symbols = {
            '已填充': '●',
            '可填充': '□',
            '待标注': '▢'
        }
        
        print(f"✓ 定义了 {len(status_symbols)} 种状态符号:")
        for status, symbol in status_symbols.items():
            print(f"  {status}: {symbol}")
        
        print("✓ UI符号显示检查通过")
        return True
        
    except Exception as e:
        print(f"✗ UI符号显示测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    try:
        print("测试错误处理:")
        
        # 检查main_enhanced.py的错误处理
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # 检查cad_visualizer.py的错误处理
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            viz_content = f.read()
        
        # 统计异常处理
        main_exceptions = main_content.count("except Exception as e:")
        viz_exceptions = viz_content.count("except Exception as e:")
        
        print(f"✓ main_enhanced.py 异常处理数量: {main_exceptions}")
        print(f"✓ cad_visualizer.py 异常处理数量: {viz_exceptions}")
        
        # 检查关键方法的错误处理
        critical_methods = [
            ("_get_color_symbol", main_content),
            ("_set_fill_column_color", main_content),
            ("_check_arc_mirrored", viz_content),
            ("_fix_mirrored_arc_angles", viz_content)
        ]
        
        for method_name, content in critical_methods:
            method_start = content.find(f"def {method_name}")
            if method_start != -1:
                method_end = content.find("def ", method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                method_content = content[method_start:method_end]
                if "except Exception as e:" in method_content:
                    print(f"✓ 方法 {method_name} 有异常处理")
                else:
                    print(f"✗ 方法 {method_name} 缺少异常处理")
                    return False
        
        print("✓ 错误处理检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试UI和圆弧修复...")
    print("=" * 60)
    
    tests = [
        ("填充按钮UI改进", test_fill_button_ui_improvements),
        ("圆弧镜像修复", test_arc_mirroring_fix),
        ("填充按钮功能", test_fill_button_functionality),
        ("圆弧可视化功能", test_arc_visualization_functionality),
        ("UI符号显示", test_ui_symbol_display),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 UI和圆弧修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
