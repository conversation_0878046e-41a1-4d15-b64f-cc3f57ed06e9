#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化测试：验证房间识别UI中的自动识别按钮
"""

def test_ui_components():
    """测试UI组件"""
    print("🚀 开始测试UI组件...")
    print("="*80)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        
        from room_recognition_ui import RoomRecognitionUI
        from room_recognition_processor import RoomRecognitionProcessor
        print("✅ 模块导入成功")
        
        # 测试处理器创建
        print("\n🔧 测试处理器创建...")
        processor = RoomRecognitionProcessor()
        print("✅ 房间识别处理器创建成功")
        
        # 检查处理器方法
        required_methods = [
            '_identify_rooms',
            '_detect_door_window_wall_intersections',
            '_collect_all_line_geometries',
            '_classify_regions',
            '_classify_single_region'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if hasattr(processor, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法缺失")
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少必要方法: {missing_methods}")
            return False
        
        # 测试UI类
        print("\n🎨 测试UI类...")
        
        # 检查UI类方法
        ui_methods = [
            '_auto_room_recognition',
            '_get_wall_door_data',
            '_show_recognition_summary',
            '_clear_progress_display'
        ]
        
        missing_ui_methods = []
        for method_name in ui_methods:
            if hasattr(RoomRecognitionUI, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法缺失")
                missing_ui_methods.append(method_name)
        
        if missing_ui_methods:
            print(f"❌ 缺少必要UI方法: {missing_ui_methods}")
            return False
        
        # 测试房间类型定义
        print("\n🏠 测试房间类型定义...")
        
        expected_room_types = [
            '客厅', '卧室', '阳台', '厨房', '卫生间', 
            '杂物间', '其他房间', '设备平台', '墙体空腔'
        ]
        
        if hasattr(processor, 'room_types'):
            actual_types = processor.room_types
            missing_types = []
            for expected_type in expected_room_types:
                if expected_type in actual_types:
                    print(f"   ✅ {expected_type}")
                else:
                    print(f"   ❌ {expected_type} 缺失")
                    missing_types.append(expected_type)
            
            if missing_types:
                print(f"⚠️ 缺少房间类型: {missing_types}")
            else:
                print("✅ 所有房间类型都已定义")
        else:
            print("❌ 房间类型定义不存在")
            return False
        
        # 测试分类规则
        print("\n📏 测试分类规则...")
        
        from shapely.geometry import Polygon
        
        # 创建测试多边形
        test_cases = [
            # (多边形, 期望类型, 描述)
            (Polygon([(0, 0), (500, 0), (500, 1000), (0, 1000)]), '设备平台', '宽度500'),
            (Polygon([(0, 0), (1300, 0), (1300, 2000), (0, 2000)]), '阳台', '宽度1300'),
            (Polygon([(0, 0), (2000, 0), (2000, 3000), (0, 3000)]), '卫生间', '宽度2000'),
            (Polygon([(0, 0), (3000, 0), (3000, 4000), (0, 4000)]), '卧室', '宽度3000'),
        ]
        
        classification_correct = 0
        for polygon, expected_type, description in test_cases:
            try:
                result = processor._classify_single_region(polygon)
                actual_type = result['type']
                
                if actual_type == expected_type:
                    print(f"   ✅ {description}: {expected_type}")
                    classification_correct += 1
                else:
                    print(f"   ❌ {description}: 期望 {expected_type}, 实际 {actual_type}")
            except Exception as e:
                print(f"   ❌ {description}: 分类失败 - {e}")
        
        print(f"📊 分类规则测试: {classification_correct}/{len(test_cases)} 正确")
        
        # 测试临时线条生成
        print("\n🔗 测试临时线条生成...")
        
        # 创建模拟数据
        processor.wall_groups = [[
            {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 1000, 'y': 0}},
            {'type': 'LINE', 'start': {'x': 1000, 'y': 0}, 'end': {'x': 1000, 'y': 1000}},
        ]]
        
        processor.door_window_groups = [[
            {'type': 'LINE', 'start': {'x': 500, 'y': 0}, 'end': {'x': 700, 'y': 0}},
        ]]
        
        try:
            temp_lines = processor._detect_door_window_wall_intersections()
            if temp_lines is not None:
                print(f"   ✅ 临时线条生成: {len(temp_lines)} 条")
            else:
                print("   ⚠️ 临时线条生成返回None")
        except Exception as e:
            print(f"   ❌ 临时线条生成失败: {e}")
        
        # 计算总体成功率
        total_tests = 5  # 导入、处理器、UI、类型、分类
        success_count = 0
        
        if not missing_methods:
            success_count += 1
        if not missing_ui_methods:
            success_count += 1
        if hasattr(processor, 'room_types'):
            success_count += 1
        if classification_correct >= len(test_cases) * 0.75:  # 75%正确率
            success_count += 1
        if temp_lines is not None:
            success_count += 1
        
        print(f"\n🎯 测试通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
        
        return success_count >= 4  # 至少4/5通过
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 房间识别UI组件测试")
    print("="*80)
    
    try:
        success = test_ui_components()
        
        print("\n" + "="*80)
        print("📊 测试总结:")
        
        if success:
            print("🎉 UI组件测试成功！")
            print("\n💡 验证的功能:")
            print("   1. ✅ 模块导入正常")
            print("   2. ✅ 处理器方法完整")
            print("   3. ✅ UI方法完整")
            print("   4. ✅ 房间类型定义完整")
            print("   5. ✅ 分类规则正确")
            print("   6. ✅ 临时线条生成正常")
            
            print("\n🎯 新增的自动识别功能:")
            print("   - 🤖 自动房间识别按钮")
            print("   - 📊 完整的数据获取流程")
            print("   - 🔄 进度显示和错误处理")
            print("   - 📋 识别结果统计和显示")
            print("   - 🎨 UI布局优化")
        else:
            print("❌ UI组件测试失败")
            print("💡 请检查缺失的方法和功能")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
