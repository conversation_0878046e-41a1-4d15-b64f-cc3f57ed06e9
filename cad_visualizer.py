import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Polygon as MplPolygon, Rectangle, Circle, Ellipse as MplEllipse, Arc
import numpy as np
import math

class CADVisualizer:
    """专门处理可视化逻辑的模块"""

    def __init__(self):
        # 显示控制器集成
        self.display_controller = None

        # 设置中文字体
        self.chinese_font = None
        try:
            self.chinese_font = fm.FontProperties(fname='C:/Windows/Fonts/simhei.ttf')
        except:
            try:
                self.chinese_font = fm.FontProperties(fname='C:/Windows/Fonts/msyh.ttc')
            except:
                pass
        
        # 初始化图形
        plt.ioff()  # 关闭交互模式，使用Tkinter控制
        self.fig, (self.ax_detail, self.ax_overview) = plt.subplots(1, 2, figsize=(12, 6))
        self.fig.patch.set_facecolor('#f0f0f0')
        self.ax_detail.set_facecolor('#f5f5f5')
        self.ax_overview.set_facecolor('#f8f8f8')
        
        # 设置坐标轴标签
        self.ax_detail.set_xlabel('X坐标', fontproperties=self.chinese_font)
        self.ax_detail.set_ylabel('Y坐标', fontproperties=self.chinese_font)
        self.ax_overview.set_xlabel('X坐标', fontproperties=self.chinese_font)
        self.ax_overview.set_ylabel('Y坐标', fontproperties=self.chinese_font)
        
        self.fig.tight_layout()

        # 默认配色方案
        self.color_scheme = {
            'background': '#FFFFFF',
            'wall': '#000000',
            'door_window': '#FF0000',
            'railing': '#00FF00',
            'furniture': '#0000FF',
            'bed': '#FF00FF',
            'sofa': '#FFFF00',
            'cabinet': '#00FFFF',
            'dining_table': '#800080',
            'appliance': '#FFA500',
            'stair': '#808080',
            'elevator': '#800000',
            'dimension': '#008000',
            'room_label': '#000080',
            'column': '#808000',
            'other': '#C0C0C0',
            'fill': '#E0E0E0',
            'text': '#000000',
            'current_group': '#FF0000',
            'labeled_group': '#00FF00'
        }

        # 颜色映射
        self.type_colors = {
            'LINE': '#1f77b4',
            'LWPOLYLINE': '#ff7f0e',
            'POLYLINE': '#ff7f0e',
            'CIRCLE': '#2ca02c',
            'ARC': '#d62728',
            'ELLIPSE': '#9467bd',
            'INSERT': '#8c564b',
            'TEXT': '#e377c2',
            'MTEXT': '#e377c2',
            'DIMENSION': '#7f7f7f'
        }
        
        # 更新颜色映射：根据用户要求设置不同类别的颜色
        self.category_colors = {
            'wall': '#FFA500',        # 墙体为橘黄色
            'door': '#42A5F5',        # 门为蓝色
            'door_window': '#42A5F5', # 门窗为蓝色
            'window': '#29B6F6',      # 窗户为浅蓝色
            'railing': '#29B6F6',     # 栏杆为浅蓝色
            'furniture': '#4DB6AC',   # 家具为青绿色
            'bed': '#80CBC4',         # 床为浅青绿色
            'sofa': '#26A69A',        # 沙发为深青绿色
            'cabinet': '#00796B',     # 柜子为深绿色
            'dining_table': '#B2DFDB', # 餐桌为浅绿色
            'appliance': '#607D8B',   # 家电为蓝灰色
            'stair': '#F9A825',       # 楼梯为橙色
            'elevator': '#F57F17',    # 电梯为深橙色
            'dimension': '#FFCA28',   # 标注为黄色
            'room_label': '#EF5350',  # 房间标注为红色
            'column': '#5D4037',      # 柱子为棕色
            'title_block': '#a0522d', # 图框为棕色
            'other': '#BDBDBD'        # 其他为灰色
        }

    def set_display_controller(self, display_controller):
        """设置显示控制器"""
        self.display_controller = display_controller
        print("✅ 可视化器已集成显示控制器")

    def visualize_entity_group(self, entity_group, category_mapping=None):
        """可视化实体组"""
        print(f"🎨 可视化器：开始绘制实体组")
        print(f"  - 实体数量: {len(entity_group) if entity_group else 0}")

        self.ax_detail.clear()

        # 检查实体组是否为空
        if not entity_group:
            print(f"  - ⚠️ 实体组为空，显示空状态")
            self.ax_detail.text(0.5, 0.5, '没有实体数据',
                               ha='center', va='center', transform=self.ax_detail.transAxes,
                               fontproperties=self.chinese_font, fontsize=14)
            self.ax_detail.set_title('CAD实体组预览 (空)', fontsize=12, fontproperties=self.chinese_font)
            return
        
        # 生成统计信息
        layer_stats = {}
        type_stats = {}
        for entity in entity_group:
            layer = entity['layer']
            layer_stats[layer] = layer_stats.get(layer, 0) + 1
            entity_type = entity['type']
            type_stats[entity_type] = type_stats.get(entity_type, 0) + 1
        
        # 创建标题
        title = f"CAD实体组预览 (共{len(entity_group)}个实体)"
        if layer_stats:
            layer_info = f"图层: {', '.join([f'{k}({v})' for k, v in list(layer_stats.items())[:3]])}"
            title += f"\n{layer_info}"
        
        # 添加推断类别信息
        inferred_categories = [entity.get('inferred_category') for entity in entity_group if entity.get('inferred_category')]
        if inferred_categories and category_mapping:
            most_common_category = max(set(inferred_categories), key=inferred_categories.count)
            category_text = category_mapping.get(most_common_category, most_common_category)
            title += f"\n推断类别: {category_text}"
        
        self.ax_detail.set_title(title, fontsize=12, fontproperties=self.chinese_font)
        self.ax_detail.set_aspect('equal')
        self.ax_detail.grid(True, linestyle='--', alpha=0.7)
        
        # 为不同图层使用不同颜色
        layer_colors = {}
        color_palette = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        
        for i, entity in enumerate(entity_group):
            # 根据图层分配颜色
            layer = entity['layer']
            if layer not in layer_colors:
                layer_colors[layer] = color_palette[len(layer_colors) % len(color_palette)]
            color = layer_colors[layer]
            
            # 根据实体类型设置不同颜色和线宽
            if entity['type'] in ['TEXT', 'MTEXT']:
                color = '#d62728'  # 红色
                linewidth = 1.0
                alpha = 0.9
            elif entity['type'] == 'DIMENSION':
                color = '#2ca02c'  # 绿色
                linewidth = 1.0
                alpha = 0.8
            elif entity['type'] == 'INSERT':
                color = '#9467bd'  # 紫色
                linewidth = 1.0
                alpha = 0.9
            elif entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                # 线条类实体使用基础线宽
                linewidth = 1.0
                alpha = 0.9
            else:
                linewidth = 1.0
                alpha = 0.8
            
            self._draw_entity(entity, color, linewidth, alpha, self.ax_detail)
            
            # 在实体旁显示图层名和类别信息
            centroid = self._get_entity_centroid(entity)
            if centroid is not None:
                # 显示图层名
                self.ax_detail.text(centroid[0], centroid[1], layer, 
                            fontsize=6, color=color, alpha=0.7,
                            bbox=dict(facecolor='white', alpha=0.5, edgecolor='none', pad=1))
                
                # 如果实体有标签，显示类别信息
                if 'label' in entity and category_mapping:
                    label_text = category_mapping.get(entity['label'], entity['label'])
                    self.ax_detail.text(centroid[0], centroid[1] + 20, f"类别: {label_text}", 
                                fontsize=8, color='red', alpha=0.8,
                                bbox=dict(facecolor='yellow', alpha=0.7, edgecolor='red', pad=2),
                                fontproperties=self.chinese_font)
        
        # 🔑 关键修复：处理大坐标值的显示问题
        try:
            # 获取当前坐标范围
            self.ax_detail.autoscale_view()

            # 检查坐标范围是否过大
            xlim = self.ax_detail.get_xlim()
            ylim = self.ax_detail.get_ylim()

            x_range = xlim[1] - xlim[0]
            y_range = ylim[1] - ylim[0]

            print(f"  - 坐标范围: X={xlim[0]:.1f}~{xlim[1]:.1f} (范围:{x_range:.1f}), Y={ylim[0]:.1f}~{ylim[1]:.1f} (范围:{y_range:.1f})")

            # 如果坐标范围很小（可能是重叠的点），扩展显示范围
            if x_range < 1.0:
                center_x = (xlim[0] + xlim[1]) / 2
                self.ax_detail.set_xlim(center_x - 50, center_x + 50)
                print(f"  - 扩展X轴显示范围")

            if y_range < 1.0:
                center_y = (ylim[0] + ylim[1]) / 2
                self.ax_detail.set_ylim(center_y - 50, center_y + 50)
                print(f"  - 扩展Y轴显示范围")

            # 强制刷新坐标轴
            self.ax_detail.relim()
            self.ax_detail.autoscale_view(tight=False)

        except Exception as e:
            print(f"  - ⚠️ 坐标范围调整失败: {e}")

        print(f"  - ✅ 实体组绘制完成，共绘制 {len(entity_group)} 个实体")
    
    def visualize_overview(self, all_entities, current_group_entities=None, labeled_entities=None,
                          processor=None, current_group_index=None, wall_fills=None, wall_fill_processor=None, hidden_groups=None):
        """可视化全图概览（增强版 - 支持组状态高亮显示）"""
        print(f"🌍 【增强版】可视化器：开始绘制全图概览")
        print(f"  - 总实体数: {len(all_entities) if all_entities else 0}")
        print(f"  - 当前组实体数: {len(current_group_entities) if current_group_entities else 0}")
        print(f"  - 已标注实体数: {len(labeled_entities) if labeled_entities else 0}")
        print(f"  - 组索引: {current_group_index}")
        print(f"  - 隐藏组数: {len(hidden_groups) if hidden_groups else 0}")

        # 🎨 通知显示控制器全图预览开始更新
        if self.display_controller:
            try:
                from display_controller import DataChangeType
                self.display_controller.notify_data_change(
                    DataChangeType.DXF_FILE_DATA,
                    {
                        'all_entities': all_entities,
                        'current_group': current_group_entities,
                        'labeled_entities': labeled_entities,
                        'wall_fills': wall_fills
                    },
                    "CADVisualizer.visualize_overview"
                )
            except ImportError:
                pass

        if not all_entities:
            print("  ⚠️ 没有实体数据，跳过绘制")
            return

        self.ax_overview.clear()
        self.ax_overview.set_aspect('equal')
        self.ax_overview.grid(True, linestyle='--', alpha=0.3)

        # 🔑 修复1：改进坐标范围计算
        min_x, min_y, max_x, max_y = self._calculate_entities_bounds(all_entities)
        print(f"  - 实体坐标范围: X({min_x:.2f}, {max_x:.2f}), Y({min_y:.2f}, {max_y:.2f})")

        # 🔑 新增：获取处理器信息用于组状态判断
        all_groups = []
        groups_info = []
        if processor:
            all_groups = getattr(processor, 'all_groups', [])
            groups_info = getattr(processor, 'groups_info', [])
            print(f"  - 处理器信息: {len(all_groups)} 个组, {len(groups_info)} 个组状态")

        # 按组状态分类绘制实体
        drawn_count = 0
        color_stats = {}
        group_status_stats = {'current': 0, 'labeled': 0, 'auto_labeled': 0, 'unlabeled': 0}

        # 绘制所有实体，根据组状态和类别选择颜色
        for i, entity in enumerate(all_entities):
            try:
                # 检查实体是否属于隐藏组
                entity_group_index = self._find_entity_group_index(entity, all_groups)
                if hidden_groups and entity_group_index is not None and entity_group_index in hidden_groups:
                    # 跳过隐藏组的实体
                    continue

                # 获取实体颜色和状态（使用增强版方法）
                color, alpha, linewidth, status = self._get_entity_display_info_enhanced(
                    entity, labeled_entities, current_group_entities, all_groups, groups_info, processor)

                # 统计颜色和状态
                color_stats[status] = color_stats.get(status, 0) + 1
                if status in group_status_stats:
                    group_status_stats[status] += 1

                # 绘制实体
                self._draw_entity(entity, color, linewidth, alpha, self.ax_overview)
                drawn_count += 1

            except Exception as e:
                print(f"    ❌ 实体{i+1}处理失败: {e}")

        print(f"  - 成功绘制实体数: {drawn_count}/{len(all_entities)}")
        print(f"  - 颜色分布: {color_stats}")
        print(f"  - 组状态统计: {group_status_stats}")
        # 绘制墙体填充（在实体绘制之后，确保填充在底层）
        # 检查是否有全局的墙体填充数据（用于V2版本）
        global_wall_fills = getattr(self, '_global_wall_fills', None)
        global_wall_fill_processor = getattr(self, '_global_wall_fill_processor', None)
        # 优先使用传入的参数，如果没有则使用全局数据
        effective_wall_fills = wall_fills if wall_fills is not None else global_wall_fills
        effective_wall_fill_processor = wall_fill_processor if wall_fill_processor is not None else global_wall_fill_processor
        if effective_wall_fills and effective_wall_fill_processor:
            try:
                patches_list = effective_wall_fill_processor.create_fill_patches(effective_wall_fills)
                for patch in patches_list:
                    self.ax_overview.add_patch(patch)
            except Exception as e:
                print(f"绘制墙体填充失败: {e}")
                import traceback
                traceback.print_exc()
        # 🔑 修复3：设置正确的坐标范围（改进版）
        if min_x != float('inf') and max_x != float('-inf'):
            # 检查坐标范围是否合理
            x_range = max_x - min_x
            y_range = max_y - min_y

            print(f"  - 计算的坐标范围: X范围={x_range:.2f}, Y范围={y_range:.2f}")

            # 如果坐标范围太大，可能需要使用自动缩放
            if x_range > 100000 or y_range > 100000:
                print(f"  ⚠️ 坐标范围过大，使用自动缩放")
                self.ax_overview.autoscale()
                self.ax_overview.set_aspect('equal', adjustable='box')
            else:
                # 添加10%的边距
                margin_x = max(10, x_range * 0.1)
                margin_y = max(10, y_range * 0.1)

                self.ax_overview.set_xlim(min_x - margin_x, max_x + margin_x)
                self.ax_overview.set_ylim(min_y - margin_y, max_y + margin_y)

                print(f"  - 设置显示范围: X({min_x - margin_x:.2f}, {max_x + margin_x:.2f}), Y({min_y - margin_y:.2f}, {max_y + margin_y:.2f})")
        else:
            print(f"  ⚠️ 无法计算坐标范围，使用自动缩放")
            self.ax_overview.autoscale()
            self.ax_overview.set_aspect('equal', adjustable='box')

        # 🔑 增强：绘制组边界框和标签
        if all_groups and processor:
            self._draw_group_boundaries_enhanced(all_groups, groups_info, current_group_index, processor, hidden_groups)

        # 注释掉重复的高亮显示，因为_draw_group_boundaries_enhanced已经处理了
        # if current_group_entities and current_group_index is not None:
        #     print(f"  - 🔴 开始绘制当前标注组高亮: {len(current_group_entities)} 个实体")
        #     self._highlight_current_annotation_group(current_group_entities, current_group_index)
        #     print(f"  - 高亮当前标注组: 组{current_group_index + 1}")
        # else:
        #     print(f"  - ⚠️ 没有当前标注组，跳过高亮显示")

        # 设置标题（增强版）
        title_parts = ['CAD实体全图概览（增强版）']
        if current_group_index is not None:
            title_parts.append(f'当前标注: 组{current_group_index + 1}')
        if group_status_stats['labeled'] > 0:
            title_parts.append(f'已标注: {group_status_stats["labeled"]}个实体')
        if group_status_stats['auto_labeled'] > 0:
            title_parts.append(f'自动标注: {group_status_stats["auto_labeled"]}个实体')

        title = ' - '.join(title_parts)
        self.ax_overview.set_title(title, fontsize=12, fontproperties=self.chinese_font)
        # 注释掉原来的图例显示，因为现在移动到右侧框了
        # self._create_enhanced_legend(all_entities, group_status_stats, current_group_index)
        # 添加当前处理组索引标记（增强版）
        if current_group_entities:
            bbox = self._get_group_bbox(current_group_entities)
            if bbox:
                center_x = (bbox[0] + bbox[2]) / 2
                center_y = (bbox[1] + bbox[3]) / 2
                # 获取组索引 - 修复版本
                group_index = "?"

                # 方法1：使用传入的组索引参数
                if current_group_index is not None:
                    try:
                        # 确保是数字类型并转换为整数
                        if isinstance(current_group_index, (int, float)):
                            group_index = int(current_group_index)
                        else:
                            # 尝试转换字符串或其他类型
                            group_index = int(str(current_group_index))

                        # 确保索引为正数
                        if group_index <= 0:
                            group_index = 1

                    except (ValueError, TypeError) as e:
                        print(f"组索引转换失败: {current_group_index} (类型: {type(current_group_index)}), 错误: {e}")
                        group_index = "?"

                # 方法2：从processor获取
                if group_index == "?":
                    proc = processor if processor is not None else getattr(self, 'processor', None)
                    if proc:
                        try:
                            # 优先使用processor的当前组索引
                            if hasattr(proc, 'current_group_index') and proc.current_group_index is not None:
                                group_index = proc.current_group_index + 1  # 显示从1开始
                            # 尝试在所有组中查找当前组
                            elif hasattr(proc, 'all_groups') and proc.all_groups:
                                group_index = proc.all_groups.index(current_group_entities) + 1
                        except (ValueError, AttributeError, TypeError) as e:
                            print(f"从processor获取组索引失败: {e}")
                            group_index = "?"

                # 方法3：最后的降级处理
                if group_index == "?":
                    # 使用简单的数字标识
                    group_index = 1
                # 绘制索引标记
                self.ax_overview.text(
                    center_x, center_y, f'组{group_index}',
                    fontproperties=self.chinese_font,
                    fontsize=12, color='red', weight='bold',
                    bbox=dict(facecolor='yellow', alpha=0.7, edgecolor='red')
                )

        # 🔑 修复5：强制刷新画布
        try:
            self.fig.canvas.draw_idle()
            print(f"  ✅ 画布刷新完成")
        except Exception as e:
            print(f"  ⚠️ 画布刷新失败: {e}")

        print(f"  ✅ 全图概览绘制完成（修复版）")

    def _find_entity_group_index(self, entity, all_groups):
        """查找实体所属的组索引"""
        if not all_groups:
            return None

        for group_index, group in enumerate(all_groups):
            if entity in group:
                return group_index

        return None
    
    def highlight_selected_entities(self, group_entities, selected_entities):
        """高亮选中实体"""
        # 重新绘制实体组，高亮选中的实体
        self.ax_detail.clear()
        
        for entity in group_entities:
            if entity in selected_entities:
                # 高亮选中的实体 - 使用更显眼的红色和更粗的线宽
                color = '#ff0000'  # 红色
                if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    linewidth = 4.0  # 线条类实体使用更粗的线宽
                else:
                    linewidth = 3.0
                alpha = 1.0
            else:
                # 普通实体 - 使用更好的默认显示
                color = self.type_colors.get(entity['type'], '#7f7f7f')
                if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    linewidth = 2.0  # 线条类实体基础线宽
                else:
                    linewidth = 1.5
                alpha = 0.6  # 非选中实体透明度降低，突出选中效果
            
            self._draw_entity(entity, color, linewidth, alpha, self.ax_detail)
        
        self.ax_detail.set_title(f"选中 {len(selected_entities)} 个实体", 
                                fontsize=12, fontproperties=self.chinese_font)
        self.ax_detail.set_aspect('equal')
        self.ax_detail.grid(True, linestyle='--', alpha=0.7)
        self.ax_detail.autoscale_view()
    
    def _draw_entity(self, entity, color, linewidth, alpha, ax):
        """绘制单个实体"""
        try:
            if entity['type'] == 'LINE' and 'points' in entity:
                points = entity['points']
                if len(points) >= 2:
                    x = [p[0] for p in points[:2]]
                    y = [p[1] for p in points[:2]]

                    # 🔑 关键修复：检查坐标有效性
                    if all(isinstance(coord, (int, float)) and not (coord != coord) for coord in x + y):  # 检查NaN
                        ax.plot(x, y, color=color, linewidth=linewidth, alpha=alpha)
                    else:
                        print(f"  ⚠️ 跳过无效LINE坐标: {points[:2]}")
        except Exception as e:
            print(f"  ⚠️ LINE绘制失败: {e}")

        if entity['type'] in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity:
            try:
                points = entity['points']
                if points:
                    x = [p[0] for p in points]
                    y = [p[1] for p in points]

                    # 🔑 关键修复：检查坐标有效性
                    if all(isinstance(coord, (int, float)) and not (coord != coord) for coord in x + y):
                        if entity.get('closed', False) and len(points) >= 3:
                            poly = MplPolygon(points, closed=True, fill=False,
                                             edgecolor=color, linewidth=linewidth, alpha=alpha)
                            ax.add_patch(poly)
                        else:
                            ax.plot(x, y, color=color, linewidth=linewidth, marker='o', markersize=3, alpha=alpha)
                    else:
                        print(f"  ⚠️ 跳过无效POLYLINE坐标: {len(points)}个点")
            except Exception as e:
                print(f"  ⚠️ POLYLINE绘制失败: {e}")

        if entity['type'] == 'CIRCLE' and 'center' in entity and 'radius' in entity:
            try:
                center = entity['center']
                radius = entity['radius']

                # 🔑 关键修复：检查坐标和半径有效性
                if (isinstance(center, (list, tuple)) and len(center) >= 2 and
                    all(isinstance(coord, (int, float)) and not (coord != coord) for coord in center[:2]) and
                    isinstance(radius, (int, float)) and radius > 0 and not (radius != radius)):

                    circle = Circle(center[:2], radius,
                                   fill=False, edgecolor=color, linewidth=linewidth, alpha=alpha)
                    ax.add_patch(circle)
                else:
                    print(f"  ⚠️ 跳过无效CIRCLE: center={center}, radius={radius}")
            except Exception as e:
                print(f"  ⚠️ CIRCLE绘制失败: {e}")

        if entity['type'] == 'ARC' and 'center' in entity and 'radius' in entity:
            # 正确绘制圆弧（改进版：精确镜像处理）
            center = entity['center']
            radius = entity['radius']
            # 🔑 关键修复：安全获取角度属性，提供默认值
            start_angle = entity.get('start_angle', 0)
            end_angle = entity.get('end_angle', 360)

            # 角度单位转换：如果角度值很小，可能是弧度制，需要转换为角度制
            if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                start_angle = np.degrees(start_angle)
                end_angle = np.degrees(end_angle)

            # 检查是否是完整圆（360度）
            angle_diff = abs(end_angle - start_angle)
            if abs(angle_diff - 360) < 1e-6 or abs(angle_diff - 2*np.pi) < 1e-6:
                # 完整圆，使用0到360度
                start_angle = 0
                end_angle = 360
            else:
                # 检查圆弧方向和镜像状态
                is_mirrored = self._check_arc_mirrored(entity)

                # 半径计算修正（处理非均匀缩放）
                scale_x = entity.get('scale_x', 1.0)
                scale_y = entity.get('scale_y', 1.0)
                if scale_x != 1.0 or scale_y != 1.0:
                    # 使用几何平均处理非均匀缩放
                    import math
                    radius = abs(radius * math.sqrt(abs(scale_x * scale_y)))

                # 标准化角度到0-360度范围
                start_angle = start_angle % 360
                end_angle = end_angle % 360

                # 处理镜像圆弧的角度
                if is_mirrored:
                    print(f"⚠️ 检测到镜像变换！原始角度: 起点={start_angle:.1f}°, 终点={end_angle:.1f}°")
                    start_angle, end_angle = self._fix_mirrored_arc_angles(start_angle, end_angle)
                    print(f"变换后角度: 起点={start_angle:.1f}°, 终点={end_angle:.1f}°")

                # 处理跨越0度的圆弧
                if end_angle < start_angle:
                    # 如果结束角度小于开始角度，说明圆弧跨越了0度
                    if (start_angle - end_angle) > 180:
                        # 圆弧跨越0度，调整结束角度
                        end_angle += 360
                    else:
                        # 可能是逆时针圆弧，交换角度
                        start_angle, end_angle = end_angle, start_angle

            # 使用matplotlib的Arc patch绘制圆弧（增强错误处理）
            try:
                # 确保参数有效
                if radius <= 0:
                    print(f"⚠️ 圆弧半径无效: {radius}")
                    return

                # 确保角度差异合理
                angle_span = abs(end_angle - start_angle)
                if angle_span > 360:
                    end_angle = start_angle + 360

                arc = Arc(center, 2*radius, 2*radius,
                         theta1=start_angle, theta2=end_angle,
                         edgecolor=color, linewidth=linewidth, alpha=alpha,
                         fill=False)
                ax.add_patch(arc)
            except Exception as e:
                print(f"⚠️ 圆弧绘制失败: {e}")
                print(f"   参数: 中心={center}, 半径={radius}, 角度={start_angle:.1f}°-{end_angle:.1f}°")
                # 降级处理：绘制完整圆形
                try:
                    circle = Circle(center, radius, edgecolor=color, fill=False,
                                  linewidth=linewidth, alpha=alpha)
                    ax.add_patch(circle)
                    print(f"   已降级为完整圆形")
                except Exception as e2:
                    print(f"   圆形降级也失败: {e2}")

        if entity['type'] == 'INSERT' and 'position' in entity:
            position = entity['position']
            rect = Rectangle((position[0]-100, position[1]-100), 200, 200, 
                            edgecolor=color, fill=False, linewidth=linewidth, alpha=alpha)
            ax.add_patch(rect)
            # 添加块名称
            ax.text(position[0], position[1], entity.get('block', 'BLK'), 
                    fontsize=8, ha='center', va='center', color=color,
                    bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))

        if entity['type'] in ['TEXT', 'MTEXT'] and 'position' in entity:
            position = entity['position']
            text = entity.get('text', '')
            height = entity.get('height', 3) * 10  # 缩放文本大小
            ax.text(position[0], position[1], text, 
                    fontsize=min(height, 20),  # 限制最大字体大小
                    color=color, ha='left', va='bottom',
                    bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))

        if entity['type'] == 'DIMENSION' and 'def_points' in entity:
            # 绘制尺寸线
            points = [p for p in entity['def_points'] if p is not None]
            if len(points) >= 2:
                x = [p[0] for p in points]
                y = [p[1] for p in points]
                ax.plot(x, y, 'g--', linewidth=1, alpha=alpha)
            
            # 绘制尺寸文本
            if points:
                text_pos = points[1] if len(points) > 1 else points[0]
                ax.text(text_pos[0], text_pos[1], entity.get('text', 'DIM'), 
                        fontsize=8, ha='center', va='center', color='green',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))
        elif entity['type'] == 'ELLIPSE' and 'center' in entity and 'major_axis' in entity and 'ratio' in entity:
            # 椭圆绘制（改进版：支持镜像处理和数据类型检查）
            try:
                center = entity['center']
                major_axis = entity['major_axis']
                ratio = entity['ratio']
                start_param = entity.get('start_param', 0)
                end_param = entity.get('end_param', 2 * np.pi)

                # 确保数据类型正确
                if isinstance(center, (list, tuple)) and len(center) >= 2:
                    center = [float(center[0]), float(center[1])]
                else:
                    print(f"⚠️ 椭圆center数据无效: {center}")
                    return

                if isinstance(major_axis, (list, tuple)) and len(major_axis) >= 2:
                    major_axis = [float(major_axis[0]), float(major_axis[1])]
                else:
                    print(f"⚠️ 椭圆major_axis数据无效: {major_axis}")
                    return

                ratio = float(ratio)
                start_param = float(start_param)
                end_param = float(end_param)

                # 检查椭圆是否被镜像
                is_mirrored = self._check_arc_mirrored(entity)

                # 计算椭圆参数
                major_axis_array = np.array(major_axis, dtype=float)
                a = float(np.linalg.norm(major_axis_array))
                b = float(a * ratio)
                angle = float(np.arctan2(major_axis_array[1], major_axis_array[0]))
            except (ValueError, TypeError) as e:
                print(f"⚠️ 椭圆数据类型转换失败: {e}")
                print(f"   center: {entity.get('center')}")
                print(f"   major_axis: {entity.get('major_axis')}")
                print(f"   ratio: {entity.get('ratio')}")
                return

            # 处理镜像变换对椭圆的影响
            if is_mirrored:
                print(f"⚠️ 检测到椭圆镜像变换！原始角度: {np.degrees(angle):.1f}°")

                # 半径计算修正（处理非均匀缩放）
                scale_x = entity.get('scale_x', 1.0)
                scale_y = entity.get('scale_y', 1.0)
                if scale_x != 1.0 or scale_y != 1.0:
                    import math
                    # 使用几何平均处理非均匀缩放
                    scale_factor = math.sqrt(abs(scale_x * scale_y))
                    a = abs(a * scale_factor)
                    b = abs(b * scale_factor)

                # 角度校正：反转角度方向
                angle = -angle

                # 参数角度校正
                if abs(end_param - start_param) < 2 * np.pi - 0.1:
                    # 非完整椭圆需要调整参数角度
                    original_start = start_param
                    original_end = end_param

                    # 反转参数角度方向并交换起止角度
                    start_param = 2 * np.pi - original_end
                    end_param = 2 * np.pi - original_start

                    print(f"椭圆弧参数调整: 起点={np.degrees(original_start):.1f}°→{np.degrees(start_param):.1f}°, "
                          f"终点={np.degrees(original_end):.1f}°→{np.degrees(end_param):.1f}°")

                print(f"椭圆变换后角度: {np.degrees(angle):.1f}°")

            # 创建椭圆（增强错误处理）
            try:
                # 确保参数有效
                if a <= 0 or b <= 0:
                    print(f"⚠️ 椭圆轴长无效: a={a}, b={b}")
                    return

                # 限制角度范围
                angle_deg = np.degrees(angle) % 360

                if abs(end_param - start_param) < 2 * np.pi - 0.1:  # 非完整椭圆
                    start_deg = np.degrees(start_param) % 360
                    end_deg = np.degrees(end_param) % 360

                    # 处理跨越角度
                    if end_deg < start_deg:
                        end_deg += 360

                    arc = Arc(center, 2*a, 2*b, angle=angle_deg,
                             theta1=start_deg, theta2=end_deg,
                             edgecolor=color, linewidth=linewidth, alpha=alpha,
                             fill=False)
                    ax.add_patch(arc)
                else:  # 完整椭圆
                    ellipse = MplEllipse(center, 2*a, 2*b, angle=angle_deg,
                                        edgecolor=color, fill=False,
                                        linewidth=linewidth, alpha=alpha)
                    ax.add_patch(ellipse)
            except Exception as e:
                print(f"⚠️ 椭圆绘制失败: {e}")
                print(f"   参数: 中心={center}, a={a:.2f}, b={b:.2f}, 角度={np.degrees(angle):.1f}°")
                # 降级处理：绘制圆形
                try:
                    avg_radius = (a + b) / 2
                    circle = Circle(center, avg_radius, edgecolor=color, fill=False,
                                  linewidth=linewidth, alpha=alpha)
                    ax.add_patch(circle)
                    print(f"   已降级为圆形，半径={avg_radius:.2f}")
                except Exception as e2:
                    print(f"   圆形降级也失败: {e2}")
        elif entity['type'] == 'SPLINE':
            # 优先使用拟合点，其次控制点
            points = []
            if 'fit_points' in entity and entity['fit_points']:
                points = entity['fit_points']
            elif 'control_points' in entity and entity['control_points']:
                points = entity['control_points']
            if points and len(points) >= 2:
                points = np.array(points)
                # 如果点数较多，直接连线；点数较少时用插值平滑
                if len(points) >= 4:
                    try:
                        from scipy.interpolate import splprep, splev
                        tck, _ = splprep([points[:,0], points[:,1]], s=0)
                        unew = np.linspace(0, 1, max(100, len(points)*10))
                        out = splev(unew, tck)
                        ax.plot(out[0], out[1], color=color, linewidth=linewidth, alpha=alpha)
                    except Exception:
                        # 没有scipy时直接折线
                        ax.plot(points[:,0], points[:,1], color=color, linewidth=linewidth, alpha=alpha)
                else:
                    ax.plot(points[:,0], points[:,1], color=color, linewidth=linewidth, alpha=alpha)
    
    def _get_entity_centroid(self, entity):
        """获取实体的质心坐标（增强数据类型检查）"""
        try:
            if 'points' in entity and entity['points']:
                points = entity['points']
                # 确保points是数值类型的列表
                if isinstance(points, list) and len(points) > 0:
                    # 检查并转换数据类型
                    numeric_points = []
                    for point in points:
                        if isinstance(point, (list, tuple)) and len(point) >= 2:
                            try:
                                numeric_point = [float(point[0]), float(point[1])]
                                numeric_points.append(numeric_point)
                            except (ValueError, TypeError):
                                print(f"⚠️ 跳过无效点: {point}")
                                continue

                    if numeric_points:
                        points_array = np.array(numeric_points, dtype=float)
                        centroid = np.mean(points_array, axis=0)
                        return tuple(centroid)

            elif 'center' in entity and entity['center']:
                center = entity['center']
                if isinstance(center, (list, tuple)) and len(center) >= 2:
                    try:
                        return (float(center[0]), float(center[1]))
                    except (ValueError, TypeError):
                        print(f"⚠️ 无效的center数据: {center}")

            elif 'position' in entity and entity['position']:
                position = entity['position']
                if isinstance(position, (list, tuple)) and len(position) >= 2:
                    try:
                        return (float(position[0]), float(position[1]))
                    except (ValueError, TypeError):
                        print(f"⚠️ 无效的position数据: {position}")

            elif 'def_points' in entity and entity['def_points']:
                def_points = entity['def_points']
                if isinstance(def_points, list):
                    numeric_points = []
                    for point in def_points:
                        if point is not None and isinstance(point, (list, tuple)) and len(point) >= 2:
                            try:
                                numeric_point = [float(point[0]), float(point[1])]
                                numeric_points.append(numeric_point)
                            except (ValueError, TypeError):
                                print(f"⚠️ 跳过无效def_point: {point}")
                                continue

                    if numeric_points:
                        points_array = np.array(numeric_points, dtype=float)
                        centroid = np.mean(points_array, axis=0)
                        return tuple(centroid)

        except Exception as e:
            print(f"⚠️ 计算实体质心失败: {e}")
            print(f"   实体类型: {entity.get('type', 'unknown')}")
            print(f"   实体数据: {entity}")

        return None

    def update_color_scheme(self, color_scheme):
        """更新配色方案"""
        if color_scheme:
            self.color_scheme.update(color_scheme)

            # 更新背景色
            if 'background' in color_scheme:
                self.ax_detail.set_facecolor(color_scheme['background'])
                self.ax_overview.set_facecolor(color_scheme['background'])
                self.fig.patch.set_facecolor(color_scheme['background'])

            # 🎨 通知显示控制器配色方案已更新
            if self.display_controller:
                try:
                    # 导入需要的类型
                    from display_controller import DataChangeType
                    self.display_controller.notify_data_change(
                        DataChangeType.COLOR_SCHEME,
                        color_scheme.copy(),
                        "CADVisualizer.update_color_scheme"
                    )
                except ImportError:
                    # 如果显示控制器模块不可用，静默忽略
                    pass

    def get_entity_color(self, entity, category_mapping=None):
        """根据实体类型和标签获取颜色"""
        # 如果实体有标签，使用标签对应的颜色
        if entity.get('label'):
            label = entity['label']
            return self.color_scheme.get(label, self.color_scheme.get('other', '#C0C0C0'))

        # 如果没有标签，使用类型颜色
        entity_type = entity.get('type', 'LINE')
        return self.type_colors.get(entity_type, '#808080')

    def _get_group_bbox(self, group):
        """获取组的边界框"""
        if not group:
            return None
        
        min_x = float('inf')
        min_y = float('inf')
        max_x = float('-inf')
        max_y = float('-inf')
        
        for entity in group:
            centroid = self._get_entity_centroid(entity)
            if centroid:
                min_x = min(min_x, centroid[0])
                min_y = min(min_y, centroid[1])
                max_x = max(max_x, centroid[0])
                max_y = max(max_y, centroid[1])
        
        if min_x != float('inf'):
            return (min_x, min_y, max_x, max_y)
        return None
    
    def update_canvas(self, canvas):
        """更新画布"""
        if canvas:
            canvas.figure = self.fig
            canvas.draw()
    
    def get_figure(self):
        """获取图形对象"""
        return self.fig
    
    def clear_plots(self):
        """清除所有绘图"""
        self.ax_detail.clear()
        self.ax_overview.clear()
        self.fig.tight_layout()
    
    def set_global_wall_fills(self, wall_fills, wall_fill_processor):
        """设置全局墙体填充数据（用于V2版本）"""
        self._global_wall_fills = wall_fills
        self._global_wall_fill_processor = wall_fill_processor
    
    def clear_global_wall_fills(self):
        """清除全局墙体填充数据"""
        if hasattr(self, '_global_wall_fills'):
            delattr(self, '_global_wall_fills')
        if hasattr(self, '_global_wall_fill_processor'):
            delattr(self, '_global_wall_fill_processor')

    def _check_arc_mirrored(self, entity):
        """检查圆弧是否被镜像（改进版：基于缩放因子）"""
        try:
            # 方法1：检查实体是否有镜像标记
            if 'mirrored' in entity:
                return entity['mirrored']

            # 方法2：基于缩放因子检测镜像（推荐方法）
            scale_x, scale_y = self._extract_scale_factors(entity)

            # 通过缩放因子符号检测镜像
            mirror_x = 1 if scale_x >= 0 else -1
            mirror_y = 1 if scale_y >= 0 else -1
            mirror_effect = mirror_x * mirror_y

            # mirror_effect = -1 表示奇数次镜像（需要角度反转）
            if mirror_effect < 0:

                return True

            # 方法3：检查变换矩阵是否包含镜像
            if 'transform_matrix' in entity:
                matrix = entity['transform_matrix']
                # 检查矩阵的行列式，负值表示镜像
                if len(matrix) >= 2 and len(matrix[0]) >= 2:
                    det = matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0]
                    return det < 0

            # 方法4：检查缩放变换
            if 'scale' in entity:
                scale = entity['scale']
                if isinstance(scale, (list, tuple)) and len(scale) >= 2:
                    scale_x, scale_y = scale[0], scale[1]
                    mirror_x = 1 if scale_x >= 0 else -1
                    mirror_y = 1 if scale_y >= 0 else -1
                    mirror_effect = mirror_x * mirror_y
                    return mirror_effect < 0

            return False

        except Exception as e:
            print(f"检查圆弧镜像状态失败: {e}")
            return False

    def _extract_scale_factors(self, entity):
        """提取实体的缩放因子"""
        try:
            scale_x = 1.0
            scale_y = 1.0

            # 方法1：直接从实体属性获取
            if 'scale_x' in entity and 'scale_y' in entity:
                scale_x = entity['scale_x']
                scale_y = entity['scale_y']
            elif 'scale' in entity:
                scale = entity['scale']
                if isinstance(scale, (list, tuple)) and len(scale) >= 2:
                    scale_x, scale_y = scale[0], scale[1]
                elif isinstance(scale, (int, float)):
                    scale_x = scale_y = scale

            # 方法2：从变换矩阵提取
            elif 'transform_matrix' in entity:
                matrix = entity['transform_matrix']
                if len(matrix) >= 2 and len(matrix[0]) >= 2:
                    # 提取缩放因子（矩阵对角元素）
                    scale_x = matrix[0][0]
                    scale_y = matrix[1][1]

            # 方法3：从块引用信息获取
            elif 'block_scale' in entity:
                block_scale = entity['block_scale']
                if isinstance(block_scale, (list, tuple)) and len(block_scale) >= 2:
                    scale_x, scale_y = block_scale[0], block_scale[1]
                elif isinstance(block_scale, (int, float)):
                    scale_x = scale_y = block_scale

            return scale_x, scale_y

        except Exception as e:
            print(f"提取缩放因子失败: {e}")
            return 1.0, 1.0

    def _fix_mirrored_arc_angles(self, start_angle, end_angle):
        """修复镜像圆弧的角度（改进版：基于几何一致性）"""
        try:
            # 角度校正：反转角度方向并交换起止角度
            # 这确保镜像后的圆弧保持正确的几何方向和位置

            # 1. 反转角度方向（顺时针↔逆时针）
            fixed_start = 360 - start_angle
            fixed_end = 360 - end_angle

            # 2. 交换起止角度保持几何一致性
            fixed_start, fixed_end = fixed_end, fixed_start

            # 3. 标准化角度到0-360度范围
            fixed_start = fixed_start % 360
            fixed_end = fixed_end % 360

            # 4. 处理角度跨越问题
            if fixed_end < fixed_start:
                # 如果结束角度小于开始角度，可能需要调整
                angle_span = (start_angle - end_angle) % 360
                if angle_span > 180:
                    # 大角度跨越，调整结束角度
                    fixed_end += 360

            return fixed_start, fixed_end

        except Exception as e:
            print(f"修复镜像圆弧角度失败: {e}")
            return start_angle, end_angle

    def _calculate_entities_bounds(self, entities):
        """计算所有实体的坐标边界（修复版）"""
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')

        for entity in entities:
            coords = self._get_entity_coordinates_fixed(entity)

            for x, y in coords:
                if isinstance(x, (int, float)) and isinstance(y, (int, float)):
                    min_x = min(min_x, x)
                    max_x = max(max_x, x)
                    min_y = min(min_y, y)
                    max_y = max(max_y, y)

        # 如果没有有效坐标，返回默认范围
        if min_x == float('inf'):
            return 0, 0, 100, 100

        return min_x, min_y, max_x, max_y

    def _get_entity_coordinates_fixed(self, entity):
        """获取实体坐标（修复版）"""
        coords = []
        entity_type = entity.get('type', '')

        try:
            if entity_type == 'LINE':
                start = entity.get('start', [0, 0])
                end = entity.get('end', [0, 0])
                coords.extend([start, end])

            elif entity_type == 'CIRCLE':
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                # 添加圆的边界点
                coords.extend([
                    [center[0] - radius, center[1] - radius],
                    [center[0] + radius, center[1] + radius],
                    [center[0], center[1]]  # 中心点
                ])

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                coords.extend(points)

            elif entity_type == 'ARC':
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                coords.extend([
                    [center[0] - radius, center[1] - radius],
                    [center[0] + radius, center[1] + radius],
                    [center[0], center[1]]
                ])

            elif entity_type == 'INSERT':
                insertion_point = entity.get('insertion_point', [0, 0])
                coords.append(insertion_point)

            else:
                # 尝试通用坐标获取
                if 'start' in entity and 'end' in entity:
                    coords.extend([entity['start'], entity['end']])
                elif 'center' in entity:
                    coords.append(entity['center'])
                elif 'points' in entity:
                    coords.extend(entity['points'])
                elif 'insertion_point' in entity:
                    coords.append(entity['insertion_point'])

        except Exception as e:
            print(f"    ⚠️ 获取实体坐标失败: {e}")
            coords = [[0, 0]]  # 返回默认坐标

        return coords

    def _get_entity_color_fixed(self, entity, labeled_entities=None):
        """获取实体颜色（修复版）"""

        # 🔑 关键检查1：实体是否有标签且在颜色映射中
        if entity.get('label') and entity.get('label') in self.category_colors:
            return self.category_colors[entity.get('label')], 0.9, "实体标签"

        # 🔑 关键检查2：自动标注实体
        elif entity.get('auto_labeled') and entity.get('label') and entity.get('label') in self.category_colors:
            return self.category_colors[entity.get('label')], 0.8, "自动标注标签"

        # 🔑 关键检查3：在已标注实体列表中
        elif labeled_entities and entity in labeled_entities:
            return '#00ff00', 0.8, "已标注实体列表"

        else:
            # 未标记实体使用浅灰色
            return '#D3D3D3', 0.7, "默认颜色"

    def _get_entity_display_info_enhanced(self, entity, labeled_entities=None, current_group_entities=None,
                                        all_groups=None, groups_info=None, processor=None):
        """获取实体显示信息（增强版 - 支持组状态判断，修复标签识别问题）"""

        # 默认值
        base_linewidth = 1.0
        if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
            base_linewidth = 1.2

        # 🔧 修复：获取实体标签，处理字符串'None'问题
        entity_label = entity.get('label')
        if entity_label == 'None' or entity_label == 'none':
            entity_label = None

        # 🔧 修复：获取自动标注状态
        auto_labeled = entity.get('auto_labeled', False)



        # 🔑 优先级1：检查是否为当前正在标注的组
        if current_group_entities and entity in current_group_entities:
            # 当前正在标注的组 - 使用红色高亮，加粗显示
            status = 'current'



            if entity_label and entity_label in self.category_colors:
                # 如果已有标签，使用标签颜色但加强显示
                color = self.category_colors[entity_label]

                return color, 1.0, base_linewidth * 2.0, status
            else:
                # 未标注的当前组使用红色
                return '#FF0000', 1.0, base_linewidth * 2.0, status



        # 🔑 优先级2：检查是否为已手动标注的实体（修复版）
        if entity_label and entity_label in self.category_colors and not auto_labeled:
            # 已手动标注的实体使用对应类别颜色，正常透明度
            status = 'labeled'
            color = self.category_colors[entity_label]



            return color, 0.9, base_linewidth, status

        # 🔑 优先级3：检查是否为自动标注的实体（修复版）
        elif auto_labeled and entity_label and entity_label in self.category_colors:
            # 自动标注的实体使用对应类别颜色，稍低透明度
            status = 'auto_labeled'
            color = self.category_colors[entity_label]



            return color, 0.7, base_linewidth * 0.8, status

        # 🔑 优先级4：通过组信息判断实体状态（新增逻辑）
        elif all_groups and groups_info:
            entity_group_status = self._get_entity_group_status(entity, all_groups, groups_info)
            if entity_group_status:
                status, group_label = entity_group_status
                if status == 'labeled' and group_label and group_label in self.category_colors:
                    color = self.category_colors[group_label]



                    return color, 0.9, base_linewidth, status
                elif status == 'auto_labeled' and group_label and group_label in self.category_colors:
                    color = self.category_colors[group_label]



                    return color, 0.7, base_linewidth * 0.8, status

        # 🔑 优先级5：检查是否在已标注实体列表中（兼容性，使用ID匹配）
        elif labeled_entities:
            if self._is_entity_in_labeled_list(entity, labeled_entities):
                status = 'labeled'
                color = '#00FF00'



                return color, 0.8, base_linewidth, status

        # 🔑 优先级6：未标注的实体
        status = 'unlabeled'
        color = '#D3D3D3'



        # 未标注实体使用浅灰色，低透明度
        return color, 0.6, base_linewidth * 0.7, status

    def _draw_entity_enhanced(self, entity, color, linewidth, alpha, ax):
        """绘制实体（增强版）"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE':
                start = entity.get('start', [0, 0])
                end = entity.get('end', [0, 0])
                ax.plot([start[0], end[0]], [start[1], end[1]],
                       color=color, alpha=alpha, linewidth=linewidth)
                return True

            elif entity_type == 'CIRCLE':
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                import matplotlib.patches as patches
                circle = patches.Circle(center, radius, fill=False,
                                      color=color, alpha=alpha, linewidth=linewidth)
                ax.add_patch(circle)
                return True

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                if len(points) >= 2:
                    xs = [p[0] for p in points]
                    ys = [p[1] for p in points]
                    ax.plot(xs, ys, color=color, alpha=alpha, linewidth=linewidth)
                    return True

            elif entity_type == 'ARC':
                # 简化绘制弧形为圆
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                import matplotlib.patches as patches
                circle = patches.Circle(center, radius, fill=False,
                                      color=color, alpha=alpha, linewidth=linewidth)
                ax.add_patch(circle)
                return True

            else:
                # 尝试使用原有的绘制方法
                self._draw_entity(entity, color, linewidth, alpha, ax)
                return True

            return False

        except Exception as e:
            print(f"      绘制实体失败: {e}")
            return False

    def _highlight_current_group(self, group_entities):
        """高亮当前组（修复版）"""
        try:
            for entity in group_entities:
                coords = self._get_entity_coordinates_fixed(entity)
                for x, y in coords:
                    self.ax_overview.plot(x, y, 'ro', markersize=8, alpha=0.7)  # 红色圆点高亮
        except Exception as e:
            print(f"    高亮当前组失败: {e}")

    def _draw_group_boundaries_enhanced(self, all_groups, groups_info, current_group_index, processor, hidden_groups=None):
        """绘制组边界框和状态标识（增强版）"""
        try:
            print(f"  - 🎨 开始绘制组边界框: {len(all_groups)} 个组")

            for i, group in enumerate(all_groups):
                if not group:
                    continue

                # 跳过隐藏组
                if hidden_groups and i in hidden_groups:
                    continue

                # 只显示正在标注的组的边界框和标签
                if i != current_group_index:
                    continue

                # 获取组的边界框
                bbox = self._get_group_bbox(group)
                if not bbox:
                    continue

                min_x, min_y, max_x, max_y = bbox

                # 正在标注组的样式
                border_color = '#FF0000'  # 红色
                border_style = '-'        # 实线
                border_width = 3.0        # 粗线

                # 绘制边界框
                import matplotlib.patches as patches
                rect = patches.Rectangle(
                    (min_x, min_y), max_x - min_x, max_y - min_y,
                    linewidth=border_width, edgecolor=border_color,
                    facecolor='none', linestyle=border_style, alpha=0.8
                )
                self.ax_overview.add_patch(rect)

                # 添加组标签
                center_x = (min_x + max_x) / 2
                center_y = (min_y + max_y) / 2

                # 正在标注组的标签样式
                label_bg = 'yellow'
                label_color = 'red'
                label_text = f'组{i+1}[标注中]'

                # 绘制标签
                self.ax_overview.text(
                    center_x, center_y, label_text,
                    fontproperties=self.chinese_font, fontsize=10,
                    color=label_color, weight='bold',
                    bbox=dict(facecolor=label_bg, alpha=0.8, edgecolor=border_color, linewidth=1),
                    ha='center', va='center'
                )

            print(f"  - ✅ 组边界框绘制完成")

        except Exception as e:
            print(f"  - ❌ 绘制组边界框失败: {e}")
            import traceback
            traceback.print_exc()

    def _highlight_current_annotation_group(self, group_entities, group_index):
        """高亮当前正在标注的组（增强版）"""
        try:
            print(f"    - 🔴 高亮标注组{group_index + 1}: {len(group_entities)} 个实体")

            # 获取组的边界框
            bbox = self._get_group_bbox(group_entities)
            if bbox:
                min_x, min_y, max_x, max_y = bbox

                # 绘制闪烁效果的边界框
                import matplotlib.patches as patches

                # 外层红色粗边框
                outer_rect = patches.Rectangle(
                    (min_x - 5, min_y - 5), (max_x - min_x) + 10, (max_y - min_y) + 10,
                    linewidth=4.0, edgecolor='#FF0000', facecolor='none',
                    linestyle='-', alpha=0.9
                )
                self.ax_overview.add_patch(outer_rect)

                # 内层黄色细边框
                inner_rect = patches.Rectangle(
                    (min_x - 2, min_y - 2), (max_x - min_x) + 4, (max_y - min_y) + 4,
                    linewidth=2.0, edgecolor='#FFFF00', facecolor='none',
                    linestyle='-', alpha=0.7
                )
                self.ax_overview.add_patch(inner_rect)

                # 在组的四个角添加标记点
                corner_size = 8
                corners = [
                    (min_x, min_y), (max_x, min_y),  # 下方两角
                    (min_x, max_y), (max_x, max_y)   # 上方两角
                ]

                for x, y in corners:
                    self.ax_overview.plot(x, y, 'ro', markersize=corner_size, alpha=0.9)
                    self.ax_overview.plot(x, y, 'yo', markersize=corner_size-2, alpha=0.7)

            print(f"    - ✅ 标注组高亮完成")

        except Exception as e:
            print(f"    - ❌ 高亮标注组失败: {e}")

    def _create_enhanced_legend(self, all_entities, group_status_stats, current_group_index):
        """创建增强版图例（包含组状态和实体类别）"""
        try:
            import matplotlib.patches as mpatches
            legend_patches = []

            # 🔑 第一部分：组状态图例
            if any(count > 0 for count in group_status_stats.values()):
                # 添加组状态标题
                legend_patches.append(mpatches.Patch(color='white', label='━━ 组状态 ━━'))

                # 当前标注组
                if group_status_stats.get('current', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#FF0000', label=f'● 正在标注 ({group_status_stats["current"]}个实体)'
                    ))

                # 已手动标注组
                if group_status_stats.get('labeled', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#00AA00', label=f'● 已标注 ({group_status_stats["labeled"]}个实体)'
                    ))

                # 自动标注组
                if group_status_stats.get('auto_labeled', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#0066CC', label=f'● 自动标注 ({group_status_stats["auto_labeled"]}个实体)'
                    ))

                # 未标注组
                if group_status_stats.get('unlabeled', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#D3D3D3', label=f'○ 未标注 ({group_status_stats["unlabeled"]}个实体)'
                    ))

            # 🔑 第二部分：实体类别图例
            existing_categories = set()
            for entity in all_entities:
                label = entity.get('label')
                if label and label in self.category_colors:
                    existing_categories.add(label)

            if existing_categories:
                # 添加类别标题
                legend_patches.append(mpatches.Patch(color='white', label='━━ 实体类别 ━━'))

                # 类别名称映射（简化版，避免字体问题）
                category_names = {
                    'wall': '■ 墙体',
                    'door': '□ 门',
                    'door_window': '□ 门窗',
                    'window': '□ 窗户',
                    'railing': '▬ 栏杆',
                    'furniture': '▪ 家具',
                    'bed': '▪ 床',
                    'sofa': '▪ 沙发',
                    'cabinet': '▪ 柜子',
                    'dining_table': '▪ 餐桌',
                    'appliance': '▫ 家电',
                    'stair': '▲ 楼梯',
                    'elevator': '▲ 电梯',
                    'dimension': '─ 标注',
                    'room_label': '○ 房间',
                    'column': '● 柱子',
                    'title_block': '□ 图框',
                    'other': '? 其他'
                }

                # 按字母顺序排序类别
                sorted_categories = sorted(existing_categories)
                for cat in sorted_categories:
                    legend_patches.append(mpatches.Patch(
                        color=self.category_colors[cat],
                        label=category_names.get(cat, cat)
                    ))

            # 显示图例
            if legend_patches:
                # 分两列显示图例以节省空间
                ncol = 2 if len(legend_patches) > 8 else 1
                legend = self.ax_overview.legend(
                    handles=legend_patches,
                    loc='upper right',
                    fontsize=8,
                    prop=self.chinese_font,
                    ncol=ncol,
                    framealpha=0.9,
                    fancybox=True,
                    shadow=True
                )

                # 设置图例边框
                legend.get_frame().set_facecolor('white')
                legend.get_frame().set_edgecolor('gray')
                legend.get_frame().set_linewidth(1)

                print(f"  - ✅ 增强图例创建完成: {len(legend_patches)} 个项目")

        except Exception as e:
            print(f"  - ❌ 创建增强图例失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_entity_group_status(self, entity, all_groups, groups_info):
        """通过组信息获取实体状态（修复版）"""
        try:
            # 查找实体所属的组
            for group_index, group in enumerate(all_groups):
                if entity in group:
                    # 获取组状态信息
                    if group_index < len(groups_info) and groups_info[group_index]:
                        group_info = groups_info[group_index]
                        status = group_info.get('status', 'unlabeled')
                        label = group_info.get('label')

                        # 转换状态
                        if status in ['labeled', 'completed']:
                            return ('labeled', label)
                        elif status == 'auto_labeled':
                            return ('auto_labeled', label)

                    # 如果没有组信息，从组内第一个实体获取
                    if group:
                        first_entity = group[0]
                        first_label = first_entity.get('label')
                        if first_label and first_label != 'None' and first_label != 'none':
                            if first_entity.get('auto_labeled', False):
                                return ('auto_labeled', first_label)
                            else:
                                return ('labeled', first_label)
                    break

            return None

        except Exception as e:
            print(f"  ⚠️ 获取实体组状态失败: {e}")
            return None

    def _is_entity_in_labeled_list(self, entity, labeled_entities):
        """检查实体是否在已标注列表中（使用多种匹配方式）"""
        try:
            # 方法1：直接引用匹配
            if entity in labeled_entities:
                return True

            # 方法2：ID匹配
            entity_id = id(entity)
            for labeled_entity in labeled_entities:
                if id(labeled_entity) == entity_id:
                    return True

            # 方法3：属性匹配（作为最后手段）
            entity_type = entity.get('type')
            entity_layer = entity.get('layer')
            entity_points = entity.get('points', [])

            if entity_type and entity_layer:
                for labeled_entity in labeled_entities:
                    if (labeled_entity.get('type') == entity_type and
                        labeled_entity.get('layer') == entity_layer and
                        labeled_entity.get('points', []) == entity_points):
                        return True

            return False

        except Exception as e:
            print(f"  ⚠️ 检查实体是否在已标注列表失败: {e}")
            return False












