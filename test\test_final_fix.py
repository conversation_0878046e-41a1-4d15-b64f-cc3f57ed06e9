#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证测试
"""

import sys
import os
import tempfile
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_no_files_scenario():
    """测试没有CAD文件的场景"""
    print("=== 测试没有CAD文件的场景 ===")
    
    # 创建只有非CAD文件的文件夹
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建非CAD文件
        non_cad_files = ['readme.txt', 'image.jpg', 'document.pdf', 'data.csv']
        for file_name in non_cad_files:
            file_path = os.path.join(temp_dir, file_name)
            with open(file_path, 'w') as f:
                f.write(f"Test file: {file_name}")
        
        try:
            from main_enhanced_with_v2_fill import EnhancedCADAppV2
            
            root = tk.Tk()
            root.withdraw()
            
            app = EnhancedCADAppV2(root)
            
            # 设置文件夹并扫描
            app.current_folder = temp_dir
            app._scan_folder_files()
            
            # 检查结果
            if len(app.all_files) == 0:
                print("✅ 正确识别：没有CAD文件")
                
                # 测试诊断对话框（不实际显示）
                folder_exists = os.path.exists(app.current_folder)
                is_directory = os.path.isdir(app.current_folder)
                all_files = os.listdir(app.current_folder) if folder_exists and is_directory else []
                
                print(f"文件夹存在: {folder_exists}")
                print(f"是目录: {is_directory}")
                print(f"文件夹中的文件: {all_files}")
                
                return True
            else:
                print(f"❌ 错误：找到了 {len(app.all_files)} 个CAD文件")
                return False
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        finally:
            try:
                root.destroy()
            except:
                pass

def test_with_cad_files():
    """测试有CAD文件的场景"""
    print("\n=== 测试有CAD文件的场景 ===")
    
    # 创建包含CAD文件的文件夹
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建CAD文件和非CAD文件
        test_files = [
            'drawing1.dxf',
            'drawing2.DXF',  # 大写
            'plan.dwg',
            'readme.txt',    # 非CAD文件
            'backup.dxf.bak' # 看起来像DXF但不是
        ]
        
        for file_name in test_files:
            file_path = os.path.join(temp_dir, file_name)
            with open(file_path, 'w') as f:
                f.write(f"Test file: {file_name}")
        
        try:
            from main_enhanced_with_v2_fill import EnhancedCADAppV2
            
            root = tk.Tk()
            root.withdraw()
            
            app = EnhancedCADAppV2(root)
            
            # 设置文件夹并扫描
            app.current_folder = temp_dir
            app._scan_folder_files()
            
            # 检查结果
            expected_cad_files = 3  # drawing1.dxf, drawing2.DXF, plan.dwg
            actual_cad_files = len(app.all_files)
            
            if actual_cad_files == expected_cad_files:
                print(f"✅ 正确找到 {actual_cad_files} 个CAD文件")
                
                # 检查具体文件
                found_files = [os.path.basename(f) for f in app.all_files]
                expected_files = ['drawing1.dxf', 'drawing2.DXF', 'plan.dwg']
                
                all_found = all(f in found_files for f in expected_files)
                if all_found:
                    print("✅ 所有预期的CAD文件都被找到")
                    return True
                else:
                    print(f"❌ 文件匹配错误: 期望 {expected_files}, 实际 {found_files}")
                    return False
            else:
                print(f"❌ 文件数量错误: 期望 {expected_cad_files}, 实际 {actual_cad_files}")
                return False
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        finally:
            try:
                root.destroy()
            except:
                pass

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 测试1: 不存在的文件夹
        app.current_folder = "C:\\不存在的文件夹"
        app._scan_folder_files()
        if len(app.all_files) == 0:
            print("✅ 不存在文件夹处理正确")
        else:
            print("❌ 不存在文件夹处理错误")
            return False
        
        # 测试2: 空文件夹
        with tempfile.TemporaryDirectory() as empty_dir:
            app.current_folder = empty_dir
            app._scan_folder_files()
            if len(app.all_files) == 0:
                print("✅ 空文件夹处理正确")
            else:
                print("❌ 空文件夹处理错误")
                return False
        
        # 测试3: 文件路径而不是文件夹
        import tempfile as tf
        with tf.NamedTemporaryFile(delete=False) as temp_file:
            temp_file_path = temp_file.name
        
        try:
            app.current_folder = temp_file_path
            app._scan_folder_files()
            if len(app.all_files) == 0:
                print("✅ 文件路径处理正确")
            else:
                print("❌ 文件路径处理错误")
                return False
        finally:
            try:
                os.unlink(temp_file_path)
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False
    finally:
        try:
            root.destroy()
        except:
            pass

def main():
    """主测试函数"""
    print("最终修复验证测试")
    print("=" * 50)
    
    tests = [
        ("没有CAD文件场景", test_no_files_scenario),
        ("有CAD文件场景", test_with_cad_files),
        ("边界情况测试", test_edge_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复完成。")
        print("\n📋 修复总结:")
        print("✅ 移除了调试输出")
        print("✅ 添加了详细的诊断对话框")
        print("✅ 改进了错误处理")
        print("✅ 优化了用户体验")
        
        print("\n🔧 用户使用建议:")
        print("1. 如果遇到'未找到DXF文件'错误，会显示详细诊断信息")
        print("2. 诊断信息包含文件夹内容和具体的解决建议")
        print("3. 支持 .dxf 和 .dwg 文件（大小写不敏感）")
        print("4. 自动过滤非CAD文件")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
