#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示增强的重构方法
展示基于完整图形截取的圆弧椭圆重构效果
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Arc, Ellipse as MplEllipse
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_enhanced_reconstruction():
    """演示增强的重构方法"""
    print("🎨 演示增强的重构方法")
    print("📐 基于完整图形截取的圆弧椭圆重构")
    print("=" * 50)
    
    try:
        from arc_ellipse_reconstructor import ArcEllipseReconstructor
        
        # 创建重构器
        reconstructor = ArcEllipseReconstructor(sample_points_count=30)
        
        # 创建测试实体
        test_entities = [
            # 普通圆弧
            {
                'type': 'ARC',
                'center': (50, 50),
                'radius': 25,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'arcs',
                'color': 'red'
            },
            # 跨越0度圆弧
            {
                'type': 'ARC',
                'center': (150, 50),
                'radius': 20,
                'start_angle': 270,
                'end_angle': 45,
                'layer': 'arcs',
                'color': 'red'
            },
            # 镜像圆弧
            {
                'type': 'ARC',
                'center': (250, 50),
                'radius': 22,
                'start_angle': 0,
                'end_angle': 180,
                'scale_x': -1.0,
                'scale_y': 1.0,
                'layer': 'arcs',
                'color': 'red'
            },
            # 椭圆弧
            {
                'type': 'ELLIPSE',
                'center': (100, 150),
                'major_axis': (30, 15),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'layer': 'ellipses',
                'color': 'purple'
            },
            # 镜像椭圆弧
            {
                'type': 'ELLIPSE',
                'center': (200, 150),
                'major_axis': (25, 10),
                'ratio': 0.7,
                'start_param': 0,
                'end_param': 1.5 * np.pi,
                'scale_x': 1.0,
                'scale_y': -1.0,
                'layer': 'ellipses',
                'color': 'purple'
            }
        ]
        
        print("🔄 处理测试实体...")
        
        # 重构实体
        reconstructed_entities = reconstructor.reconstruct_entities(test_entities.copy())
        
        print(f"✅ 重构完成，共处理 {len(reconstructed_entities)} 个实体")
        
        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8))
        fig.suptitle('增强重构方法演示 - 基于完整图形截取', fontsize=16)
        
        # 绘制原始实体
        ax1.set_title('原始实体（可能有显示问题）', fontsize=14)
        ax1.set_aspect('equal')
        ax1.grid(True, alpha=0.3)
        
        for entity in test_entities:
            draw_original_entity(entity, ax1)
        
        # 绘制重构后实体
        ax2.set_title('重构后实体（基于完整图形截取）', fontsize=14)
        ax2.set_aspect('equal')
        ax2.grid(True, alpha=0.3)
        
        for entity in reconstructed_entities:
            draw_reconstructed_entity(entity, ax2)
        
        # 调整视图
        for ax in [ax1, ax2]:
            ax.relim()
            ax.autoscale()
            ax.legend()
        
        plt.tight_layout()
        plt.show()
        
        # 显示重构详情
        print("\n📊 重构详情:")
        for i, entity in enumerate(reconstructed_entities):
            if 'reconstruction_info' in entity:
                info = entity['reconstruction_info']
                original_type = entity['original_type']
                print(f"\n实体 {i+1}: {original_type} -> POLYLINE")
                
                if original_type == 'ARC':
                    print(f"  中心点: {info['center']}")
                    print(f"  半径: {info['radius']:.2f}")
                    print(f"  角度范围: {info['start_angle']:.1f}° - {info['end_angle']:.1f}°")
                    print(f"  起点: [{info['start_point'][0]:.2f}, {info['start_point'][1]:.2f}]")
                    print(f"  终点: [{info['end_point'][0]:.2f}, {info['end_point'][1]:.2f}]")
                elif original_type == 'ELLIPSE':
                    print(f"  中心点: {info['center']}")
                    print(f"  长轴: {info['a']:.2f}, 短轴: {info['b']:.2f}")
                    print(f"  旋转角度: {np.degrees(info['angle']):.1f}°")
                    print(f"  参数范围: {info['start_param']:.2f} - {info['end_param']:.2f}")
                    print(f"  起点: [{info['start_point'][0]:.2f}, {info['start_point'][1]:.2f}]")
                    print(f"  终点: [{info['end_point'][0]:.2f}, {info['end_point'][1]:.2f}]")
                
                print(f"  采样点数: {info['sample_points_count']}")
                print(f"  镜像处理: {'是' if info.get('is_mirrored', False) else '否'}")
        
        print("\n🎉 演示完成！")
        print("\n💡 新方法优势:")
        print("  ✅ 基于完整图形进行精确截取")
        print("  ✅ 通过端点和采样点定位方向")
        print("  ✅ 正确处理镜像和缩放变换")
        print("  ✅ 提供详细的重构信息")
        print("  ✅ 支持复杂的几何变换")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def draw_original_entity(entity, ax):
    """绘制原始实体"""
    entity_type = entity['type']
    color = entity.get('color', 'black')
    
    if entity_type == 'ARC':
        center = entity['center']
        radius = entity['radius']
        start_angle = entity.get('start_angle', 0)
        end_angle = entity.get('end_angle', 360)
        
        # 简化的角度处理
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)
        
        arc = Arc(center, 2*radius, 2*radius,
                 theta1=start_angle, theta2=end_angle,
                 edgecolor=color, linewidth=2, fill=False,
                 label=f'原始{entity_type}')
        ax.add_patch(arc)
    
    elif entity_type == 'ELLIPSE':
        center = entity['center']
        major_axis = entity['major_axis']
        ratio = entity.get('ratio', 1.0)
        
        a = np.linalg.norm(major_axis)
        b = a * ratio
        angle = np.degrees(np.arctan2(major_axis[1], major_axis[0]))
        
        ellipse = MplEllipse(center, 2*a, 2*b, angle=angle,
                           fill=False, edgecolor=color, linewidth=2,
                           label=f'原始{entity_type}')
        ax.add_patch(ellipse)

def draw_reconstructed_entity(entity, ax):
    """绘制重构后实体"""
    if entity['type'] == 'POLYLINE' and 'original_type' in entity:
        points = np.array(entity['points'])
        color = entity.get('color', 'black')
        original_type = entity['original_type']
        
        # 绘制重构的多段线
        ax.plot(points[:, 0], points[:, 1], color=color, linewidth=3, 
               alpha=0.8, label=f'重构{original_type}')
        
        # 标记采样点
        ax.scatter(points[:, 0], points[:, 1], color=color, s=20, alpha=0.6)
        
        # 如果有重构信息，绘制端点
        if 'reconstruction_info' in entity:
            info = entity['reconstruction_info']
            if 'start_point' in info and 'end_point' in info:
                start_point = info['start_point']
                end_point = info['end_point']
                
                # 起点用绿色圆圈标记
                ax.scatter(start_point[0], start_point[1], color='green', s=80, 
                          marker='o', edgecolor='darkgreen', linewidth=2, zorder=10)
                
                # 终点用红色方块标记
                ax.scatter(end_point[0], end_point[1], color='red', s=80, 
                          marker='s', edgecolor='darkred', linewidth=2, zorder=10)

if __name__ == "__main__":
    demo_enhanced_reconstruction()
