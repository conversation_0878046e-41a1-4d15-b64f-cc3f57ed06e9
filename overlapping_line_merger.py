#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重叠线条合并处理器
在DXF处理过程中，对门窗图层上完全重叠的线条进行合并
"""

import math
from collections import defaultdict


class OverlappingLineMerger:
    """重叠线条合并处理器"""
    
    def __init__(self, tolerance=0.1):
        """
        初始化重叠线条合并器
        
        Args:
            tolerance: 位置容差，用于判断点是否重叠
        """
        self.tolerance = tolerance
        
        # 门窗图层模式（与主处理器保持一致）
        self.door_window_layer_patterns = [
            # 中文门窗模式
            '门', '窗', '门窗', '门洞', '窗洞', '开口', '洞口', '门套', '窗套',
            '入口', '出口', '通道口', '门扇', '窗扇',
            # 英文门窗模式
            'door', 'doors', 'window', 'windows', 'opening', 'openings',
            'entrance', 'exit', 'portal', 'aperture',
            # 常见CAD图层命名
            'A-DOOR', 'ADOOR', 'A_DOOR', 'DOOR_', '_DOOR',
            'A-WINDOW', 'AWINDOW', 'A_WINDOW', 'WINDOW_', '_WINDOW',
            'A-OPEN', 'AOPEN', 'OPENING', 'OPENINGS',
            # 专业术语
            'casement', 'sash', 'frame', 'jamb',
            # 图层编号模式
            '01-门', '02-窗', 'L-门', 'L-窗', 'A-门', 'A-窗', 'AR-门', 'AR-窗'
        ]
    
    def process_entities(self, entities):
        """
        处理实体列表，合并门窗图层上的重叠线条
        
        Args:
            entities: 实体列表
            
        Returns:
            处理后的实体列表
        """
        print("🔧 开始重叠线条合并处理...")
        
        # 分离门窗图层和其他图层的实体
        door_window_entities = []
        other_entities = []
        
        for entity in entities:
            if self._is_door_window_layer(entity):
                door_window_entities.append(entity)
            else:
                other_entities.append(entity)
        
        print(f"   门窗图层实体: {len(door_window_entities)} 个")
        print(f"   其他图层实体: {len(other_entities)} 个")
        
        if not door_window_entities:
            print("   无门窗图层实体，跳过重叠线条合并")
            return entities
        
        # 对门窗图层实体进行重叠线条合并
        merged_door_window_entities = self._merge_overlapping_lines(door_window_entities)
        
        # 合并结果
        result_entities = other_entities + merged_door_window_entities
        
        original_count = len(entities)
        merged_count = len(result_entities)
        removed_count = original_count - merged_count
        
        if removed_count > 0:
            print(f"✅ 重叠线条合并完成: {original_count} → {merged_count} 个实体 (合并了 {removed_count} 个重叠线条)")
        else:
            print("✅ 重叠线条合并完成: 未发现重叠线条")
        
        return result_entities
    
    def _is_door_window_layer(self, entity):
        """判断实体是否属于门窗图层"""
        layer = entity.get('layer', '').lower()
        return any(pattern.lower() in layer for pattern in self.door_window_layer_patterns)
    
    def _merge_overlapping_lines(self, entities):
        """合并重叠的线条"""
        # 只处理LINE类型的实体
        line_entities = [e for e in entities if e.get('type') == 'LINE']
        non_line_entities = [e for e in entities if e.get('type') != 'LINE']
        
        if not line_entities:
            return entities
        
        print(f"   处理 {len(line_entities)} 个LINE实体...")
        
        # 按图层分组处理
        layer_groups = defaultdict(list)
        for entity in line_entities:
            layer = entity.get('layer', '')
            layer_groups[layer].append(entity)
        
        merged_lines = []
        total_removed = 0
        
        for layer, layer_entities in layer_groups.items():
            if len(layer_entities) <= 1:
                merged_lines.extend(layer_entities)
                continue
            
            print(f"   处理图层 '{layer}': {len(layer_entities)} 个线条")
            
            # 对该图层的线条进行重叠检测和合并
            layer_merged, layer_removed = self._merge_lines_in_layer(layer_entities)
            merged_lines.extend(layer_merged)
            total_removed += layer_removed
            
            if layer_removed > 0:
                print(f"     图层 '{layer}' 合并了 {layer_removed} 个重叠线条")
        
        # 返回合并后的所有实体
        return non_line_entities + merged_lines
    
    def _merge_lines_in_layer(self, lines):
        """合并单个图层内的重叠线条"""
        if len(lines) <= 1:
            return lines, 0
        
        merged_lines = []
        used_indices = set()
        removed_count = 0
        
        for i, line1 in enumerate(lines):
            if i in used_indices:
                continue
            
            # 查找与当前线条重叠的所有线条
            overlapping_lines = [line1]
            used_indices.add(i)
            
            for j, line2 in enumerate(lines):
                if j <= i or j in used_indices:
                    continue
                
                if self._are_lines_overlapping(line1, line2):
                    overlapping_lines.append(line2)
                    used_indices.add(j)
                    removed_count += 1
            
            # 如果找到重叠线条，选择最优的代表线条
            if len(overlapping_lines) > 1:
                representative_line = self._select_representative_line(overlapping_lines)
                merged_lines.append(representative_line)
            else:
                merged_lines.append(line1)
        
        return merged_lines, removed_count
    
    def _are_lines_overlapping(self, line1, line2):
        """判断两条线是否完全重叠（位置、长度一致）"""
        # 获取线条的起点和终点
        start1, end1 = self._get_line_points(line1)
        start2, end2 = self._get_line_points(line2)
        
        if not all([start1, end1, start2, end2]):
            return False
        
        # 检查两种情况：
        # 1. 起点对起点，终点对终点
        # 2. 起点对终点，终点对起点（方向相反）
        
        case1 = (self._points_equal(start1, start2) and self._points_equal(end1, end2))
        case2 = (self._points_equal(start1, end2) and self._points_equal(end1, start2))
        
        return case1 or case2
    
    def _get_line_points(self, line):
        """获取线条的起点和终点"""
        try:
            if 'start' in line and 'end' in line:
                start = line['start']
                end = line['end']
            elif 'points' in line and len(line['points']) >= 2:
                start = line['points'][0]
                end = line['points'][1]
            else:
                return None, None
            
            # 确保点是(x, y)格式
            if isinstance(start, (list, tuple)) and len(start) >= 2:
                start = (float(start[0]), float(start[1]))
            else:
                return None, None
            
            if isinstance(end, (list, tuple)) and len(end) >= 2:
                end = (float(end[0]), float(end[1]))
            else:
                return None, None
            
            return start, end
            
        except Exception as e:
            print(f"⚠️ 获取线条点坐标失败: {e}")
            return None, None
    
    def _points_equal(self, point1, point2):
        """判断两个点是否相等（在容差范围内）"""
        try:
            x1, y1 = float(point1[0]), float(point1[1])
            x2, y2 = float(point2[0]), float(point2[1])
            
            distance = math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)
            return distance <= self.tolerance
            
        except Exception:
            return False
    
    def _select_representative_line(self, overlapping_lines):
        """从重叠的线条中选择最优的代表线条"""
        if not overlapping_lines:
            return None
        
        if len(overlapping_lines) == 1:
            return overlapping_lines[0]
        
        # 选择策略：
        # 1. 优先选择有更多属性信息的线条
        # 2. 优先选择颜色不是默认值的线条
        # 3. 优先选择线型不是默认值的线条
        # 4. 如果都相同，选择第一个
        
        best_line = overlapping_lines[0]
        best_score = self._calculate_line_quality_score(best_line)
        
        for line in overlapping_lines[1:]:
            score = self._calculate_line_quality_score(line)
            if score > best_score:
                best_line = line
                best_score = score
        
        return best_line
    
    def _calculate_line_quality_score(self, line):
        """计算线条的质量分数，用于选择最优代表线条"""
        score = 0
        
        # 基础分数
        score += 1
        
        # 有颜色信息加分
        color = line.get('color', 256)  # 256是BYLAYER
        if color != 256:
            score += 2
        
        # 有线型信息加分
        linetype = line.get('linetype', 'BYLAYER')
        if linetype and linetype.upper() not in ['BYLAYER', 'CONTINUOUS']:
            score += 2
        
        # 有虚线标记加分
        if line.get('is_dashed', False):
            score += 1
        
        # 有更多属性信息加分
        attribute_count = len([k for k in line.keys() if k not in ['type', 'layer', 'start', 'end', 'points']])
        score += min(attribute_count, 5)  # 最多加5分
        
        return score


def test_overlapping_line_merger():
    """测试重叠线条合并器"""
    print("🧪 测试重叠线条合并器...")
    
    merger = OverlappingLineMerger()
    
    # 创建测试数据
    test_entities = [
        # 门窗图层的重叠线条
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (0, 0),
            'end': (100, 0),
            'color': 1,
            'linetype': 'CONTINUOUS'
        },
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (0, 0),
            'end': (100, 0),  # 完全重叠
            'color': 256,
            'linetype': 'BYLAYER'
        },
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (100, 0),
            'end': (0, 0),  # 方向相反但重叠
            'color': 2,
            'linetype': 'DASHED'
        },
        # 非门窗图层的线条
        {
            'type': 'LINE',
            'layer': 'WALL',
            'start': (0, 0),
            'end': (100, 0),
            'color': 3
        },
        # 门窗图层的非重叠线条
        {
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'start': (200, 0),
            'end': (300, 0),
            'color': 4
        }
    ]
    
    print(f"原始实体数量: {len(test_entities)}")
    
    # 执行合并
    merged_entities = merger.process_entities(test_entities)
    
    print(f"合并后实体数量: {len(merged_entities)}")
    
    # 验证结果
    door_lines = [e for e in merged_entities if e.get('layer') == 'A-DOOR' and e.get('type') == 'LINE']
    print(f"A-DOOR图层线条数量: {len(door_lines)} (期望: 1)")
    
    if len(door_lines) == 1:
        selected_line = door_lines[0]
        print(f"选择的代表线条: 颜色={selected_line.get('color')}, 线型={selected_line.get('linetype')}")
    
    return len(merged_entities) < len(test_entities)


if __name__ == "__main__":
    # 运行测试
    success = test_overlapping_line_merger()
    if success:
        print("✅ 重叠线条合并器测试通过")
    else:
        print("❌ 重叠线条合并器测试失败")
