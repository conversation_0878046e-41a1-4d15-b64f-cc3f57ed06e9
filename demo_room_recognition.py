#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别模块演示脚本
展示房间识别功能的完整演示
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_window():
    """创建演示窗口"""
    root = tk.Tk()
    root.title("房间识别模块演示")
    root.geometry("1000x700")
    root.configure(bg='#f0f0f0')
    
    # 创建主标题
    title_frame = tk.Frame(root, bg='#2c3e50', height=60)
    title_frame.pack(fill='x')
    title_frame.pack_propagate(False)
    
    title_label = tk.Label(title_frame, text="🏠 房间识别模块演示", 
                          font=('Arial', 16, 'bold'), 
                          fg='white', bg='#2c3e50')
    title_label.pack(expand=True)
    
    # 创建说明区域
    info_frame = tk.Frame(root, bg='#ecf0f1', relief='ridge', bd=2)
    info_frame.pack(fill='x', padx=10, pady=5)
    
    info_text = """
📋 功能说明：
• 建筑外轮廓识别：基于墙体组识别建筑外轮廓
• 房间识别：通过墙体和门窗组识别房间区域  
• 房间分类：支持8种房间类型（客厅、卧室、阳台、厨房、卫生间、杂物间、其他房间、设备平台）
• 房间切分功能：基于门窗的房间切分
• 房间类型修改：支持动态修改房间类型
• 可视化显示：房间布局图和颜色编码
• 统计功能：房间数量和面积统计
• 数据导出：房间信息的结构化导出
    """
    
    info_label = tk.Label(info_frame, text=info_text, 
                         font=('Arial', 9), justify='left',
                         bg='#ecf0f1', fg='#2c3e50')
    info_label.pack(anchor='w', padx=10, pady=5)
    
    # 创建演示区域
    demo_frame = tk.Frame(root)
    demo_frame.pack(fill='both', expand=True, padx=10, pady=5)
    
    # 左侧：房间识别UI演示
    left_frame = tk.LabelFrame(demo_frame, text="房间识别界面", 
                              font=('Arial', 10, 'bold'))
    left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
    
    # 右侧：控制和状态
    right_frame = tk.LabelFrame(demo_frame, text="控制面板", 
                               font=('Arial', 10, 'bold'), width=300)
    right_frame.pack(side='right', fill='y', padx=(5, 0))
    right_frame.pack_propagate(False)
    
    try:
        # 导入房间识别模块
        from room_recognition_processor import RoomRecognitionProcessor
        from room_recognition_ui import RoomRecognitionUI
        
        # 创建处理器
        processor = RoomRecognitionProcessor()
        
        # 创建房间识别UI
        room_ui = RoomRecognitionUI(left_frame, processor)
        
        # 创建控制面板
        create_control_panel(right_frame, room_ui, processor)
        
        # 状态标签
        status_frame = tk.Frame(root, bg='#34495e', height=30)
        status_frame.pack(fill='x')
        status_frame.pack_propagate(False)
        
        status_label = tk.Label(status_frame, text="✅ 房间识别模块已成功加载", 
                               font=('Arial', 9), fg='white', bg='#34495e')
        status_label.pack(expand=True)
        
        print("✅ 房间识别演示界面创建成功")
        
    except Exception as e:
        error_label = tk.Label(left_frame, text=f"❌ 房间识别模块加载失败:\n{e}", 
                              font=('Arial', 10), fg='red')
        error_label.pack(expand=True)
        print(f"❌ 演示界面创建失败: {e}")
    
    return root

def create_control_panel(parent, room_ui, processor):
    """创建控制面板"""
    
    # 测试数据按钮
    test_frame = tk.LabelFrame(parent, text="测试数据", font=('Arial', 9, 'bold'))
    test_frame.pack(fill='x', padx=5, pady=5)
    
    def load_simple_room():
        """加载简单房间数据"""
        wall_groups = [
            [
                {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 200, 'y': 0}},
                {'type': 'LINE', 'start': {'x': 200, 'y': 0}, 'end': {'x': 200, 'y': 150}},
                {'type': 'LINE', 'start': {'x': 200, 'y': 150}, 'end': {'x': 0, 'y': 150}},
                {'type': 'LINE', 'start': {'x': 0, 'y': 150}, 'end': {'x': 0, 'y': 0}}
            ]
        ]
        door_groups = [
            [{'type': 'LINE', 'start': {'x': 80, 'y': 0}, 'end': {'x': 120, 'y': 0}}]
        ]
        room_ui.update_with_new_data(wall_groups, door_groups)
        messagebox.showinfo("成功", "简单房间数据已加载")
    
    def load_complex_room():
        """加载复杂房间数据"""
        wall_groups = [
            [
                {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 300, 'y': 0}},
                {'type': 'LINE', 'start': {'x': 300, 'y': 0}, 'end': {'x': 300, 'y': 200}},
                {'type': 'LINE', 'start': {'x': 300, 'y': 200}, 'end': {'x': 0, 'y': 200}},
                {'type': 'LINE', 'start': {'x': 0, 'y': 200}, 'end': {'x': 0, 'y': 0}},
                # 内部分隔墙
                {'type': 'LINE', 'start': {'x': 150, 'y': 0}, 'end': {'x': 150, 'y': 100}},
                {'type': 'LINE', 'start': {'x': 150, 'y': 120}, 'end': {'x': 150, 'y': 200}},
                {'type': 'LINE', 'start': {'x': 0, 'y': 100}, 'end': {'x': 100, 'y': 100}},
                {'type': 'LINE', 'start': {'x': 120, 'y': 100}, 'end': {'x': 300, 'y': 100}}
            ]
        ]
        door_groups = [
            [{'type': 'LINE', 'start': {'x': 100, 'y': 100}, 'end': {'x': 120, 'y': 100}}],
            [{'type': 'LINE', 'start': {'x': 150, 'y': 100}, 'end': {'x': 150, 'y': 120}}],
            [{'type': 'LINE', 'start': {'x': 120, 'y': 0}, 'end': {'x': 180, 'y': 0}}]
        ]
        room_ui.update_with_new_data(wall_groups, door_groups)
        messagebox.showinfo("成功", "复杂房间数据已加载")
    
    tk.Button(test_frame, text="加载简单房间", command=load_simple_room,
              bg='#3498db', fg='white', font=('Arial', 9)).pack(fill='x', pady=2)
    
    tk.Button(test_frame, text="加载复杂房间", command=load_complex_room,
              bg='#e74c3c', fg='white', font=('Arial', 9)).pack(fill='x', pady=2)
    
    # 统计信息
    stats_frame = tk.LabelFrame(parent, text="统计信息", font=('Arial', 9, 'bold'))
    stats_frame.pack(fill='x', padx=5, pady=5)
    
    stats_text = tk.Text(stats_frame, height=8, font=('Arial', 8))
    stats_text.pack(fill='both', expand=True, padx=2, pady=2)
    
    def update_stats():
        """更新统计信息"""
        try:
            stats = processor.get_room_statistics()
            export_data = processor.export_room_data()
            
            stats_text.delete(1.0, tk.END)
            stats_text.insert(tk.END, "📊 房间统计信息:\n")
            stats_text.insert(tk.END, f"总房间数: {export_data.get('total_rooms', 0)}\n")
            stats_text.insert(tk.END, f"总面积: {export_data.get('total_area', 0):.2f}\n\n")
            
            if stats:
                for room_type, count in stats.items():
                    stats_text.insert(tk.END, f"{room_type}: {count}\n")
            
            if export_data.get('rooms'):
                stats_text.insert(tk.END, "\n📋 房间详情:\n")
                for i, room in enumerate(export_data['rooms']):
                    stats_text.insert(tk.END, f"房间{i+1}: {room.get('type', '未知')} "
                                             f"({room.get('area', 0):.1f}㎡)\n")
        except Exception as e:
            stats_text.delete(1.0, tk.END)
            stats_text.insert(tk.END, f"统计信息获取失败: {e}")
    
    tk.Button(stats_frame, text="刷新统计", command=update_stats,
              bg='#27ae60', fg='white', font=('Arial', 9)).pack(fill='x', pady=2)
    
    # 操作按钮
    action_frame = tk.LabelFrame(parent, text="操作", font=('Arial', 9, 'bold'))
    action_frame.pack(fill='x', padx=5, pady=5)
    
    def clear_data():
        """清空数据"""
        room_ui.update_with_new_data([], [])
        stats_text.delete(1.0, tk.END)
        messagebox.showinfo("成功", "数据已清空")
    
    tk.Button(action_frame, text="清空数据", command=clear_data,
              bg='#95a5a6', fg='white', font=('Arial', 9)).pack(fill='x', pady=2)

def main():
    """主函数"""
    print("🚀 启动房间识别模块演示...")
    
    try:
        # 创建演示窗口
        root = create_demo_window()
        
        # 添加关闭处理
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出演示吗？"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        print("✅ 演示窗口已创建，开始运行...")
        print("\n🎯 演示说明:")
        print("1. 点击'加载简单房间'或'加载复杂房间'按钮加载测试数据")
        print("2. 在房间识别界面中查看识别结果")
        print("3. 点击'刷新统计'查看房间统计信息")
        print("4. 可以在房间列表中修改房间类型")
        print("5. 查看房间布局可视化显示")
        
        # 运行演示
        root.mainloop()
        
        print("✅ 房间识别模块演示完成")
        
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
