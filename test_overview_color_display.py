#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CAD实体全图预览中的颜色显示问题

测试内容：
1. 创建4个组（墙体组、门窗组、待标注组1、待标注组2），执行识别和标注过程
2. 测试标注后的颜色在全图预览中的显示
3. 执行配色方案切换，测试全图预览中各个实体的颜色变化
4. 检测标注待标组前，高亮显示的组数量
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import time
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class OverviewColorTester:
    """全图预览颜色显示测试器"""
    
    def __init__(self):
        self.app = None
        self.root = None
        self.test_results = {}
        self.current_test_step = 0
        
    def create_test_entities(self):
        """创建测试实体数据"""
        print("🔧 创建测试实体数据...")
        
        # 墙体组实体
        wall_entities = [
            {
                'type': 'LINE',
                'layer': 'WALL',
                'start': {'x': 0, 'y': 0},
                'end': {'x': 100, 'y': 0},
                'color': 7,
                'linetype': 'CONTINUOUS'
            },
            {
                'type': 'LINE', 
                'layer': 'WALL',
                'start': {'x': 100, 'y': 0},
                'end': {'x': 100, 'y': 100},
                'color': 7,
                'linetype': 'CONTINUOUS'
            },
            {
                'type': 'LINE',
                'layer': 'WALL', 
                'start': {'x': 100, 'y': 100},
                'end': {'x': 0, 'y': 100},
                'color': 7,
                'linetype': 'CONTINUOUS'
            },
            {
                'type': 'LINE',
                'layer': 'WALL',
                'start': {'x': 0, 'y': 100}, 
                'end': {'x': 0, 'y': 0},
                'color': 7,
                'linetype': 'CONTINUOUS'
            }
        ]
        
        # 门窗组实体
        door_window_entities = [
            {
                'type': 'LINE',
                'layer': 'DOOR',
                'start': {'x': 40, 'y': 0},
                'end': {'x': 60, 'y': 0},
                'color': 3,
                'linetype': 'CONTINUOUS'
            },
            {
                'type': 'ARC',
                'layer': 'DOOR',
                'center': {'x': 40, 'y': 0},
                'radius': 20,
                'start_angle': 0,
                'end_angle': 90,
                'color': 3
            }
        ]
        
        # 待标注组1实体（将标注为家具）
        furniture_entities = [
            {
                'type': 'LWPOLYLINE',
                'layer': 'FURNITURE',
                'points': [
                    {'x': 20, 'y': 20},
                    {'x': 40, 'y': 20},
                    {'x': 40, 'y': 40},
                    {'x': 20, 'y': 40}
                ],
                'closed': True,
                'color': 1
            },
            {
                'type': 'CIRCLE',
                'layer': 'FURNITURE',
                'center': {'x': 30, 'y': 30},
                'radius': 5,
                'color': 1
            }
        ]
        
        # 待标注组2实体（将标注为沙发）
        sofa_entities = [
            {
                'type': 'LWPOLYLINE',
                'layer': 'SOFA',
                'points': [
                    {'x': 60, 'y': 60},
                    {'x': 90, 'y': 60},
                    {'x': 90, 'y': 80},
                    {'x': 60, 'y': 80}
                ],
                'closed': True,
                'color': 2
            },
            {
                'type': 'LWPOLYLINE',
                'layer': 'SOFA',
                'points': [
                    {'x': 65, 'y': 65},
                    {'x': 85, 'y': 65},
                    {'x': 85, 'y': 75},
                    {'x': 65, 'y': 75}
                ],
                'closed': True,
                'color': 2
            }
        ]
        
        return {
            'wall_group': wall_entities,
            'door_window_group': door_window_entities,
            'furniture_group': furniture_entities,
            'sofa_group': sofa_entities
        }
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🚀 设置测试环境...")
        
        try:
            from main_enhanced_with_v2_fill import EnhancedCADAppV2
            
            # 创建根窗口
            self.root = tk.Tk()
            self.root.title("全图预览颜色显示测试")
            self.root.geometry("1200x800")
            
            # 创建应用实例
            print("🔄 正在创建应用实例...")
            self.app = EnhancedCADAppV2(self.root)
            print("✅ 应用实例创建成功")
            
            # 创建测试实体
            self.test_entities = self.create_test_entities()
            
            # 模拟加载实体到应用
            self.load_test_entities()
            
            return True
            
        except Exception as e:
            print(f"❌ 设置测试环境失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_test_entities(self):
        """加载测试实体到应用"""
        print("📥 加载测试实体到应用...")
        
        try:
            # 合并所有实体
            all_entities = []
            for group_name, entities in self.test_entities.items():
                for entity in entities:
                    entity['group'] = group_name
                    all_entities.append(entity)
            
            # 设置到应用中
            if hasattr(self.app, 'entities'):
                self.app.entities = all_entities
            else:
                self.app.entities = all_entities
            
            # 初始化组信息
            if not hasattr(self.app, 'entity_groups'):
                self.app.entity_groups = {}
            
            # 设置组信息
            self.app.entity_groups = {
                0: {'entities': self.test_entities['wall_group'], 'label': 'wall', 'status': 'auto_labeled'},
                1: {'entities': self.test_entities['door_window_group'], 'label': 'door_window', 'status': 'auto_labeled'},
                2: {'entities': self.test_entities['furniture_group'], 'label': 'unlabeled', 'status': 'unlabeled'},
                3: {'entities': self.test_entities['sofa_group'], 'label': 'unlabeled', 'status': 'unlabeled'}
            }
            
            print("✅ 测试实体加载完成")
            
        except Exception as e:
            print(f"❌ 加载测试实体失败: {e}")
    
    def test_step_1_initial_recognition(self):
        """测试步骤1：初始识别过程"""
        print("\n" + "="*60)
        print("📋 测试步骤1：初始识别过程")
        print("="*60)
        
        try:
            # 检查初始状态
            print("🔍 检查初始状态...")
            
            # 检查实体组是否正确加载
            if hasattr(self.app, 'entity_groups'):
                group_count = len(self.app.entity_groups)
                print(f"✅ 实体组数量: {group_count}")
                
                for group_id, group_info in self.app.entity_groups.items():
                    label = group_info.get('label', 'unknown')
                    status = group_info.get('status', 'unknown')
                    entity_count = len(group_info.get('entities', []))
                    print(f"   组{group_id}: {label} ({status}) - {entity_count}个实体")
            
            # 检查全图预览中的颜色显示
            self.check_overview_colors("初始状态")
            
            self.test_results['step_1'] = True
            print("✅ 测试步骤1完成")
            
        except Exception as e:
            print(f"❌ 测试步骤1失败: {e}")
            self.test_results['step_1'] = False
    
    def test_step_2_labeling_process(self):
        """测试步骤2：标注过程"""
        print("\n" + "="*60)
        print("📋 测试步骤2：标注过程")
        print("="*60)

        try:
            # 模拟开始标注组2（设置为正在标注状态）
            print("🎯 模拟开始标注组2...")
            if hasattr(self.app, 'entity_groups') and 2 in self.app.entity_groups:
                # 设置当前正在标注的组
                self.app.current_group_index = 2
                self.app.entity_groups[2]['status'] = 'current_group'
                print("✅ 组2设置为正在标注状态")

            # 检查标注前高亮显示的组数量（应该只有1个正在标注的组）
            highlight_count_before = self.count_highlighted_groups()
            print(f"🔍 标注前高亮显示的组数量: {highlight_count_before}")
            self.test_results['highlight_count_before'] = highlight_count_before

            # 模拟标注过程
            print("🏷️ 开始标注过程...")

            # 标注组2为家具
            if hasattr(self.app, 'entity_groups') and 2 in self.app.entity_groups:
                self.app.entity_groups[2]['label'] = 'furniture'
                self.app.entity_groups[2]['status'] = 'manual_labeled'
                print("✅ 组2标注为家具")

                # 强制更新可视化以反映标注变化
                self.force_update_visualization()

            # 切换到标注组3
            print("🎯 切换到标注组3...")
            if hasattr(self.app, 'entity_groups') and 3 in self.app.entity_groups:
                self.app.current_group_index = 3
                self.app.entity_groups[3]['status'] = 'current_group'
                print("✅ 组3设置为正在标注状态")

            # 检查切换后的高亮组数量
            highlight_count_switch = self.count_highlighted_groups()
            print(f"🔍 切换到组3后高亮显示的组数量: {highlight_count_switch}")
            self.test_results['highlight_count_switch'] = highlight_count_switch

            # 标注组3为沙发
            if hasattr(self.app, 'entity_groups') and 3 in self.app.entity_groups:
                self.app.entity_groups[3]['label'] = 'sofa'
                self.app.entity_groups[3]['status'] = 'manual_labeled'
                print("✅ 组3标注为沙发")

                # 强制更新可视化以反映标注变化
                self.force_update_visualization()

            # 清除当前标注组（标注完成）
            self.app.current_group_index = None

            # 检查标注后的颜色显示
            self.check_overview_colors("标注后")

            # 检查标注后高亮显示的组数量
            highlight_count_after = self.count_highlighted_groups()
            print(f"🔍 标注后高亮显示的组数量: {highlight_count_after}")
            self.test_results['highlight_count_after'] = highlight_count_after

            self.test_results['step_2'] = True
            print("✅ 测试步骤2完成")

        except Exception as e:
            print(f"❌ 测试步骤2失败: {e}")
            self.test_results['step_2'] = False
    
    def test_step_3_color_scheme_switch(self):
        """测试步骤3：配色方案切换"""
        print("\n" + "="*60)
        print("📋 测试步骤3：配色方案切换")
        print("="*60)
        
        try:
            # 获取当前配色方案
            current_scheme = self.get_current_color_scheme()
            print(f"🎨 当前配色方案: {current_scheme}")
            
            # 检查切换前的颜色
            colors_before = self.get_overview_entity_colors()
            print("🔍 切换前的实体颜色:")
            for entity_type, color in colors_before.items():
                print(f"   {entity_type}: {color}")
            
            # 执行配色方案切换
            print("🔄 执行配色方案切换...")
            success = self.switch_color_scheme()
            
            if success:
                print("✅ 配色方案切换成功")
                
                # 检查切换后的颜色
                colors_after = self.get_overview_entity_colors()
                print("🔍 切换后的实体颜色:")
                for entity_type, color in colors_after.items():
                    print(f"   {entity_type}: {color}")
                
                # 比较颜色变化
                color_changes = self.compare_color_changes(colors_before, colors_after)
                print("📊 颜色变化分析:")
                for entity_type, change in color_changes.items():
                    print(f"   {entity_type}: {change}")
                
                self.test_results['color_changes'] = color_changes
                
            else:
                print("❌ 配色方案切换失败")
            
            self.test_results['step_3'] = success
            print("✅ 测试步骤3完成")
            
        except Exception as e:
            print(f"❌ 测试步骤3失败: {e}")
            self.test_results['step_3'] = False
    
    def check_overview_colors(self, stage_name):
        """检查全图预览中的颜色显示"""
        print(f"🎨 检查{stage_name}全图预览颜色显示...")
        
        try:
            # 检查是否有可视化器
            if hasattr(self.app, 'visualizer') and self.app.visualizer:
                print("✅ 可视化器存在")
                
                # 检查画布（修复：检查多个可能的画布位置）
                canvas_found = False
                canvas_location = "unknown"

                if hasattr(self.app, 'canvas') and self.app.canvas:
                    canvas_found = True
                    canvas_location = "app.canvas"
                elif hasattr(self.app.visualizer, 'canvas') and self.app.visualizer.canvas:
                    canvas_found = True
                    canvas_location = "visualizer.canvas"
                elif hasattr(self.app.visualizer, 'canvas_overview') and self.app.visualizer.canvas_overview:
                    canvas_found = True
                    canvas_location = "visualizer.canvas_overview"
                elif hasattr(self.app.visualizer, 'canvas_detail') and self.app.visualizer.canvas_detail:
                    canvas_found = True
                    canvas_location = "visualizer.canvas_detail"

                if canvas_found:
                    print(f"✅ 画布存在 (位置: {canvas_location})")

                    # 获取当前显示的实体颜色
                    entity_colors = self.get_overview_entity_colors()

                    print(f"📊 {stage_name}实体颜色统计:")
                    for entity_type, color in entity_colors.items():
                        print(f"   {entity_type}: {color}")

                    self.test_results[f'{stage_name}_colors'] = entity_colors

                else:
                    print("⚠️ 画布不存在")
                    # 调试信息：显示可视化器的属性
                    if hasattr(self.app, 'visualizer') and self.app.visualizer:
                        visualizer_attrs = [attr for attr in dir(self.app.visualizer) if 'canvas' in attr.lower()]
                        print(f"   可视化器画布相关属性: {visualizer_attrs}")
                        app_attrs = [attr for attr in dir(self.app) if 'canvas' in attr.lower()]
                        print(f"   应用画布相关属性: {app_attrs}")
            else:
                print("⚠️ 可视化器不存在")
                
        except Exception as e:
            print(f"❌ 检查{stage_name}颜色显示失败: {e}")
    
    def get_overview_entity_colors(self):
        """获取全图预览中实体的颜色"""
        entity_colors = {}

        try:
            if hasattr(self.app, 'entity_groups'):
                for group_id, group_info in self.app.entity_groups.items():
                    label = group_info.get('label', 'unlabeled')
                    status = group_info.get('status', 'unknown')

                    # 根据标签和状态确定颜色
                    color = self.get_entity_color_by_multiple_methods(group_info, label, status)
                    entity_colors[f"group_{group_id}_{label}"] = color

            # 同时检查可视化器中的实际颜色
            visual_colors = self.get_visualizer_colors()
            if visual_colors:
                entity_colors.update(visual_colors)

        except Exception as e:
            print(f"❌ 获取实体颜色失败: {e}")

        return entity_colors

    def get_entity_color_by_multiple_methods(self, group_info, label, status):
        """通过多种方法获取实体颜色"""
        color = None

        try:
            # 方法1: 使用当前配色方案直接获取（优先级最高，最准确）
            if hasattr(self.app, 'current_color_scheme') and self.app.current_color_scheme:
                if label in self.app.current_color_scheme:
                    return f"current_scheme:{self.app.current_color_scheme[label]}"

            # 方法2: 使用配色方案直接获取（备用）
            if hasattr(self.app, 'color_scheme') and self.app.color_scheme:
                if label in self.app.color_scheme:
                    return f"scheme:{self.app.color_scheme[label]}"

            # 方法3: 使用增强的颜色获取方法
            if hasattr(self.app, '_get_entity_color_from_scheme_enhanced'):
                sample_entity = group_info.get('entities', [{}])[0] if group_info.get('entities') else {}
                # 为实体添加标签信息
                sample_entity['label'] = label
                sample_entity['status'] = status
                color = self.app._get_entity_color_from_scheme_enhanced(sample_entity)
                if color:
                    return f"enhanced:{color}"

            # 方法4: 使用标准颜色获取方法
            if hasattr(self.app, '_get_entity_color_from_scheme'):
                sample_entity = group_info.get('entities', [{}])[0] if group_info.get('entities') else {}
                sample_entity['label'] = label
                sample_entity['status'] = status
                color = self.app._get_entity_color_from_scheme(sample_entity)
                if color:
                    return f"standard:{color}"

            # 方法5: 使用默认颜色逻辑
            default_colors = {
                'wall': '#FF0000',
                'door_window': '#0000FF',
                'furniture': '#00FF00',
                'sofa': '#800080',
                'unlabeled': '#808080'
            }

            if label in default_colors:
                return f"default:{default_colors[label]}"

            return f"fallback:gray"

        except Exception as e:
            print(f"❌ 获取实体颜色失败: {e}")
            return f"error:unknown"

    def analyze_detailed_color_changes(self, colors_before, colors_after):
        """分析详细的颜色变化"""
        print("="*50)

        all_entities = set(colors_before.keys()) | set(colors_after.keys())

        for entity_type in sorted(all_entities):
            before = colors_before.get(entity_type, 'unknown')
            after = colors_after.get(entity_type, 'unknown')

            print(f"{entity_type}:")
            print(f"   配色方案1: {before}")
            print(f"   配色方案2: {after}")

            if before != after:
                print(f"   ✅ 颜色已改变: {before} → {after}")
            else:
                print(f"   ❌ 颜色未改变")
            print()

    def simple_color_scheme_test(self):
        """简化的配色方案切换测试"""
        try:
            print("🧪 执行简化配色方案切换测试...")

            # 检查配色方案是否存在
            if not hasattr(self.app, 'current_color_scheme'):
                print("❌ 配色方案不存在")
                return False

            # 记录初始配色方案
            initial_scheme = dict(self.app.current_color_scheme)
            print(f"📋 初始配色方案: {len(initial_scheme)}个颜色")

            # 创建测试配色方案
            test_scheme = {
                'wall': '#FF0000',
                'door_window': '#0000FF',
                'furniture': '#00FF00',
                'sofa': '#800080'
            }

            # 应用测试配色方案
            self.app.current_color_scheme.update(test_scheme)
            print("✅ 测试配色方案已应用")

            # 验证配色方案是否更新
            changes_detected = 0
            for key, expected_color in test_scheme.items():
                actual_color = self.app.current_color_scheme.get(key)
                if actual_color == expected_color:
                    print(f"   ✅ {key}: {actual_color}")
                    changes_detected += 1
                else:
                    print(f"   ❌ {key}: 期望={expected_color}, 实际={actual_color}")

            success = changes_detected == len(test_scheme)
            print(f"📊 配色方案更新结果: {changes_detected}/{len(test_scheme)} 成功")

            return success

        except Exception as e:
            print(f"❌ 简化配色方案测试失败: {e}")
            return False

    def force_update_visualization(self):
        """强制更新可视化以反映标注变化"""
        try:
            print("🔄 强制更新可视化...")

            # 更新界面
            self.root.update()

            # 如果有处理器，触发可视化更新
            if hasattr(self.app, 'processor') and self.app.processor:
                # 模拟触发可视化更新
                if hasattr(self.app.processor, 'visualize_current_group'):
                    try:
                        self.app.processor.visualize_current_group()
                    except:
                        pass

            # 如果有可视化器，强制重绘
            if hasattr(self.app, 'visualizer') and self.app.visualizer:
                # 尝试多种画布更新方法
                canvas_updated = False

                if hasattr(self.app, 'canvas') and self.app.canvas:
                    self.app.canvas.draw()
                    canvas_updated = True
                    print("   ✅ 更新了 app.canvas")

                if hasattr(self.app.visualizer, 'canvas') and self.app.visualizer.canvas:
                    self.app.visualizer.canvas.draw()
                    canvas_updated = True
                    print("   ✅ 更新了 visualizer.canvas")

                if hasattr(self.app.visualizer, 'canvas_overview') and self.app.visualizer.canvas_overview:
                    self.app.visualizer.canvas_overview.draw()
                    canvas_updated = True
                    print("   ✅ 更新了 visualizer.canvas_overview")

                if not canvas_updated:
                    print("   ⚠️ 未找到可更新的画布")

            # 再次更新界面
            self.root.update()
            time.sleep(0.1)  # 短暂等待确保更新完成

            print("✅ 可视化更新完成")

        except Exception as e:
            print(f"❌ 强制更新可视化失败: {e}")

    def get_visualizer_colors(self):
        """从可视化器中获取实际显示的颜色"""
        visual_colors = {}

        try:
            if hasattr(self.app, 'visualizer') and self.app.visualizer:
                # 检查matplotlib图形对象
                if hasattr(self.app.visualizer, 'ax') and self.app.visualizer.ax:
                    # 获取所有绘制的线条和多边形
                    for i, line in enumerate(self.app.visualizer.ax.lines):
                        color = line.get_color()
                        visual_colors[f"line_{i}"] = f"visual:{color}"

                    for i, patch in enumerate(self.app.visualizer.ax.patches):
                        color = patch.get_facecolor()
                        visual_colors[f"patch_{i}"] = f"visual:{color}"

                # 检查画布
                if hasattr(self.app.visualizer, 'canvas'):
                    visual_colors['canvas_status'] = "visual:active"
                else:
                    visual_colors['canvas_status'] = "visual:inactive"

        except Exception as e:
            print(f"❌ 获取可视化器颜色失败: {e}")

        return visual_colors
    
    def count_highlighted_groups(self):
        """统计高亮显示的组数量"""
        highlighted_count = 0
        highlighted_details = []

        try:
            if hasattr(self.app, 'entity_groups'):
                for group_id, group_info in self.app.entity_groups.items():
                    status = group_info.get('status', 'unknown')
                    label = group_info.get('label', 'unlabeled')
                    is_highlighted = False
                    highlight_reason = ""

                    # 检查各种高亮状态 - 只有正在标注的组才高亮
                    if status in ['current_group', 'manual_group', 'highlighting']:
                        is_highlighted = True
                        highlight_reason = f"status:{status}"
                    elif hasattr(self.app, 'current_group_index') and self.app.current_group_index == group_id:
                        is_highlighted = True
                        highlight_reason = "current_index"
                    # 移除了 unlabeled 自动高亮的逻辑，因为只有正在标注的组才应该高亮

                    if is_highlighted:
                        highlighted_count += 1
                        highlighted_details.append({
                            'group_id': group_id,
                            'label': label,
                            'status': status,
                            'reason': highlight_reason
                        })

            # 输出详细信息
            if highlighted_details:
                print(f"🔍 高亮组详细信息:")
                for detail in highlighted_details:
                    print(f"   组{detail['group_id']}: {detail['label']} ({detail['status']}) - {detail['reason']}")
            else:
                print(f"🔍 当前没有高亮显示的组")

            self.test_results['highlighted_details'] = highlighted_details

        except Exception as e:
            print(f"❌ 统计高亮组失败: {e}")

        return highlighted_count
    
    def get_current_color_scheme(self):
        """获取当前配色方案"""
        try:
            scheme_info = {}

            # 检查 current_color_scheme
            if hasattr(self.app, 'current_color_scheme') and self.app.current_color_scheme:
                scheme_info['current_color_scheme'] = self.app.current_color_scheme

            # 检查 color_scheme
            if hasattr(self.app, 'color_scheme') and self.app.color_scheme:
                scheme_info['color_scheme'] = self.app.color_scheme

            if scheme_info:
                return f"custom: {scheme_info}"
            else:
                return "default"
        except Exception as e:
            return f"error: {e}"
    
    def switch_color_scheme(self):
        """切换配色方案"""
        try:
            # 检查是否有配色切换方法
            if hasattr(self.app, 'apply_color_scheme_v2'):
                # 创建测试配色方案1
                test_scheme_1 = {
                    'wall': '#FF0000',      # 红色
                    'door_window': '#0000FF', # 蓝色
                    'furniture': '#00FF00',   # 绿色
                    'sofa': '#800080',       # 紫色
                    'unlabeled': '#808080',  # 灰色
                    'background': '#FFFFFF', # 白色背景
                    'highlight': '#FFFF00'   # 黄色高亮
                }

                # 设置配色方案到应用（使用正确的属性名）
                print(f"🔧 设置配色方案1: {test_scheme_1}")
                self.app.current_color_scheme.update(test_scheme_1)
                if hasattr(self.app, 'color_scheme'):
                    self.app.color_scheme = test_scheme_1

                print(f"✅ 配色方案1设置完成: {self.app.current_color_scheme}")

                # 应用第一个配色方案
                self.app.apply_color_scheme_v2()
                print("✅ 配色方案1应用成功")

                # 验证配色方案是否真的被设置
                print(f"🔍 验证配色方案1设置结果:")
                for key in ['wall', 'door_window', 'furniture', 'sofa']:
                    expected = test_scheme_1.get(key, 'unknown')
                    actual = self.app.current_color_scheme.get(key, 'unknown')
                    print(f"   {key}: 期望={expected}, 实际={actual}, {'✅' if expected == actual else '❌'}")

                # 等待一下让界面更新
                self.root.update()
                time.sleep(0.5)

                # 检查配色方案1后的颜色
                scheme1_colors = self.get_overview_entity_colors()

                # 创建测试配色方案2（不同的颜色）
                test_scheme_2 = {
                    'wall': '#8B4513',      # 棕色
                    'door_window': '#FF6347', # 番茄红
                    'furniture': '#32CD32',   # 酸橙绿
                    'sofa': '#9370DB',       # 中紫色
                    'unlabeled': '#A9A9A9',  # 深灰色
                    'background': '#F0F8FF', # 爱丽丝蓝背景
                    'highlight': '#FFD700'   # 金色高亮
                }

                # 设置配色方案到应用（使用正确的属性名）
                print(f"🔧 设置配色方案2: {test_scheme_2}")
                self.app.current_color_scheme.update(test_scheme_2)
                if hasattr(self.app, 'color_scheme'):
                    self.app.color_scheme = test_scheme_2

                print(f"✅ 配色方案2设置完成: {self.app.current_color_scheme}")

                # 应用第二个配色方案
                self.app.apply_color_scheme_v2()
                print("✅ 配色方案2应用成功")

                # 验证配色方案是否真的被设置
                print(f"🔍 验证配色方案2设置结果:")
                for key in ['wall', 'door_window', 'furniture', 'sofa']:
                    expected = test_scheme_2.get(key, 'unknown')
                    actual = self.app.current_color_scheme.get(key, 'unknown')
                    print(f"   {key}: 期望={expected}, 实际={actual}, {'✅' if expected == actual else '❌'}")

                # 更新界面
                self.root.update()

                # 检查配色方案2后的颜色
                scheme2_colors = self.get_overview_entity_colors()

                # 分析颜色变化
                print("\n📊 详细颜色变化分析:")
                if scheme1_colors and scheme2_colors:
                    self.analyze_detailed_color_changes(scheme1_colors, scheme2_colors)
                else:
                    print("⚠️ 颜色数据不完整，跳过详细分析")
                    print(f"   配色方案1颜色数量: {len(scheme1_colors) if scheme1_colors else 0}")
                    print(f"   配色方案2颜色数量: {len(scheme2_colors) if scheme2_colors else 0}")

                return True

            else:
                # 备用方案：使用简化测试
                print("🔄 使用备用简化配色方案测试...")
                return self.simple_color_scheme_test()

            elif hasattr(self.app, 'color_scheme_combo'):
                # 尝试通过UI组件切换
                print("🔄 尝试通过UI组件切换配色方案...")

                # 获取配色方案选项
                if hasattr(self.app.color_scheme_combo, 'get'):
                    current_scheme = self.app.color_scheme_combo.get()
                    print(f"当前方案: {current_scheme}")

                    # 获取所有可用方案
                    if hasattr(self.app.color_scheme_combo, 'cget'):
                        values = self.app.color_scheme_combo.cget('values')
                        print(f"可用方案: {values}")

                        # 切换到不同的方案
                        if len(values) > 1:
                            new_scheme = values[1] if values[0] == current_scheme else values[0]
                            self.app.color_scheme_combo.set(new_scheme)

                            # 触发切换事件
                            if hasattr(self.app, 'on_color_scheme_changed'):
                                self.app.on_color_scheme_changed(None)

                            print(f"✅ 切换到方案: {new_scheme}")
                            return True

                print("⚠️ UI组件切换失败")
                return False

            else:
                print("⚠️ 未找到配色切换方法")
                return False

        except Exception as e:
            print(f"❌ 配色方案切换失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def compare_color_changes(self, colors_before, colors_after):
        """比较颜色变化"""
        changes = {}
        
        for entity_type in colors_before:
            before_color = colors_before.get(entity_type, 'unknown')
            after_color = colors_after.get(entity_type, 'unknown')
            
            if before_color != after_color:
                changes[entity_type] = f"{before_color} → {after_color}"
            else:
                changes[entity_type] = "无变化"
        
        return changes
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始全图预览颜色显示测试...")
        print("="*80)
        
        # 设置测试环境
        if not self.setup_test_environment():
            print("❌ 测试环境设置失败，终止测试")
            return False
        
        # 执行测试步骤
        self.test_step_1_initial_recognition()
        self.test_step_2_labeling_process()
        self.test_step_3_color_scheme_switch()
        
        # 生成测试报告
        self.generate_test_report()
        
        return True
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*80)
        print("📊 测试报告")
        print("="*80)
        
        # 测试步骤结果
        print("📋 测试步骤结果:")
        for step, result in self.test_results.items():
            if step.startswith('step_'):
                status = "✅ 通过" if result else "❌ 失败"
                print(f"   {step}: {status}")
        
        # 高亮组数量分析
        if 'highlight_count_before' in self.test_results and 'highlight_count_after' in self.test_results:
            before = self.test_results['highlight_count_before']
            after = self.test_results['highlight_count_after']
            print(f"\n🔍 高亮组数量分析:")
            print(f"   标注前（正在标注组2时）: {before}个")
            if 'highlight_count_switch' in self.test_results:
                switch = self.test_results['highlight_count_switch']
                print(f"   切换到组3时: {switch}个")
            print(f"   标注后（全部完成）: {after}个")
            print(f"   总变化: {after - before}")
            print(f"   ✅ 预期结果: 只有正在标注的组才高亮显示")
        
        # 颜色变化分析
        if 'color_changes' in self.test_results:
            print(f"\n🎨 颜色变化分析:")
            for entity_type, change in self.test_results['color_changes'].items():
                print(f"   {entity_type}: {change}")
        
        # 总体结果
        passed_tests = sum(1 for k, v in self.test_results.items() if k.startswith('step_') and v)
        total_tests = sum(1 for k in self.test_results.keys() if k.startswith('step_'))
        
        print(f"\n📈 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！")
        else:
            print("⚠️ 部分测试失败，请检查问题")
        
        print("="*80)

def main():
    """主函数"""
    tester = OverviewColorTester()
    
    try:
        # 运行测试
        success = tester.run_all_tests()
        
        if success and tester.root:
            # 添加测试完成信息
            info_frame = tk.Frame(tester.root, bg='lightgreen', height=60)
            info_frame.pack(fill='x', side='bottom')
            info_frame.pack_propagate(False)
            
            info_label = tk.Label(info_frame, 
                                 text="✅ 全图预览颜色显示测试完成！请查看控制台输出的详细报告。",
                                 bg='lightgreen', font=('Arial', 12, 'bold'))
            info_label.pack(expand=True)
            
            # 添加关闭按钮
            close_btn = tk.Button(info_frame, text="关闭测试", 
                                 command=tester.root.destroy,
                                 bg='red', fg='white', font=('Arial', 10, 'bold'))
            close_btn.pack(side='right', padx=10, pady=5)
            
            print("\n🖼️ 测试界面已准备就绪，请检查全图预览中的颜色显示")
            print("   关闭窗口以结束测试")
            
            # 运行界面
            tester.root.mainloop()
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if tester.root:
            try:
                tester.root.destroy()
            except:
                pass

if __name__ == "__main__":
    main()
