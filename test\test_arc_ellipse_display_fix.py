#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试圆弧椭圆显示修复
验证修复后各种方案都能正确显示圆弧和椭圆弧，而不是完整的圆和椭圆
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Ellipse as MplEllipse
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_chinese_font():
    """设置中文字体支持"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

setup_chinese_font()

def test_arc_ellipse_display_fix():
    """测试圆弧椭圆显示修复"""
    print("🔄 测试圆弧椭圆显示修复...")
    print("🎯 验证各方案都显示圆弧/椭圆弧，而不是完整图形")
    print("=" * 60)
    
    try:
        from multi_solution_reconstructor import MultiSolutionReconstructor
        
        # 创建多解决方案重构器
        reconstructor = MultiSolutionReconstructor(sample_points_count=30)
        
        # 创建测试实体 - 重点测试圆弧和椭圆弧
        test_entities = [
            # 90度圆弧（应该显示为四分之一圆）
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 30,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'arcs',
                'color': 'red'
            },
            # 镜像90度圆弧（关键测试）
            {
                'type': 'ARC',
                'center': (200, 100),
                'radius': 30,
                'start_angle': 0,
                'end_angle': 90,
                'scale_x': -1.0,
                'scale_y': 1.0,
                'layer': 'arcs',
                'color': 'red'
            },
            # 半椭圆弧（应该显示为半椭圆）
            {
                'type': 'ELLIPSE',
                'center': (150, 200),
                'major_axis': (40, 0),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'layer': 'ellipses',
                'color': 'purple'
            },
            # 镜像半椭圆弧（关键测试）
            {
                'type': 'ELLIPSE',
                'center': (250, 200),
                'major_axis': (40, 0),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'scale_x': 1.0,
                'scale_y': -1.0,
                'layer': 'ellipses',
                'color': 'purple'
            }
        ]
        
        print("🔄 使用所有解决方案处理测试实体...")
        
        # 使用所有解决方案重构
        solutions = reconstructor.reconstruct_with_all_solutions(test_entities)
        
        print(f"✅ 处理完成，共生成 {len(solutions)} 种解决方案")
        
        # 验证各方案的结果
        for solution_name, entities in solutions.items():
            print(f"\n📊 {solution_name}:")
            
            arc_count = 0
            ellipse_count = 0
            polyline_count = 0
            
            for entity in entities:
                entity_type = entity.get('type', 'UNKNOWN')
                if entity_type == 'ARC':
                    arc_count += 1
                elif entity_type == 'ELLIPSE':
                    ellipse_count += 1
                elif entity_type == 'POLYLINE':
                    polyline_count += 1
                    original_type = entity.get('original_type', 'UNKNOWN')
                    points_count = len(entity.get('points', []))
                    print(f"  ✅ {original_type} -> POLYLINE ({points_count} 点)")
            
            if solution_name == "原始显示":
                if arc_count > 0 or ellipse_count > 0:
                    print(f"  ⚠️ 原始显示仍使用 ARC/ELLIPSE 对象，可能显示完整图形")
                else:
                    print(f"  ✅ 原始显示已转换为多段线")
            else:
                if polyline_count > 0:
                    print(f"  ✅ 正确转换为多段线，应显示弧段")
                else:
                    print(f"  ⚠️ 未转换为多段线，可能显示完整图形")
        
        # 创建可视化验证
        create_visual_verification(solutions)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_visual_verification(solutions):
    """创建可视化验证"""
    try:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('圆弧椭圆显示修复验证 - 应显示弧段而非完整图形', fontsize=16)
        
        solution_names = ["原始显示", "角度修正法", "坐标变换法", 
                         "几何重建法", "采样定向法", "用户建议法"]
        
        for ax, solution_name in zip(axes.flat, solution_names):
            ax.set_title(solution_name, fontsize=12)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            entities = solutions.get(solution_name, [])
            draw_entities_for_verification(entities, ax)
            
            # 添加说明文字
            ax.text(0.02, 0.98, '应显示:\n• 90°圆弧段\n• 半椭圆弧段\n• 镜像效果', 
                   transform=ax.transAxes, fontsize=10, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 调整布局
        plt.tight_layout()
        plt.show()
        
        print("📊 可视化验证图已显示")
        print("💡 检查要点:")
        print("  1. 圆弧应显示为90度弧段，不是完整圆")
        print("  2. 椭圆应显示为半椭圆弧段，不是完整椭圆")
        print("  3. 镜像图形应显示正确的方向")
        print("  4. 所有方案都应显示弧段，而不是完整图形")
        
    except Exception as e:
        print(f"⚠️ 创建可视化验证失败: {e}")

def draw_entities_for_verification(entities, ax):
    """绘制实体用于验证"""
    for entity in entities:
        try:
            entity_type = entity.get('type', 'UNKNOWN')
            color = entity.get('color', 'black')
            
            if entity_type == 'POLYLINE':
                # 多段线 - 应该显示弧段
                points = entity['points']
                if len(points) >= 2:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    
                    # 使用粗线显示重构结果
                    ax.plot(x_coords, y_coords, color=color, linewidth=3, alpha=0.8)
                    
                    # 标记采样点
                    ax.scatter(x_coords, y_coords, color=color, s=15, alpha=0.6)
                    
                    # 标记端点
                    if len(x_coords) > 0:
                        ax.scatter([x_coords[0]], [y_coords[0]], color='green', s=50, 
                                 marker='o', edgecolor='darkgreen', linewidth=2, zorder=10)
                        ax.scatter([x_coords[-1]], [y_coords[-1]], color='red', s=50, 
                                 marker='s', edgecolor='darkred', linewidth=2, zorder=10)
            
            elif entity_type == 'ARC':
                # 原生圆弧 - 可能显示完整圆（问题情况）
                center = entity['center']
                radius = entity['radius']
                
                # 绘制完整圆作为参考（虚线）
                circle = Circle(center, radius, fill=False, edgecolor=color, 
                               linewidth=1, linestyle='--', alpha=0.5)
                ax.add_patch(circle)
                
                # 添加警告标记
                ax.text(center[0], center[1], '⚠️\n完整圆', ha='center', va='center', 
                       fontsize=8, color='red', weight='bold')
            
            elif entity_type == 'ELLIPSE':
                # 原生椭圆 - 可能显示完整椭圆（问题情况）
                center = entity['center']
                major_axis = entity['major_axis']
                ratio = entity.get('ratio', 1.0)
                
                a = np.linalg.norm(major_axis)
                b = a * ratio
                angle = np.degrees(np.arctan2(major_axis[1], major_axis[0]))
                
                # 绘制完整椭圆作为参考（虚线）
                ellipse = MplEllipse(center, 2*a, 2*b, angle=angle,
                                   fill=False, edgecolor=color, 
                                   linewidth=1, linestyle='--', alpha=0.5)
                ax.add_patch(ellipse)
                
                # 添加警告标记
                ax.text(center[0], center[1], '⚠️\n完整椭圆', ha='center', va='center', 
                       fontsize=8, color='red', weight='bold')
        
        except Exception as e:
            print(f"⚠️ 绘制实体失败: {entity_type}, 错误: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 圆弧椭圆显示修复测试")
    print("🎯 验证各方案正确显示弧段而非完整图形")
    print("=" * 60)
    
    success = test_arc_ellipse_display_fix()
    
    if success:
        print("\n🎉 测试完成！")
        print("\n💡 修复要点:")
        print("  1. 原始显示现在也使用多段线绘制")
        print("  2. 所有方案都转换为POLYLINE类型")
        print("  3. 正确处理镜像变换对角度的影响")
        print("  4. 显示真实的弧段而不是完整图形")
        print("\n🚀 现在运行 dxf_display_test.py 查看修复效果")
    else:
        print("\n❌ 测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
