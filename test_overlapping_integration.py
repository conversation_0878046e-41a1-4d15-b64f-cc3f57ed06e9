#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重叠线条合并功能的集成
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_integration():
    """测试重叠线条合并功能的集成"""
    print("=" * 80)
    print("🧪 测试重叠线条合并功能集成")
    print("=" * 80)
    
    try:
        from cad_data_processor import CADDataProcessor
        
        # 创建处理器
        processor = CADDataProcessor()
        
        print(f"\n📋 处理器初始化状态：")
        print(f"   专业DXF读取器: {'✅ 可用' if processor.professional_reader else '❌ 不可用'}")
        print(f"   重叠线条合并器: {'✅ 可用' if processor.overlapping_merger else '❌ 不可用'}")
        
        if not processor.overlapping_merger:
            print("❌ 重叠线条合并器不可用，无法进行集成测试")
            return False
        
        # 创建模拟实体数据（模拟DXF文件加载后的数据）
        mock_entities = create_mock_entities()
        
        print(f"\n🔍 模拟实体数据：")
        print(f"   总实体数量: {len(mock_entities)}")
        
        # 统计门窗图层实体
        door_window_entities = []
        for entity in mock_entities:
            layer = entity.get('layer', '').lower()
            if any(pattern in layer for pattern in ['door', 'window', '门', '窗']):
                door_window_entities.append(entity)
        
        print(f"   门窗图层实体: {len(door_window_entities)}")
        
        # 统计重叠线条
        overlapping_count = count_overlapping_lines(door_window_entities)
        print(f"   预期重叠线条组: {overlapping_count}")
        
        # 测试重叠线条合并功能
        print(f"\n🔧 执行重叠线条合并...")
        merged_entities = processor.overlapping_merger.process_entities(mock_entities)
        
        print(f"\n📊 合并结果：")
        print(f"   原始实体数量: {len(mock_entities)}")
        print(f"   合并后数量: {len(merged_entities)}")
        print(f"   减少数量: {len(mock_entities) - len(merged_entities)}")
        
        # 验证合并效果
        success = verify_merge_results(mock_entities, merged_entities)
        
        if success:
            print(f"\n✅ 重叠线条合并功能集成测试通过")
        else:
            print(f"\n❌ 重叠线条合并功能集成测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_mock_entities():
    """创建模拟实体数据"""
    entities = []
    
    # 门窗图层的重叠线条
    entities.extend([
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (0, 0),
            'end': (100, 0),
            'color': 1,
            'linetype': 'CONTINUOUS'
        },
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (0, 0),
            'end': (100, 0),  # 完全重叠
            'color': 256,
            'linetype': 'BYLAYER'
        },
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (100, 0),
            'end': (0, 0),  # 方向相反但重叠
            'color': 2,
            'linetype': 'DASHED',
            'is_dashed': True
        }
    ])
    
    # 窗户图层的重叠线条
    entities.extend([
        {
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'start': (200, 0),
            'end': (300, 0),
            'color': 3,
            'linetype': 'CONTINUOUS'
        },
        {
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'start': (200, 0),
            'end': (300, 0),  # 完全重叠
            'color': 4,
            'linetype': 'DASHED2'
        }
    ])
    
    # 中文门窗图层的重叠线条
    entities.extend([
        {
            'type': 'LINE',
            'layer': '门',
            'start': (400, 0),
            'end': (500, 0),
            'color': 5
        },
        {
            'type': 'LINE',
            'layer': '门',
            'start': (400, 0),
            'end': (500, 0),  # 完全重叠
            'color': 6
        }
    ])
    
    # 非门窗图层的线条（不应被合并）
    entities.extend([
        {
            'type': 'LINE',
            'layer': 'WALL',
            'start': (0, 0),
            'end': (100, 0),  # 与门窗图层重叠但图层不同
            'color': 7
        },
        {
            'type': 'LINE',
            'layer': 'WALL',
            'start': (0, 0),
            'end': (100, 0),  # 墙体图层的重叠线条
            'color': 8
        }
    ])
    
    # 门窗图层的非重叠线条
    entities.extend([
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': (600, 0),
            'end': (700, 0),  # 不重叠
            'color': 9
        },
        {
            'type': 'ARC',
            'layer': 'A-WINDOW',
            'center': (800, 0),
            'radius': 50,
            'color': 10
        }
    ])
    
    return entities

def count_overlapping_lines(entities):
    """统计重叠线条组的数量"""
    line_entities = [e for e in entities if e.get('type') == 'LINE']
    
    overlapping_groups = 0
    processed = set()
    
    for i, line1 in enumerate(line_entities):
        if i in processed:
            continue
        
        group_size = 1
        processed.add(i)
        
        for j, line2 in enumerate(line_entities):
            if j <= i or j in processed:
                continue
            
            if are_lines_overlapping_simple(line1, line2):
                group_size += 1
                processed.add(j)
        
        if group_size > 1:
            overlapping_groups += 1
    
    return overlapping_groups

def are_lines_overlapping_simple(line1, line2):
    """简单的重叠检测"""
    try:
        start1, end1 = line1.get('start'), line1.get('end')
        start2, end2 = line2.get('start'), line2.get('end')
        
        if not all([start1, end1, start2, end2]):
            return False
        
        # 检查两种情况
        case1 = (start1 == start2 and end1 == end2)
        case2 = (start1 == end2 and end1 == start2)
        
        return case1 or case2
    except:
        return False

def verify_merge_results(original_entities, merged_entities):
    """验证合并结果"""
    print(f"\n🔍 验证合并结果...")
    
    # 检查实体数量减少
    if len(merged_entities) >= len(original_entities):
        print(f"❌ 实体数量未减少")
        return False
    
    # 检查门窗图层线条数量
    original_door_lines = [e for e in original_entities 
                          if e.get('type') == 'LINE' and 'door' in e.get('layer', '').lower()]
    merged_door_lines = [e for e in merged_entities 
                        if e.get('type') == 'LINE' and 'door' in e.get('layer', '').lower()]
    
    print(f"   A-DOOR图层线条: {len(original_door_lines)} → {len(merged_door_lines)}")
    
    if len(merged_door_lines) >= len(original_door_lines):
        print(f"❌ 门图层线条数量未减少")
        return False
    
    # 检查窗户图层线条数量
    original_window_lines = [e for e in original_entities 
                            if e.get('type') == 'LINE' and 'window' in e.get('layer', '').lower()]
    merged_window_lines = [e for e in merged_entities 
                          if e.get('type') == 'LINE' and 'window' in e.get('layer', '').lower()]
    
    print(f"   A-WINDOW图层线条: {len(original_window_lines)} → {len(merged_window_lines)}")
    
    if len(merged_window_lines) >= len(original_window_lines):
        print(f"❌ 窗图层线条数量未减少")
        return False
    
    # 检查非门窗图层是否保持不变
    original_wall_lines = [e for e in original_entities 
                          if e.get('type') == 'LINE' and e.get('layer') == 'WALL']
    merged_wall_lines = [e for e in merged_entities 
                        if e.get('type') == 'LINE' and e.get('layer') == 'WALL']
    
    print(f"   WALL图层线条: {len(original_wall_lines)} → {len(merged_wall_lines)}")
    
    if len(merged_wall_lines) != len(original_wall_lines):
        print(f"❌ 非门窗图层线条数量发生变化")
        return False
    
    print(f"✅ 合并结果验证通过")
    return True

def test_full_workflow():
    """测试完整的工作流程"""
    print(f"\n" + "=" * 80)
    print("🔄 测试完整工作流程")
    print("=" * 80)
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        if not processor.overlapping_merger:
            print("❌ 重叠线条合并器不可用，跳过完整工作流程测试")
            return True
        
        # 创建模拟实体
        entities = create_mock_entities()
        
        print(f"🔧 执行完整处理流程...")
        print(f"   1. 重叠线条合并")
        
        # 步骤1：重叠线条合并
        merged_entities = processor.overlapping_merger.process_entities(entities)
        
        print(f"   2. 实体分组")
        
        # 步骤2：实体分组
        groups = processor.group_entities(merged_entities)
        
        print(f"\n📊 完整流程结果：")
        print(f"   原始实体: {len(entities)}")
        print(f"   合并后实体: {len(merged_entities)}")
        print(f"   分组数量: {len(groups)}")
        
        # 统计各图层的分组情况
        layer_stats = {}
        for group in groups:
            for entity in group:
                layer = entity.get('layer', 'UNKNOWN')
                if layer not in layer_stats:
                    layer_stats[layer] = {'groups': 0, 'entities': 0}
                layer_stats[layer]['entities'] += 1
            
            # 统计组的主要图层
            group_layers = [e.get('layer') for e in group]
            main_layer = max(set(group_layers), key=group_layers.count)
            layer_stats[main_layer]['groups'] += 1
        
        print(f"\n📋 各图层分组统计：")
        for layer, stats in layer_stats.items():
            print(f"   {layer}: {stats['groups']} 组, {stats['entities']} 实体")
        
        print(f"\n✅ 完整工作流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始重叠线条合并功能集成测试...")
    
    # 测试1：基本集成测试
    test1_result = test_integration()
    
    # 测试2：完整工作流程测试
    test2_result = test_full_workflow()
    
    print("\n" + "=" * 80)
    print("测试结果汇总:")
    print(f"基本集成测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"完整工作流程测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！重叠线条合并功能已成功集成。")
        print("\n📝 功能说明：")
        print("1. 在DXF文件加载过程中自动检测门窗图层的重叠线条")
        print("2. 对完全重叠（位置、长度一致）的线条进行合并")
        print("3. 选择最优的代表线条保留（优先保留有更多属性的线条）")
        print("4. 不影响其他图层的线条")
        print("5. 在图层识别和自动分组之前执行，确保后续处理的准确性")
    else:
        print("\n❌ 部分测试失败，请检查集成实现。")
    
    print("=" * 80)
