#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试自动滚动修复效果
验证：
1. IndexError修复
2. 自动滚动到第一个未标注组
3. 保持原始组顺序
"""

import os
import sys
import tkinter as tk
from unittest.mock import Mock, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_auto_scroll_functionality():
    """测试自动滚动功能"""
    print("=" * 60)
    print("测试自动滚动功能")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 模拟TreeView
        app.group_tree = Mock()
        
        # 模拟TreeView的children和item方法
        mock_items = ['item1', 'item2', 'item3', 'item4', 'item5']
        app.group_tree.get_children.return_value = mock_items
        
        # 模拟item方法返回不同状态的组
        def mock_item(item_id, key):
            if key == 'text':
                item_index = mock_items.index(item_id)
                return f"组{item_index + 1}"
            elif key == 'values':
                item_index = mock_items.index(item_id)
                if item_index == 0:  # 组1：已标注
                    return ['已标注', '墙体', '5']
                elif item_index == 1:  # 组2：已标注
                    return ['已标注', '门', '3']
                elif item_index == 2:  # 组3：未标注（第一个未标注）
                    return ['未标注', '待标注', '4']
                elif item_index == 3:  # 组4：标注中
                    return ['标注中', '待标注', '2']
                elif item_index == 4:  # 组5：未标注
                    return ['未标注', '待标注', '6']
            return []
        
        app.group_tree.item.side_effect = mock_item
        
        print("\n🔍 测试1: 有未标注组时的自动滚动")
        app._scroll_to_first_unlabeled_group()
        
        # 验证是否滚动到了第一个未标注组（组3）
        if app.group_tree.see.called:
            called_item = app.group_tree.see.call_args[0][0]
            expected_item = mock_items[2]  # 组3（第一个未标注组）
            if called_item == expected_item:
                print("✅ 正确滚动到第一个未标注组（组3）")
            else:
                print(f"❌ 滚动到错误的组，期望: {expected_item}, 实际: {called_item}")
        else:
            print("❌ 没有调用滚动方法")
        
        # 验证是否选中了该组
        if app.group_tree.selection_set.called:
            print("✅ 正确选中了第一个未标注组")
        else:
            print("❌ 没有选中第一个未标注组")
        
        print("\n🔍 测试2: 所有组都已标注时的滚动")
        
        # 重置mock
        app.group_tree.reset_mock()
        
        # 修改mock返回值，所有组都已标注
        def mock_item_all_labeled(item_id, key):
            if key == 'text':
                item_index = mock_items.index(item_id)
                return f"组{item_index + 1}"
            elif key == 'values':
                return ['已标注', '墙体', '5']  # 所有组都已标注
            return []
        
        app.group_tree.item.side_effect = mock_item_all_labeled
        
        app._scroll_to_first_unlabeled_group()
        
        # 验证是否滚动到了组1
        if app.group_tree.see.called:
            called_item = app.group_tree.see.call_args[0][0]
            expected_item = mock_items[0]  # 组1
            if called_item == expected_item:
                print("✅ 所有组已标注时，正确滚动到组1")
            else:
                print(f"❌ 滚动到错误的组，期望: {expected_item}, 实际: {called_item}")
        else:
            print("❌ 没有调用滚动方法")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_order_preservation():
    """测试组顺序保持不变"""
    print("\n" + "=" * 60)
    print("测试组顺序保持")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        from main_enhanced import EnhancedCADProcessor
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        app.processor = EnhancedCADProcessor()
        
        # 模拟TreeView
        app.group_tree = Mock()
        app.group_tree.get_children.return_value = []
        app.group_tree.delete = Mock()
        app.group_tree.insert = Mock()
        
        # 模拟处理器数据
        test_groups_info = [
            {'status': 'labeled', 'type': 'wall', 'entity_count': 5},      # 组1：已标注
            {'status': 'labeled', 'type': 'door', 'entity_count': 3},      # 组2：已标注
            {'status': 'unlabeled', 'type': '', 'entity_count': 4},        # 组3：未标注
            {'status': 'labeling', 'type': '', 'entity_count': 2},         # 组4：标注中
            {'status': 'unlabeled', 'type': '', 'entity_count': 6},        # 组5：未标注
        ]
        
        app.processor.get_groups_info = Mock(return_value=test_groups_info)
        app.processor.processor = Mock()
        app.processor.processor.category_mapping = {'wall': '墙体', 'door': '门'}
        app._get_group_fill_status = Mock(return_value='○')
        app._get_fill_tag = Mock(return_value='unfilled')
        app._set_fill_column_color = Mock()
        app._scroll_to_first_unlabeled_group = Mock()
        
        print("\n🔍 测试组列表更新保持原始顺序")
        app.update_group_list()
        
        # 验证insert调用的顺序
        insert_calls = app.group_tree.insert.call_args_list
        
        expected_order = ['组1', '组2', '组3', '组4', '组5']
        actual_order = []
        
        for call in insert_calls:
            # call[1] 是关键字参数，call[1]['text'] 是组ID
            if 'text' in call[1]:
                actual_order.append(call[1]['text'])
        
        if actual_order == expected_order:
            print("✅ 组顺序保持正确的原始顺序")
            print(f"  顺序: {actual_order}")
        else:
            print("❌ 组顺序不正确")
            print(f"  期望: {expected_order}")
            print(f"  实际: {actual_order}")
        
        # 验证是否调用了自动滚动
        if app._scroll_to_first_unlabeled_group.called:
            print("✅ 更新完成后调用了自动滚动")
        else:
            print("❌ 更新完成后没有调用自动滚动")
        
        root.destroy()
        return actual_order == expected_order
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indexerror_fixes():
    """测试IndexError修复"""
    print("\n" + "=" * 60)
    print("测试IndexError修复")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置测试数据
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],  # 已标注
            [{'type': 'CIRCLE'}],  # 未标注
        ]
        
        processor.groups_info = []
        processor.manual_grouping_mode = True
        processor.pending_manual_groups = [[{'type': 'CIRCLE'}]]
        processor.current_manual_group_index = 0
        
        print("\n🔍 测试正常情况")
        try:
            processor._update_groups_info()
            print("✅ 正常情况处理成功")
        except Exception as e:
            print(f"❌ 正常情况处理失败: {e}")
        
        print("\n🔍 测试索引超出范围")
        processor.current_manual_group_index = 10  # 超出范围
        
        try:
            processor._update_groups_info()
            print("✅ 索引超出范围处理成功，没有IndexError")
            print(f"  索引已重置为: {processor.current_manual_group_index}")
        except IndexError as e:
            print(f"❌ 仍然出现IndexError: {e}")
        except Exception as e:
            print(f"✅ 其他异常（可能正常）: {e}")
        
        print("\n🔍 测试label_current_group的安全调用")
        processor.pending_manual_groups = [[{'type': 'CIRCLE'}]]
        processor.current_manual_group_index = 0
        
        try:
            success = processor.label_current_group('door')
            print(f"✅ label_current_group安全调用成功: {success}")
        except Exception as e:
            print(f"❌ label_current_group调用失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scroll_priority():
    """测试滚动优先级"""
    print("\n" + "=" * 60)
    print("测试滚动优先级")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        app.group_tree = Mock()
        
        # 测试场景：有多种未标注状态的组
        mock_items = ['item1', 'item2', 'item3', 'item4']
        app.group_tree.get_children.return_value = mock_items
        
        def mock_item_priority(item_id, key):
            if key == 'text':
                item_index = mock_items.index(item_id)
                return f"组{item_index + 1}"
            elif key == 'values':
                item_index = mock_items.index(item_id)
                if item_index == 0:  # 组1：已标注
                    return ['已标注', '墙体', '5']
                elif item_index == 1:  # 组2：未标注（第一个未标注）
                    return ['未标注', '待标注', '3']
                elif item_index == 2:  # 组3：标注中
                    return ['标注中', '待标注', '4']
                elif item_index == 3:  # 组4：待处理
                    return ['待处理', '待标注', '2']
            return []
        
        app.group_tree.item.side_effect = mock_item_priority
        
        print("\n🔍 测试滚动优先级：未标注 > 标注中 > 待处理")
        app._scroll_to_first_unlabeled_group()
        
        # 应该滚动到第一个未标注组（组2）
        if app.group_tree.see.called:
            called_item = app.group_tree.see.call_args[0][0]
            expected_item = mock_items[1]  # 组2（第一个未标注）
            if called_item == expected_item:
                print("✅ 正确滚动到第一个未标注组（组2）")
            else:
                print(f"❌ 滚动优先级错误，期望: {expected_item}, 实际: {called_item}")
        else:
            print("❌ 没有调用滚动方法")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试自动滚动修复效果...")
    
    test1 = test_auto_scroll_functionality()
    test2 = test_group_order_preservation()
    test3 = test_indexerror_fixes()
    test4 = test_scroll_priority()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = {
        "自动滚动功能": test1,
        "组顺序保持": test2,
        "IndexError修复": test3,
        "滚动优先级": test4,
    }
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！自动滚动修复成功。")
        print("\n📋 功能说明：")
        print("- 组列表保持原始顺序（组1, 组2, 组3...）")
        print("- 每次更新后自动滚动到第一个未标注组")
        print("- 所有组已标注时滚动到组1")
        print("- 不再出现IndexError崩溃")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码。")
