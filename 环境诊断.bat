@echo off
chcp 65001 >nul
echo ====================================
echo   CAD工具环境诊断
echo ====================================
echo.

echo 1. 检查Python版本...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    goto :end
) else (
    echo ✅ Python已安装
)
echo.

echo 2. 检查Python路径...
where python
echo.

echo 3. 检查依赖包安装情况...
echo 正在检查matplotlib...
python -c "import matplotlib; print('✅ matplotlib:', matplotlib.__version__)" 2>nul || echo "❌ matplotlib 未安装"

echo 正在检查pandas...
python -c "import pandas; print('✅ pandas:', pandas.__version__)" 2>nul || echo "❌ pandas 未安装"

echo 正在检查numpy...
python -c "import numpy; print('✅ numpy:', numpy.__version__)" 2>nul || echo "❌ numpy 未安装"

echo 正在检查ezdxf...
python -c "import ezdxf; print('✅ ezdxf:', ezdxf.__version__)" 2>nul || echo "❌ ezdxf 未安装"

echo 正在检查shapely...
python -c "import shapely; print('✅ shapely:', shapely.__version__)" 2>nul || echo "❌ shapely 未安装"

echo 正在检查rtree...
python -c "import rtree; print('✅ rtree 可用')" 2>nul || echo "❌ rtree 未安装"

echo 正在检查tkinter...
python -c "import tkinter; print('✅ tkinter 可用')" 2>nul || echo "❌ tkinter 未安装"
echo.

echo 4. 检查主程序文件...
if exist "main.py" (
    echo ✅ main.py 存在
) else (
    echo ❌ main.py 不存在
)

if exist "main_simple.py" (
    echo ✅ main_simple.py 存在
) else (
    echo ❌ main_simple.py 不存在
)

if exist "cad_data_processor.py" (
    echo ✅ cad_data_processor.py 存在
) else (
    echo ❌ cad_data_processor.py 不存在
)

if exist "requirements.txt" (
    echo ✅ requirements.txt 存在
) else (
    echo ❌ requirements.txt 不存在
)
echo.

echo 5. 测试GUI环境...
echo 正在测试Tkinter GUI...
python -c "import tkinter as tk; root=tk.Tk(); root.title('测试'); root.geometry('200x100'); tk.Label(root, text='GUI测试成功').pack(); root.after(2000, root.quit); root.mainloop(); print('✅ GUI环境正常')" 2>nul || echo "❌ GUI环境有问题"
echo.

echo 6. 系统信息...
echo 操作系统: %OS%
echo 处理器架构: %PROCESSOR_ARCHITECTURE%
echo 当前目录: %CD%
echo.

:end
echo ====================================
echo   诊断完成
echo ====================================
echo.
echo 如果所有项目都显示✅，说明环境配置正确
echo 如果有❌项目，请根据故障排除指南.md解决
echo.
pause 