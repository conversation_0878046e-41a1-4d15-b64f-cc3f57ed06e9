#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试门窗识别问题
检查为什么门窗组没有被正确识别
"""

def test_door_window_detection():
    """测试门窗检测逻辑"""
    print("🚀 开始测试门窗检测逻辑...")
    print("="*80)
    
    try:
        # 模拟实际的标签情况
        print("🔍 测试标签匹配逻辑...")
        
        # 根据截图，实际的标签可能是这些
        test_labels = [
            '门',      # 中文门
            'door',    # 英文门
            'Door',    # 大写门
            'DOOR',    # 全大写门
            '窗',      # 中文窗
            'window',  # 英文窗
            'Window',  # 大写窗
            'WINDOW',  # 全大写窗
            '门窗',    # 中文门窗
            '栏杆',    # 中文栏杆
            'railing', # 英文栏杆
            '墙',      # 中文墙（对比）
            'wall',    # 英文墙（对比）
        ]
        
        # 当前的匹配规则
        door_window_patterns = ['door', 'window', '门', '窗', '门窗', 'railing', '栏杆']
        wall_patterns = ['wall', '墙体', '墙']
        
        print("\n📊 标签匹配测试:")
        door_window_matches = 0
        wall_matches = 0
        
        for label in test_labels:
            label_lower = label.lower()
            
            is_door_window = label_lower in door_window_patterns
            is_wall = label_lower in wall_patterns
            
            if is_door_window:
                print(f"   ✅ 门窗: '{label}' -> '{label_lower}' ✓")
                door_window_matches += 1
            elif is_wall:
                print(f"   🧱 墙体: '{label}' -> '{label_lower}' ✓")
                wall_matches += 1
            else:
                print(f"   ❌ 未匹配: '{label}' -> '{label_lower}' ✗")
        
        print(f"\n📊 匹配结果: 门窗 {door_window_matches}/{len([l for l in test_labels if any(p in l.lower() for p in ['门', '窗', 'door', 'window', 'railing', '栏杆'])])}, 墙体 {wall_matches}/{len([l for l in test_labels if any(p in l.lower() for p in ['墙', 'wall'])])}")
        
        # 测试实际的分类逻辑
        print("\n🔧 测试分类逻辑...")
        
        # 模拟实体组
        mock_groups = [
            # 墙体组
            [
                {'label': '墙', 'type': 'LINE'},
                {'label': 'wall', 'type': 'LINE'},
                {'label': '墙体', 'type': 'LINE'},
            ],
            # 门组
            [
                {'label': '门', 'type': 'LINE'},
                {'label': '门', 'type': 'LINE'},
                {'label': '门', 'type': 'LINE'},
            ],
            # 窗组
            [
                {'label': '窗', 'type': 'LINE'},
                {'label': 'window', 'type': 'LINE'},
            ],
            # 门窗组
            [
                {'label': '门窗', 'type': 'LINE'},
                {'label': 'door', 'type': 'LINE'},
            ],
            # 混合组
            [
                {'label': '门', 'type': 'LINE'},
                {'label': '墙', 'type': 'LINE'},
            ],
        ]
        
        wall_groups = []
        door_window_groups = []
        
        for group_idx, group in enumerate(mock_groups):
            group_labels = set()
            wall_entities = []
            door_window_entities = []
            
            # 调试：收集组中所有标签
            debug_labels = []
            for entity in group:
                label = entity.get('label', '').lower()
                if label:
                    group_labels.add(label)
                    debug_labels.append(label)
                
                # 根据标签分类实体
                if label in ['wall', '墙体', '墙']:
                    wall_entities.append(entity)
                elif label in ['door', 'window', '门', '窗', '门窗', 'railing', '栏杆']:
                    door_window_entities.append(entity)
            
            # 显示组的分类结果
            unique_labels = list(set(debug_labels))
            print(f"   组 {group_idx+1}: 标签 {unique_labels}")
            print(f"      墙体实体: {len(wall_entities)}")
            print(f"      门窗实体: {len(door_window_entities)}")
            
            # 根据主要标签确定组类型
            if wall_entities:
                wall_groups.append(wall_entities)
                print(f"      -> 归类为墙体组")
            
            if door_window_entities:
                door_window_groups.append(door_window_entities)
                print(f"      -> 归类为门窗组")
            
            if not wall_entities and not door_window_entities:
                print(f"      -> ❌ 未归类")
        
        print(f"\n📊 最终分类结果:")
        print(f"   墙体组: {len(wall_groups)} 个")
        print(f"   门窗组: {len(door_window_groups)} 个")
        
        # 检查可能的问题
        print("\n🔍 问题诊断:")
        
        issues = []
        
        # 检查1: 标签匹配是否完整
        expected_door_labels = ['门', 'door', 'window', '窗', '门窗', 'railing', '栏杆']
        missing_patterns = []
        for pattern in expected_door_labels:
            if pattern not in door_window_patterns:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            issues.append(f"缺少门窗匹配模式: {missing_patterns}")
        else:
            print("   ✅ 门窗匹配模式完整")
        
        # 检查2: 大小写处理
        test_case_labels = ['Door', 'DOOR', 'Window', 'WINDOW']
        case_issues = []
        for label in test_case_labels:
            if label.lower() not in door_window_patterns:
                case_issues.append(label)
        
        if case_issues:
            issues.append(f"大小写处理问题: {case_issues}")
        else:
            print("   ✅ 大小写处理正确")
        
        # 检查3: 中文编码
        chinese_labels = ['门', '窗', '门窗', '栏杆']
        encoding_issues = []
        for label in chinese_labels:
            if label not in door_window_patterns:
                encoding_issues.append(label)
        
        if encoding_issues:
            issues.append(f"中文编码问题: {encoding_issues}")
        else:
            print("   ✅ 中文标签支持正确")
        
        # 检查4: 空标签处理
        empty_entity = {'label': '', 'layer': 'door_layer'}
        layer_name = empty_entity.get('layer', '').lower() if isinstance(empty_entity.get('layer', ''), str) else str(empty_entity.get('layer', '')).lower()
        
        door_layer_patterns = ['door', 'window', '门', '窗', 'railing', '栏杆']
        layer_match = any(door_pattern in layer_name for door_pattern in door_layer_patterns)
        
        if layer_match:
            print("   ✅ 图层匹配逻辑正确")
        else:
            issues.append("图层匹配逻辑可能有问题")
        
        if issues:
            print(f"\n❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print(f"\n✅ 门窗检测逻辑正常")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_data_structure():
    """测试实际数据结构"""
    print("\n" + "="*80)
    print("🔍 测试实际数据结构...")
    
    try:
        # 模拟从截图看到的实际情况
        print("📊 根据截图分析:")
        print("   组14: 自动标注 门 5 已完成")
        print("   组15: 自动标注 门 5 已完成") 
        print("   组16: 自动标注 门 5 已完成")
        print("   组17: 自动标注 门 5 已完成")
        print("   组18: 自动标注 门 10 已完成")
        print("   组19: 自动标注 门 11 已完成")
        print("   组20: 自动标注 门 4 已完成")
        
        # 可能的实际标签值
        possible_labels = [
            '门',           # 直接中文
            'door',         # 英文
            '自动标注',     # 可能包含这个前缀
            '门 5',         # 可能包含数字
            '门5',          # 可能没有空格
        ]
        
        print(f"\n🔍 可能的实际标签值:")
        for label in possible_labels:
            print(f"   '{label}'")
        
        # 测试这些标签是否能被识别
        door_window_patterns = ['door', 'window', '门', '窗', '门窗', 'railing', '栏杆']
        
        print(f"\n📊 标签识别测试:")
        for label in possible_labels:
            label_lower = label.lower()
            
            # 直接匹配
            direct_match = label_lower in door_window_patterns
            
            # 包含匹配
            contains_match = any(pattern in label_lower for pattern in door_window_patterns)
            
            print(f"   '{label}' -> '{label_lower}':")
            print(f"      直接匹配: {direct_match}")
            print(f"      包含匹配: {contains_match}")
            
            if not direct_match and not contains_match:
                print(f"      ❌ 无法识别")
            else:
                print(f"      ✅ 可以识别")
        
        # 建议改进方案
        print(f"\n💡 建议改进方案:")
        print("   1. 使用包含匹配而不是精确匹配")
        print("   2. 处理带数字的标签")
        print("   3. 处理带前缀的标签")
        print("   4. 添加更多调试信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 门窗识别问题诊断")
    print("="*80)
    
    try:
        success1 = test_door_window_detection()
        success2 = test_actual_data_structure()
        
        print("\n" + "="*80)
        print("📊 诊断总结:")
        
        if success1 and success2:
            print("🎉 门窗检测逻辑基本正常！")
            print("\n💡 可能的问题:")
            print("   1. 实际标签值可能包含额外信息（如数字、前缀）")
            print("   2. 需要使用包含匹配而不是精确匹配")
            print("   3. 需要添加更多调试信息来查看实际标签值")
            
            print("\n🔧 建议修复方案:")
            print("   1. 修改匹配逻辑为包含匹配")
            print("   2. 添加详细的调试输出")
            print("   3. 处理复合标签（如'门 5'）")
        else:
            print("❌ 门窗检测逻辑存在问题")
            print("💡 请检查标签匹配规则")
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
