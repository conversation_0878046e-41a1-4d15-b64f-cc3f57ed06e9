#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终配色系统修复
"""

import sys
import os

def test_apply_color_scheme_fix():
    """测试应用配色方案修复"""
    try:
        print("测试应用配色方案修复:")
        
        # 检查代码中是否包含了修复后的应用配色逻辑
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找apply_color_scheme方法
        apply_method_start = content.find("def apply_color_scheme")
        if apply_method_start == -1:
            print("✗ apply_color_scheme方法未找到")
            return False
        
        apply_method_end = content.find("def ", apply_method_start + 1)
        if apply_method_end == -1:
            apply_method_end = len(content)
        
        apply_method_content = content[apply_method_start:apply_method_end]
        
        # 检查关键修复
        checks = [
            ("更新可视化器配色", "update_color_scheme"),
            ("即使没有数据也应用", "即使没有数据"),
            ("影响后续操作", "影响所有后续显示"),
            ("不再提示没有数据", "配色方案已应用")
        ]
        
        for check_name, pattern in checks:
            if pattern in apply_method_content:
                print(f"✓ {check_name}: 已修复")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 应用配色方案修复检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 应用配色方案修复测试失败: {e}")
        return False

def test_export_file_parameter_fix():
    """测试导出文件参数修复"""
    try:
        print("测试导出文件参数修复:")
        
        # 检查代码中是否使用了正确的参数
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找export_current_color_scheme方法
        export_method_start = content.find("def export_current_color_scheme")
        if export_method_start == -1:
            print("✗ export_current_color_scheme方法未找到")
            return False
        
        export_method_end = content.find("def ", export_method_start + 1)
        if export_method_end == -1:
            export_method_end = len(content)
        
        export_method_content = content[export_method_start:export_method_end]
        
        # 检查参数修复
        if "initialfile=" in export_method_content:
            print("✓ 使用正确的initialfile参数")
        else:
            print("✗ 未找到initialfile参数")
            return False
        
        if "initialvalue=" in export_method_content:
            print("✗ 仍在使用错误的initialvalue参数")
            return False
        else:
            print("✓ 已移除错误的initialvalue参数")
        
        print("✓ 导出文件参数修复检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 导出文件参数修复测试失败: {e}")
        return False

def test_clear_fill_fix():
    """测试清除填充修复"""
    try:
        print("测试清除填充修复:")
        
        # 检查代码中是否包含了修复后的清除填充逻辑
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找clear_wall_fills方法
        clear_method_start = content.find("def clear_wall_fills")
        if clear_method_start == -1:
            print("✗ clear_wall_fills方法未找到")
            return False
        
        clear_method_end = content.find("def ", clear_method_start + 1)
        if clear_method_end == -1:
            clear_method_end = len(content)
        
        clear_method_content = content[clear_method_start:clear_method_end]
        
        # 检查关键修复
        checks = [
            ("使用show_overview", "show_overview"),
            ("清除墙体填充", "wall_fills=None"),
            ("使用canvas.draw", "canvas.draw")
        ]
        
        for check_name, pattern in checks:
            if pattern in clear_method_content:
                print(f"✓ {check_name}: 已修复")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 清除填充修复检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 清除填充修复测试失败: {e}")
        return False

def test_group_fill_buttons():
    """测试组填充按钮功能"""
    try:
        print("测试组填充按钮功能:")
        
        # 检查代码中是否包含了组填充功能
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("填充列", "'填充'"),
            ("组点击事件", "on_group_click"),
            ("填充组方法", "fill_group"),
            ("获取填充状态", "_get_group_fill_status"),
            ("执行填充", "_perform_group_fill")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 组填充按钮功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 组填充按钮功能测试失败: {e}")
        return False

def test_fill_colors_in_scheme():
    """测试配色方案中的填充颜色"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试配色方案中的填充颜色:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查填充颜色是否存在
        fill_colors = [
            'wall_fill', 'door_window_fill', 'railing_fill', 'furniture_fill',
            'bed_fill', 'sofa_fill', 'cabinet_fill', 'dining_table_fill',
            'appliance_fill', 'stair_fill', 'elevator_fill', 'dimension_fill',
            'room_label_fill', 'column_fill', 'other_fill'
        ]
        
        default_scheme = app.default_color_scheme
        
        for fill_color in fill_colors:
            if fill_color in default_scheme:
                print(f"✓ 填充颜色 {fill_color}: {default_scheme[fill_color]}")
            else:
                print(f"✗ 填充颜色 {fill_color}: 缺失")
                root.destroy()
                return False
        
        root.destroy()
        print("✓ 配色方案填充颜色检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配色方案填充颜色测试失败: {e}")
        return False

def test_group_list_structure():
    """测试组列表结构"""
    try:
        print("测试组列表结构:")
        
        # 检查代码中的组列表结构
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_create_group_list方法
        group_list_start = content.find("def _create_group_list")
        if group_list_start == -1:
            print("✗ _create_group_list方法未找到")
            return False
        
        group_list_end = content.find("def ", group_list_start + 1)
        if group_list_end == -1:
            group_list_end = len(content)
        
        group_list_content = content[group_list_start:group_list_end]
        
        # 检查关键结构
        checks = [
            ("填充列", "'填充'"),
            ("单击事件绑定", "on_group_click"),
            ("填充列标题", "heading('填充'"),
            ("填充列宽度", "column('填充'")
        ]
        
        for check_name, pattern in checks:
            if pattern in group_list_content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 组列表结构检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 组列表结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试最终配色系统修复...")
    print("=" * 60)
    
    tests = [
        ("应用配色方案修复", test_apply_color_scheme_fix),
        ("导出文件参数修复", test_export_file_parameter_fix),
        ("清除填充修复", test_clear_fill_fix),
        ("组填充按钮功能", test_group_fill_buttons),
        ("配色方案填充颜色", test_fill_colors_in_scheme),
        ("组列表结构", test_group_list_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 最终配色系统修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
