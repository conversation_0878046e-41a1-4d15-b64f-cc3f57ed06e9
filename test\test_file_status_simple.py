#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的文件状态更新测试
"""

import sys
import os
import tempfile
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_status_update_mechanism():
    """测试状态更新机制"""
    print("=== 测试状态更新机制 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建临时文件夹和文件
        temp_dir = tempfile.mkdtemp()
        test_folder = os.path.join(temp_dir, "test_folder")
        os.makedirs(test_folder)
        
        # 创建测试文件
        test_file = os.path.join(test_folder, "test.dxf")
        with open(test_file, 'w') as f:
            f.write("0\nSECTION\n2\nENTITIES\n0\nENDSEC\n0\nEOF\n")
        
        print(f"创建测试文件: {test_file}")
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 手动初始化文件管理
        app.current_folder = test_folder
        app._scan_folder_files()
        
        print(f"扫描结果: 找到 {len(app.all_files)} 个文件")
        
        if app.all_files:
            file_path = app.all_files[0]
            file_name = os.path.basename(file_path)
            
            print(f"测试文件: {file_name}")
            
            # 检查初始状态
            initial_status = app.file_status[file_name].copy()
            print(f"初始状态: {initial_status}")
            
            # 设置当前文件
            app.current_file = file_path
            
            # 测试状态更新
            print(f"\n🔄 测试状态更新:")
            
            # 1. 测试处理中状态
            app.on_status_update("processing", "开始处理")
            processing_status = app.file_status[file_name].copy()
            print(f"处理中状态: {processing_status}")
            
            # 2. 测试完成状态
            app.on_status_update("completed", "处理完成")
            completed_status = app.file_status[file_name].copy()
            print(f"完成状态: {completed_status}")
            
            # 验证状态变化
            status_changed_to_processing = processing_status['processing_status'] == 'processing'
            status_changed_to_completed = completed_status['processing_status'] == 'completed'
            
            print(f"\n✅ 验证结果:")
            print(f"  状态变为处理中: {status_changed_to_processing}")
            print(f"  状态变为已完成: {status_changed_to_completed}")
            
            success = status_changed_to_processing and status_changed_to_completed
            
        else:
            print("❌ 没有找到测试文件")
            success = False
        
        root.destroy()
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_combo_content():
    """测试文件下拉菜单内容"""
    print("\n=== 测试文件下拉菜单内容 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建临时文件夹和多个文件
        temp_dir = tempfile.mkdtemp()
        test_folder = os.path.join(temp_dir, "test_folder")
        os.makedirs(test_folder)
        
        # 创建多个测试文件
        test_files = []
        for i in range(3):
            test_file = os.path.join(test_folder, f"file_{i+1}.dxf")
            with open(test_file, 'w') as f:
                f.write("0\nSECTION\n2\nENTITIES\n0\nENDSEC\n0\nEOF\n")
            test_files.append(test_file)
        
        print(f"创建测试文件: {[os.path.basename(f) for f in test_files]}")
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 扫描文件
        app.current_folder = test_folder
        app._scan_folder_files()
        
        print(f"扫描结果: 找到 {len(app.all_files)} 个文件")
        
        # 检查文件状态字典
        print(f"\n📋 文件状态字典:")
        for file_path in app.all_files:
            file_name = os.path.basename(file_path)
            if file_name in app.file_status:
                status = app.file_status[file_name]
                print(f"  {file_name}: {status}")
            else:
                print(f"  {file_name}: ❌ 状态缺失")
        
        # 测试下拉菜单更新
        print(f"\n🔄 测试下拉菜单更新:")
        
        # 模拟处理第一个文件
        if app.all_files:
            first_file = app.all_files[0]
            first_file_name = os.path.basename(first_file)
            
            app.current_file = first_file
            
            # 更新下拉菜单
            app.update_file_combo()
            
            # 检查下拉菜单内容
            if app.file_combo:
                combo_values = list(app.file_combo['values'])
                print(f"  下拉菜单项数: {len(combo_values)}")
                
                for i, value in enumerate(combo_values):
                    print(f"    {i+1}. {value}")
                
                # 验证内容
                has_all_files = len(combo_values) == len(app.all_files)
                contains_status = all('未处理' in item or '处理中' in item or '已完成' in item for item in combo_values)
                
                print(f"\n✅ 验证结果:")
                print(f"  包含所有文件: {has_all_files}")
                print(f"  包含状态信息: {contains_status}")
                
                success = has_all_files and contains_status
            else:
                print("❌ 下拉菜单未初始化")
                success = False
        else:
            print("❌ 没有找到文件")
            success = False
        
        root.destroy()
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_setup():
    """测试回调设置"""
    print("\n=== 测试回调设置 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 检查方法存在性
        has_on_status_update = hasattr(app, 'on_status_update') and callable(app.on_status_update)
        has_on_progress_update = hasattr(app, 'on_progress_update') and callable(app.on_progress_update)
        
        print(f"on_status_update 方法存在: {has_on_status_update}")
        print(f"on_progress_update 方法存在: {has_on_progress_update}")
        
        # 测试方法调用
        if has_on_status_update:
            try:
                # 模拟状态更新调用
                app.on_status_update("test", "测试数据")
                print("✅ on_status_update 方法调用成功")
                method_callable = True
            except Exception as e:
                print(f"❌ on_status_update 方法调用失败: {e}")
                method_callable = False
        else:
            method_callable = False
        
        root.destroy()
        
        return has_on_status_update and has_on_progress_update and method_callable
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("简化文件状态更新测试")
    print("=" * 40)
    print("目标: 验证核心的状态更新机制")
    print()
    
    tests = [
        ("状态更新机制", test_status_update_mechanism),
        ("文件下拉菜单内容", test_file_combo_content),
        ("回调设置", test_callback_setup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！核心状态更新机制正常。")
        print("\n📋 验证结果:")
        print("✅ 状态更新机制正常工作")
        print("✅ 文件下拉菜单内容正确")
        print("✅ 回调方法设置正确")
        
        print("\n🔧 修复要点:")
        print("• 确保处理器正确设置状态回调")
        print("• on_status_update 方法正确处理状态变化")
        print("• update_file_combo 方法正确更新下拉菜单")
        print("• 文件状态字典正确维护")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
