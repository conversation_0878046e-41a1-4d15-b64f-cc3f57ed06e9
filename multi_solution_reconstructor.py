#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多解决方案重构器
针对镜像操作导致的坐标系手性变化问题，提供多种解决方案
"""

import numpy as np
import math
from typing import List, Dict, Any, Tuple
from enum import Enum

class SolutionType(Enum):
    """解决方案类型"""
    ORIGINAL = "原始显示"
    ANGLE_CORRECTION = "角度修正法"
    COORDINATE_TRANSFORM = "坐标变换法"
    GEOMETRIC_RECONSTRUCTION = "几何重建法"
    SAMPLING_DIRECTION = "采样定向法"
    USER_SUGGESTED = "用户建议法"
    HANDEDNESS_CORRECTION = "手性校正法"

class MultiSolutionReconstructor:
    """多解决方案重构器"""
    
    def __init__(self, sample_points_count: int = 50):
        """
        初始化多解决方案重构器

        Args:
            sample_points_count: 采样点数量
        """
        self.sample_points_count = sample_points_count

        # 导入坐标系检测器
        try:
            from coordinate_system_detector import CoordinateSystemDetector
            self.coord_detector = CoordinateSystemDetector()
        except ImportError:
            self.coord_detector = None
    
    def reconstruct_with_all_solutions(self, entities: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        使用所有解决方案重构实体
        
        Args:
            entities: 原始实体列表
            
        Returns:
            各种解决方案的重构结果字典
        """
        solutions = {}
        
        # 原始显示（转换为多段线以正确显示）
        solutions[SolutionType.ORIGINAL.value] = self._convert_to_polylines_for_display(entities.copy())
        
        # 角度修正法
        solutions[SolutionType.ANGLE_CORRECTION.value] = self._angle_correction_method(entities.copy())
        
        # 坐标变换法
        solutions[SolutionType.COORDINATE_TRANSFORM.value] = self._coordinate_transform_method(entities.copy())
        
        # 几何重建法
        solutions[SolutionType.GEOMETRIC_RECONSTRUCTION.value] = self._geometric_reconstruction_method(entities.copy())
        
        # 采样定向法
        solutions[SolutionType.SAMPLING_DIRECTION.value] = self._sampling_direction_method(entities.copy())
        
        # 用户建议法
        solutions[SolutionType.USER_SUGGESTED.value] = self._user_suggested_method(entities.copy())

        # 手性校正法
        solutions[SolutionType.HANDEDNESS_CORRECTION.value] = self._handedness_correction_method(entities.copy())

        return solutions

    def _convert_to_polylines_for_display(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将ARC和ELLIPSE转换为多段线以正确显示"""
        converted = []

        for entity in entities:
            if entity['type'] == 'ARC':
                converted.append(self._transform_arc_to_polyline(entity, 'original_display'))
            elif entity['type'] == 'ELLIPSE':
                converted.append(self._transform_ellipse_to_polyline(entity, 'original_display'))
            else:
                converted.append(entity)

        return converted

    def _angle_correction_method(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        方案1: 角度修正法
        直接修正镜像后的角度，考虑坐标系手性变化
        """
        reconstructed = []
        
        for entity in entities:
            if entity['type'] == 'ARC':
                new_entity = self._correct_arc_angles(entity)
                reconstructed.append(new_entity)
            elif entity['type'] == 'ELLIPSE':
                new_entity = self._correct_ellipse_params(entity)
                reconstructed.append(new_entity)
            else:
                reconstructed.append(entity)
        
        return reconstructed
    
    def _coordinate_transform_method(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        方案2: 坐标变换法
        通过逆变换恢复原始坐标系
        """
        reconstructed = []
        
        for entity in entities:
            if entity['type'] in ['ARC', 'ELLIPSE']:
                new_entity = self._apply_inverse_transform(entity)
                reconstructed.append(new_entity)
            else:
                reconstructed.append(entity)
        
        return reconstructed
    
    def _geometric_reconstruction_method(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        方案3: 几何重建法
        基于几何点变换重建圆弧和椭圆
        """
        reconstructed = []
        
        for entity in entities:
            if entity['type'] == 'ARC':
                new_entity = self._reconstruct_arc_geometrically(entity)
                reconstructed.append(new_entity)
            elif entity['type'] == 'ELLIPSE':
                new_entity = self._reconstruct_ellipse_geometrically(entity)
                reconstructed.append(new_entity)
            else:
                reconstructed.append(entity)
        
        return reconstructed
    
    def _sampling_direction_method(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        方案4: 采样定向法
        通过采样点确定正确的方向和范围
        """
        reconstructed = []
        
        for entity in entities:
            if entity['type'] == 'ARC':
                new_entity = self._reconstruct_arc_by_sampling(entity)
                reconstructed.append(new_entity)
            elif entity['type'] == 'ELLIPSE':
                new_entity = self._reconstruct_ellipse_by_sampling(entity)
                reconstructed.append(new_entity)
            else:
                reconstructed.append(entity)
        
        return reconstructed
    
    def _user_suggested_method(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        方案5: 用户建议法
        获取中心点、角度、完整图形、端点及采样点，通过采样点定位方向重新截取
        """
        reconstructed = []
        
        for entity in entities:
            if entity['type'] == 'ARC':
                new_entity = self._user_suggested_arc_reconstruction(entity)
                reconstructed.append(new_entity)
            elif entity['type'] == 'ELLIPSE':
                new_entity = self._user_suggested_ellipse_reconstruction(entity)
                reconstructed.append(new_entity)
            else:
                reconstructed.append(entity)
        
        return reconstructed
    
    def _correct_arc_angles(self, arc_entity: Dict[str, Any]) -> Dict[str, Any]:
        """角度修正法处理圆弧 - 转换为多段线"""
        center = np.array(arc_entity['center'])
        radius = arc_entity['radius']
        start_angle = arc_entity.get('start_angle', 0)
        end_angle = arc_entity.get('end_angle', 360)

        # 检查是否有镜像
        scale_x = arc_entity.get('scale_x', 1.0)
        scale_y = arc_entity.get('scale_y', 1.0)
        is_mirrored = (scale_x * scale_y) < 0

        # 角度单位转换
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)

        # 镜像时修正角度
        if is_mirrored:
            original_start = start_angle
            original_end = end_angle
            start_angle = (360 - original_end) % 360
            end_angle = (360 - original_start) % 360

        # 生成圆弧点
        if end_angle < start_angle:
            end_angle += 360

        num_points = max(int(self.sample_points_count * (end_angle - start_angle) / 360), 3)
        angles = np.linspace(np.radians(start_angle), np.radians(end_angle), num_points)

        x_coords = center[0] + radius * np.cos(angles)
        y_coords = center[1] + radius * np.sin(angles)

        # 应用缩放变换
        if scale_x != 1.0 or scale_y != 1.0:
            x_coords = x_coords * scale_x
            y_coords = y_coords * scale_y

        sample_points = np.column_stack((x_coords, y_coords))

        return {
            'type': 'POLYLINE',
            'points': sample_points.tolist(),
            'layer': arc_entity.get('layer', 'default'),
            'color': arc_entity.get('color', 'red'),
            'original_type': 'ARC',
            'reconstruction_method': 'angle_correction'
        }
    
    def _correct_ellipse_params(self, ellipse_entity: Dict[str, Any]) -> Dict[str, Any]:
        """角度修正法处理椭圆 - 转换为多段线"""
        center = np.array(ellipse_entity['center'])
        major_axis = np.array(ellipse_entity['major_axis'])
        ratio = ellipse_entity.get('ratio', 1.0)
        start_param = ellipse_entity.get('start_param', 0)
        end_param = ellipse_entity.get('end_param', 2 * np.pi)

        # 计算椭圆参数
        a = np.linalg.norm(major_axis)
        b = a * ratio
        angle = np.arctan2(major_axis[1], major_axis[0])

        # 检查是否有镜像
        scale_x = ellipse_entity.get('scale_x', 1.0)
        scale_y = ellipse_entity.get('scale_y', 1.0)
        is_mirrored = (scale_x * scale_y) < 0

        if is_mirrored:
            angle = -angle
            if abs(end_param - start_param) < 2 * np.pi - 0.1:
                original_start = start_param
                original_end = end_param
                start_param = 2 * np.pi - original_end
                end_param = 2 * np.pi - original_start

        # 生成椭圆点
        if end_param < start_param:
            end_param += 2 * np.pi

        param_range = end_param - start_param
        num_points = max(int(self.sample_points_count * param_range / (2 * np.pi)), 3)
        params = np.linspace(start_param, end_param, num_points)

        # 计算椭圆上的点
        x_std = a * np.cos(params)
        y_std = b * np.sin(params)

        # 应用旋转
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)
        x_rot = x_std * cos_angle - y_std * sin_angle
        y_rot = x_std * sin_angle + y_std * cos_angle

        # 平移到中心
        x_coords = center[0] + x_rot
        y_coords = center[1] + y_rot

        # 应用缩放变换
        if scale_x != 1.0 or scale_y != 1.0:
            x_coords = x_coords * scale_x
            y_coords = y_coords * scale_y

        sample_points = np.column_stack((x_coords, y_coords))

        return {
            'type': 'POLYLINE',
            'points': sample_points.tolist(),
            'layer': ellipse_entity.get('layer', 'default'),
            'color': ellipse_entity.get('color', 'purple'),
            'original_type': 'ELLIPSE',
            'reconstruction_method': 'angle_correction'
        }
    
    def _apply_inverse_transform(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """坐标变换法 - 转换为多段线"""
        entity_type = entity['type']

        if entity_type == 'ARC':
            return self._transform_arc_to_polyline(entity, 'coordinate_transform')
        elif entity_type == 'ELLIPSE':
            return self._transform_ellipse_to_polyline(entity, 'coordinate_transform')
        else:
            return entity

    def _transform_arc_to_polyline(self, arc_entity: Dict[str, Any], method: str) -> Dict[str, Any]:
        """将圆弧转换为多段线（通用方法）"""
        center = np.array(arc_entity['center'])
        radius = arc_entity['radius']
        start_angle = arc_entity.get('start_angle', 0)
        end_angle = arc_entity.get('end_angle', 360)

        # 角度单位转换
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)

        # 处理角度范围
        if end_angle < start_angle:
            end_angle += 360

        num_points = max(int(self.sample_points_count * (end_angle - start_angle) / 360), 3)
        angles = np.linspace(np.radians(start_angle), np.radians(end_angle), num_points)

        x_coords = center[0] + radius * np.cos(angles)
        y_coords = center[1] + radius * np.sin(angles)

        sample_points = np.column_stack((x_coords, y_coords))

        return {
            'type': 'POLYLINE',
            'points': sample_points.tolist(),
            'layer': arc_entity.get('layer', 'default'),
            'color': arc_entity.get('color', 'red'),
            'original_type': 'ARC',
            'reconstruction_method': method
        }

    def _transform_ellipse_to_polyline(self, ellipse_entity: Dict[str, Any], method: str) -> Dict[str, Any]:
        """将椭圆转换为多段线（通用方法）"""
        center = np.array(ellipse_entity['center'])
        major_axis = np.array(ellipse_entity['major_axis'])
        ratio = ellipse_entity.get('ratio', 1.0)
        start_param = ellipse_entity.get('start_param', 0)
        end_param = ellipse_entity.get('end_param', 2 * np.pi)

        # 计算椭圆参数
        a = np.linalg.norm(major_axis)
        b = a * ratio
        angle = np.arctan2(major_axis[1], major_axis[0])

        # 处理参数范围
        if end_param < start_param:
            end_param += 2 * np.pi

        param_range = end_param - start_param
        num_points = max(int(self.sample_points_count * param_range / (2 * np.pi)), 3)
        params = np.linspace(start_param, end_param, num_points)

        # 计算椭圆上的点
        x_std = a * np.cos(params)
        y_std = b * np.sin(params)

        # 应用旋转
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)
        x_rot = x_std * cos_angle - y_std * sin_angle
        y_rot = x_std * sin_angle + y_std * cos_angle

        # 平移到中心
        x_coords = center[0] + x_rot
        y_coords = center[1] + y_rot

        sample_points = np.column_stack((x_coords, y_coords))

        return {
            'type': 'POLYLINE',
            'points': sample_points.tolist(),
            'layer': ellipse_entity.get('layer', 'default'),
            'color': ellipse_entity.get('color', 'purple'),
            'original_type': 'ELLIPSE',
            'reconstruction_method': method
        }
    
    def _reconstruct_arc_geometrically(self, arc_entity: Dict[str, Any]) -> Dict[str, Any]:
        """几何重建法处理圆弧"""
        # 采样原始圆弧上的点
        center = np.array(arc_entity['center'])
        radius = arc_entity['radius']
        start_angle = arc_entity.get('start_angle', 0)
        end_angle = arc_entity.get('end_angle', 360)
        
        # 生成采样点
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)
        
        angles = np.linspace(np.radians(start_angle), np.radians(end_angle), 9)
        sample_points = []
        
        for angle in angles:
            x = center[0] + radius * np.cos(angle)
            y = center[1] + radius * np.sin(angle)
            sample_points.append([x, y])
        
        # 应用变换
        scale_x = arc_entity.get('scale_x', 1.0)
        scale_y = arc_entity.get('scale_y', 1.0)
        
        if scale_x != 1.0 or scale_y != 1.0:
            for point in sample_points:
                point[0] *= scale_x
                point[1] *= scale_y
        
        return {
            'type': 'POLYLINE',
            'points': sample_points,
            'layer': arc_entity.get('layer', 'default'),
            'color': arc_entity.get('color', 'red'),
            'original_type': 'ARC',
            'reconstruction_method': 'geometric_reconstruction'
        }
    
    def _reconstruct_ellipse_geometrically(self, ellipse_entity: Dict[str, Any]) -> Dict[str, Any]:
        """几何重建法处理椭圆"""
        center = np.array(ellipse_entity['center'])
        major_axis = np.array(ellipse_entity['major_axis'])
        ratio = ellipse_entity.get('ratio', 1.0)
        start_param = ellipse_entity.get('start_param', 0)
        end_param = ellipse_entity.get('end_param', 2 * np.pi)
        
        # 计算椭圆参数
        a = np.linalg.norm(major_axis)
        b = a * ratio
        angle = np.arctan2(major_axis[1], major_axis[0])
        
        # 生成采样点
        params = np.linspace(start_param, end_param, 20)
        sample_points = []
        
        for param in params:
            # 标准椭圆上的点
            x_std = a * np.cos(param)
            y_std = b * np.sin(param)
            
            # 旋转
            x_rot = x_std * np.cos(angle) - y_std * np.sin(angle)
            y_rot = x_std * np.sin(angle) + y_std * np.cos(angle)
            
            # 平移
            x = center[0] + x_rot
            y = center[1] + y_rot
            
            sample_points.append([x, y])
        
        # 应用变换
        scale_x = ellipse_entity.get('scale_x', 1.0)
        scale_y = ellipse_entity.get('scale_y', 1.0)
        
        if scale_x != 1.0 or scale_y != 1.0:
            for point in sample_points:
                point[0] *= scale_x
                point[1] *= scale_y
        
        return {
            'type': 'POLYLINE',
            'points': sample_points,
            'layer': ellipse_entity.get('layer', 'default'),
            'color': ellipse_entity.get('color', 'purple'),
            'original_type': 'ELLIPSE',
            'reconstruction_method': 'geometric_reconstruction'
        }

    def _reconstruct_arc_by_sampling(self, arc_entity: Dict[str, Any]) -> Dict[str, Any]:
        """采样定向法处理圆弧"""
        center = np.array(arc_entity['center'])
        radius = arc_entity['radius']
        start_angle = arc_entity.get('start_angle', 0)
        end_angle = arc_entity.get('end_angle', 360)

        # 检查镜像
        scale_x = arc_entity.get('scale_x', 1.0)
        scale_y = arc_entity.get('scale_y', 1.0)
        is_mirrored = (scale_x * scale_y) < 0

        # 生成完整圆的采样点
        full_angles = np.linspace(0, 2*np.pi, self.sample_points_count * 2, endpoint=False)
        full_circle_points = []
        for angle in full_angles:
            x = center[0] + radius * np.cos(angle)
            y = center[1] + radius * np.sin(angle)
            full_circle_points.append([x, y])

        # 计算理论端点
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_rad = start_angle
            end_rad = end_angle
        else:
            start_rad = np.radians(start_angle)
            end_rad = np.radians(end_angle)

        start_point = center + radius * np.array([np.cos(start_rad), np.sin(start_rad)])
        end_point = center + radius * np.array([np.cos(end_rad), np.sin(end_rad)])

        # 应用变换到端点
        if scale_x != 1.0 or scale_y != 1.0:
            start_point = np.array([start_point[0] * scale_x, start_point[1] * scale_y])
            end_point = np.array([end_point[0] * scale_x, end_point[1] * scale_y])

        # 在完整圆上找到最接近的点
        full_circle_points = np.array(full_circle_points)
        start_distances = np.linalg.norm(full_circle_points - start_point, axis=1)
        end_distances = np.linalg.norm(full_circle_points - end_point, axis=1)

        start_idx = np.argmin(start_distances)
        end_idx = np.argmin(end_distances)

        # 确定采样范围
        if end_idx >= start_idx:
            indices = list(range(start_idx, end_idx + 1))
        else:
            indices = list(range(start_idx, len(full_circle_points))) + list(range(0, end_idx + 1))

        sample_points = [full_circle_points[i].tolist() for i in indices]

        return {
            'type': 'POLYLINE',
            'points': sample_points,
            'layer': arc_entity.get('layer', 'default'),
            'color': arc_entity.get('color', 'red'),
            'original_type': 'ARC',
            'reconstruction_method': 'sampling_direction'
        }

    def _reconstruct_ellipse_by_sampling(self, ellipse_entity: Dict[str, Any]) -> Dict[str, Any]:
        """采样定向法处理椭圆"""
        center = np.array(ellipse_entity['center'])
        major_axis = np.array(ellipse_entity['major_axis'])
        ratio = ellipse_entity.get('ratio', 1.0)
        start_param = ellipse_entity.get('start_param', 0)
        end_param = ellipse_entity.get('end_param', 2 * np.pi)

        # 计算椭圆参数
        a = np.linalg.norm(major_axis)
        b = a * ratio
        angle = np.arctan2(major_axis[1], major_axis[0])

        # 生成完整椭圆的采样点
        full_params = np.linspace(0, 2*np.pi, self.sample_points_count * 2, endpoint=False)
        full_ellipse_points = []

        for param in full_params:
            x_std = a * np.cos(param)
            y_std = b * np.sin(param)

            x_rot = x_std * np.cos(angle) - y_std * np.sin(angle)
            y_rot = x_std * np.sin(angle) + y_std * np.cos(angle)

            x = center[0] + x_rot
            y = center[1] + y_rot

            full_ellipse_points.append([x, y])

        # 计算理论端点
        start_x_std = a * np.cos(start_param)
        start_y_std = b * np.sin(start_param)
        end_x_std = a * np.cos(end_param)
        end_y_std = b * np.sin(end_param)

        start_x_rot = start_x_std * np.cos(angle) - start_y_std * np.sin(angle)
        start_y_rot = start_x_std * np.sin(angle) + start_y_std * np.cos(angle)
        end_x_rot = end_x_std * np.cos(angle) - end_y_std * np.sin(angle)
        end_y_rot = end_x_std * np.sin(angle) + end_y_std * np.cos(angle)

        start_point = center + np.array([start_x_rot, start_y_rot])
        end_point = center + np.array([end_x_rot, end_y_rot])

        # 应用变换
        scale_x = ellipse_entity.get('scale_x', 1.0)
        scale_y = ellipse_entity.get('scale_y', 1.0)

        if scale_x != 1.0 or scale_y != 1.0:
            start_point = np.array([start_point[0] * scale_x, start_point[1] * scale_y])
            end_point = np.array([end_point[0] * scale_x, end_point[1] * scale_y])

        # 在完整椭圆上找到最接近的点
        full_ellipse_points = np.array(full_ellipse_points)
        start_distances = np.linalg.norm(full_ellipse_points - start_point, axis=1)
        end_distances = np.linalg.norm(full_ellipse_points - end_point, axis=1)

        start_idx = np.argmin(start_distances)
        end_idx = np.argmin(end_distances)

        # 确定采样范围
        if end_idx >= start_idx:
            indices = list(range(start_idx, end_idx + 1))
        else:
            indices = list(range(start_idx, len(full_ellipse_points))) + list(range(0, end_idx + 1))

        sample_points = [full_ellipse_points[i].tolist() for i in indices]

        return {
            'type': 'POLYLINE',
            'points': sample_points,
            'layer': ellipse_entity.get('layer', 'default'),
            'color': ellipse_entity.get('color', 'purple'),
            'original_type': 'ELLIPSE',
            'reconstruction_method': 'sampling_direction'
        }

    def _user_suggested_arc_reconstruction(self, arc_entity: Dict[str, Any]) -> Dict[str, Any]:
        """
        用户建议法处理圆弧
        获取中心点、角度、完整图形、端点及采样点，通过采样点定位方向重新截取
        """
        # 1. 获取完整圆的基本参数
        center = np.array(arc_entity['center'])
        radius = float(arc_entity['radius'])
        start_angle = float(arc_entity.get('start_angle', 0))
        end_angle = float(arc_entity.get('end_angle', 360))

        # 2. 处理缩放和镜像变换
        scale_x = arc_entity.get('scale_x', 1.0)
        scale_y = arc_entity.get('scale_y', 1.0)
        is_mirrored = (scale_x * scale_y) < 0

        # 3. 计算实际半径（考虑缩放）
        if scale_x != 1.0 or scale_y != 1.0:
            actual_radius = radius * math.sqrt(abs(scale_x * scale_y))
        else:
            actual_radius = radius

        # 4. 生成完整圆的采样点用于定位方向
        full_circle_angles = np.linspace(0, 2*np.pi, self.sample_points_count * 2, endpoint=False)
        full_circle_points = []
        for angle in full_circle_angles:
            x = center[0] + actual_radius * np.cos(angle)
            y = center[1] + actual_radius * np.sin(angle)
            full_circle_points.append([x, y])
        full_circle_points = np.array(full_circle_points)

        # 5. 计算原始圆弧的端点
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_rad = start_angle
            end_rad = end_angle
        else:
            start_rad = np.radians(start_angle)
            end_rad = np.radians(end_angle)

        # 处理镜像对角度的影响
        if is_mirrored:
            # 镜像时角度方向反转
            original_start = start_rad
            original_end = end_rad
            start_rad = 2*np.pi - original_end
            end_rad = 2*np.pi - original_start

        start_point = center + actual_radius * np.array([np.cos(start_rad), np.sin(start_rad)])
        end_point = center + actual_radius * np.array([np.cos(end_rad), np.sin(end_rad)])

        # 6. 通过采样点定位在完整圆上的正确位置
        start_distances = np.linalg.norm(full_circle_points - start_point, axis=1)
        end_distances = np.linalg.norm(full_circle_points - end_point, axis=1)

        start_idx = np.argmin(start_distances)
        end_idx = np.argmin(end_distances)

        # 计算对应的角度
        corrected_start_angle = np.degrees(full_circle_angles[start_idx])
        corrected_end_angle = np.degrees(full_circle_angles[end_idx])

        # 7. 从完整图形中截取正确的圆弧段
        if corrected_end_angle < corrected_start_angle:
            corrected_end_angle += 360

        angle_range = corrected_end_angle - corrected_start_angle
        num_points = max(int(self.sample_points_count * angle_range / 360), 3)

        angles = np.linspace(np.radians(corrected_start_angle), np.radians(corrected_end_angle), num_points)
        final_sample_points = []

        for angle in angles:
            x = center[0] + actual_radius * np.cos(angle)
            y = center[1] + actual_radius * np.sin(angle)
            final_sample_points.append([x, y])

        return {
            'type': 'POLYLINE',
            'points': final_sample_points,
            'layer': arc_entity.get('layer', 'default'),
            'color': arc_entity.get('color', 'red'),
            'original_type': 'ARC',
            'reconstruction_method': 'user_suggested',
            'reconstruction_info': {
                'center': center.tolist(),
                'radius': actual_radius,
                'start_angle': corrected_start_angle,
                'end_angle': corrected_end_angle,
                'start_point': start_point.tolist(),
                'end_point': end_point.tolist(),
                'is_mirrored': is_mirrored
            }
        }

    def _user_suggested_ellipse_reconstruction(self, ellipse_entity: Dict[str, Any]) -> Dict[str, Any]:
        """
        用户建议法处理椭圆
        获取中心点、角度、完整图形、端点及采样点，通过采样点定位方向重新截取
        """
        # 1. 获取完整椭圆的基本参数
        center = np.array(ellipse_entity['center'])
        major_axis = np.array(ellipse_entity['major_axis'])
        ratio = float(ellipse_entity.get('ratio', 1.0))
        start_param = float(ellipse_entity.get('start_param', 0))
        end_param = float(ellipse_entity.get('end_param', 2 * np.pi))

        # 2. 计算椭圆几何参数
        a = float(np.linalg.norm(major_axis))  # 长轴长度
        b = float(a * ratio)  # 短轴长度
        angle = float(np.arctan2(major_axis[1], major_axis[0]))  # 旋转角度

        # 3. 处理缩放和镜像变换
        scale_x = ellipse_entity.get('scale_x', 1.0)
        scale_y = ellipse_entity.get('scale_y', 1.0)
        is_mirrored = (scale_x * scale_y) < 0

        if is_mirrored:
            angle = -angle
            if scale_x != 1.0 or scale_y != 1.0:
                scale_factor = math.sqrt(abs(scale_x * scale_y))
                a *= scale_factor
                b *= scale_factor

        # 4. 生成完整椭圆的采样点用于定位方向
        full_params = np.linspace(0, 2*np.pi, self.sample_points_count * 2, endpoint=False)
        full_ellipse_points = []

        for param in full_params:
            x_std = a * np.cos(param)
            y_std = b * np.sin(param)

            x_rot = x_std * np.cos(angle) - y_std * np.sin(angle)
            y_rot = x_std * np.sin(angle) + y_std * np.cos(angle)

            x = center[0] + x_rot
            y = center[1] + y_rot

            full_ellipse_points.append([x, y])

        full_ellipse_points = np.array(full_ellipse_points)

        # 5. 计算原始椭圆弧的端点
        if is_mirrored and abs(end_param - start_param) < 2 * np.pi - 0.1:
            original_start = start_param
            original_end = end_param
            start_param = 2 * np.pi - original_end
            end_param = 2 * np.pi - original_start

        start_x_std = a * np.cos(start_param)
        start_y_std = b * np.sin(start_param)
        end_x_std = a * np.cos(end_param)
        end_y_std = b * np.sin(end_param)

        start_x_rot = start_x_std * np.cos(angle) - start_y_std * np.sin(angle)
        start_y_rot = start_x_std * np.sin(angle) + start_y_std * np.cos(angle)
        end_x_rot = end_x_std * np.cos(angle) - end_y_std * np.sin(angle)
        end_y_rot = end_x_std * np.sin(angle) + end_y_std * np.cos(angle)

        start_point = center + np.array([start_x_rot, start_y_rot])
        end_point = center + np.array([end_x_rot, end_y_rot])

        # 6. 通过采样点定位在完整椭圆上的正确位置
        start_distances = np.linalg.norm(full_ellipse_points - start_point, axis=1)
        end_distances = np.linalg.norm(full_ellipse_points - end_point, axis=1)

        start_idx = np.argmin(start_distances)
        end_idx = np.argmin(end_distances)

        corrected_start_param = full_params[start_idx]
        corrected_end_param = full_params[end_idx]

        # 7. 从完整图形中截取正确的椭圆弧段
        if corrected_end_param < corrected_start_param:
            corrected_end_param += 2*np.pi

        param_range = corrected_end_param - corrected_start_param
        num_points = max(int(self.sample_points_count * param_range / (2*np.pi)), 3)

        params = np.linspace(corrected_start_param, corrected_end_param, num_points)
        final_sample_points = []

        for param in params:
            x_std = a * np.cos(param)
            y_std = b * np.sin(param)

            x_rot = x_std * np.cos(angle) - y_std * np.sin(angle)
            y_rot = x_std * np.sin(angle) + y_std * np.cos(angle)

            x = center[0] + x_rot
            y = center[1] + y_rot

            final_sample_points.append([x, y])

        return {
            'type': 'POLYLINE',
            'points': final_sample_points,
            'layer': ellipse_entity.get('layer', 'default'),
            'color': ellipse_entity.get('color', 'purple'),
            'original_type': 'ELLIPSE',
            'reconstruction_method': 'user_suggested',
            'reconstruction_info': {
                'center': center.tolist(),
                'a': a,
                'b': b,
                'angle': angle,
                'start_param': corrected_start_param,
                'end_param': corrected_end_param,
                'start_point': start_point.tolist(),
                'end_point': end_point.tolist(),
                'is_mirrored': is_mirrored
            }
        }

    def _handedness_correction_method(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        方案7: 手性校正法
        基于DXF拉伸方向检测坐标系手性并进行校正
        """
        if not self.coord_detector:
            # 如果检测器不可用，返回原始实体转换为多段线
            return self._convert_to_polylines_for_display(entities)

        reconstructed = []

        for entity in entities:
            if entity['type'] == 'ARC':
                # 使用坐标系检测器校正圆弧
                corrected_entity = self.coord_detector.correct_arc_angles_by_handedness(entity)
                # 转换为多段线显示
                polyline_entity = self._convert_corrected_arc_to_polyline(corrected_entity)
                reconstructed.append(polyline_entity)
            elif entity['type'] == 'ELLIPSE':
                # 使用坐标系检测器校正椭圆
                corrected_entity = self.coord_detector.correct_ellipse_params_by_handedness(entity)
                # 转换为多段线显示
                polyline_entity = self._convert_corrected_ellipse_to_polyline(corrected_entity)
                reconstructed.append(polyline_entity)
            else:
                reconstructed.append(entity)

        return reconstructed

    def _convert_corrected_arc_to_polyline(self, corrected_arc: Dict[str, Any]) -> Dict[str, Any]:
        """将校正后的圆弧转换为多段线"""
        center = np.array(corrected_arc['center'])
        radius = corrected_arc['radius']
        start_angle = corrected_arc.get('start_angle', 0)
        end_angle = corrected_arc.get('end_angle', 360)

        # 角度单位转换
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)

        # 处理角度范围
        if end_angle < start_angle:
            end_angle += 360

        num_points = max(int(self.sample_points_count * (end_angle - start_angle) / 360), 3)
        angles = np.linspace(np.radians(start_angle), np.radians(end_angle), num_points)

        x_coords = center[0] + radius * np.cos(angles)
        y_coords = center[1] + radius * np.sin(angles)

        sample_points = np.column_stack((x_coords, y_coords))

        return {
            'type': 'POLYLINE',
            'points': sample_points.tolist(),
            'layer': corrected_arc.get('layer', 'default'),
            'color': corrected_arc.get('color', 'red'),
            'original_type': 'ARC',
            'reconstruction_method': 'handedness_correction',
            'coordinate_system_correction': corrected_arc.get('coordinate_system_correction', {})
        }

    def _convert_corrected_ellipse_to_polyline(self, corrected_ellipse: Dict[str, Any]) -> Dict[str, Any]:
        """将校正后的椭圆转换为多段线"""
        center = np.array(corrected_ellipse['center'])
        major_axis = np.array(corrected_ellipse['major_axis'])
        ratio = corrected_ellipse.get('ratio', 1.0)
        start_param = corrected_ellipse.get('start_param', 0)
        end_param = corrected_ellipse.get('end_param', 2 * np.pi)

        # 计算椭圆参数
        a = np.linalg.norm(major_axis)
        b = a * ratio
        angle = np.arctan2(major_axis[1], major_axis[0])

        # 处理参数范围
        if end_param < start_param:
            end_param += 2 * np.pi

        param_range = end_param - start_param
        num_points = max(int(self.sample_points_count * param_range / (2 * np.pi)), 3)
        params = np.linspace(start_param, end_param, num_points)

        # 计算椭圆上的点
        x_std = a * np.cos(params)
        y_std = b * np.sin(params)

        # 应用旋转
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)
        x_rot = x_std * cos_angle - y_std * sin_angle
        y_rot = x_std * sin_angle + y_std * cos_angle

        # 平移到中心
        x_coords = center[0] + x_rot
        y_coords = center[1] + y_rot

        sample_points = np.column_stack((x_coords, y_coords))

        return {
            'type': 'POLYLINE',
            'points': sample_points.tolist(),
            'layer': corrected_ellipse.get('layer', 'default'),
            'color': corrected_ellipse.get('color', 'purple'),
            'original_type': 'ELLIPSE',
            'reconstruction_method': 'handedness_correction',
            'coordinate_system_correction': corrected_ellipse.get('coordinate_system_correction', {})
        }
