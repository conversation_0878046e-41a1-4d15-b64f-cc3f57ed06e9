#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的DXF测试程序
用于验证修复后的功能，避免复杂的界面问题
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Circle, Arc, Ellipse as MplEllipse
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置中文字体
def setup_chinese_font():
    """设置中文字体支持"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

setup_chinese_font()

class SimpleDXFTest:
    """简化的DXF测试程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("简化DXF测试程序")
        self.root.geometry("800x600")
        
        self.entities = []
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="选择DXF文件", 
                  command=self.select_file).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="使用测试数据", 
                  command=self.load_test_data).pack(side=tk.LEFT, padx=(0, 10))
        
        # 显示区域
        display_frame = ttk.Frame(self.root)
        display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建matplotlib图形
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.ax.set_title('DXF文件显示测试')
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, display_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 信息面板
        info_frame = ttk.Frame(self.root)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_text = tk.Text(info_frame, height=6, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def select_file(self):
        """选择DXF文件"""
        file_path = filedialog.askopenfilename(
            title="选择DXF文件",
            filetypes=[("DXF文件", "*.dxf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_dxf_file(file_path)
    
    def load_dxf_file(self, file_path):
        """加载DXF文件"""
        try:
            self.log_info(f"🔄 加载文件: {os.path.basename(file_path)}")
            
            # 尝试使用ezdxf库
            try:
                import ezdxf
                doc = ezdxf.readfile(file_path)
                msp = doc.modelspace()
                
                self.entities = []
                for entity in msp:
                    converted = self.convert_ezdxf_entity(entity)
                    if converted:
                        self.entities.append(converted)
                
                self.log_info(f"✅ 成功加载 {len(self.entities)} 个实体")
                self.update_display()
                
            except ImportError:
                self.log_info("⚠️ ezdxf库未安装，请安装: pip install ezdxf")
                messagebox.showwarning("警告", "需要安装ezdxf库才能读取DXF文件\n请运行: pip install ezdxf")
            
        except Exception as e:
            error_msg = f"❌ 加载文件失败: {str(e)}"
            self.log_info(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def convert_ezdxf_entity(self, entity):
        """转换ezdxf实体为内部格式"""
        try:
            entity_type = entity.dxftype()
            
            if entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                return {
                    'type': 'LINE',
                    'points': [(start.x, start.y), (end.x, end.y)],
                    'color': 'blue'
                }
            
            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                return {
                    'type': 'CIRCLE',
                    'center': (center.x, center.y),
                    'radius': entity.dxf.radius,
                    'color': 'green'
                }
            
            elif entity_type == 'ARC':
                center = entity.dxf.center
                return {
                    'type': 'ARC',
                    'center': (center.x, center.y),
                    'radius': entity.dxf.radius,
                    'start_angle': entity.dxf.start_angle,
                    'end_angle': entity.dxf.end_angle,
                    'color': 'red'
                }
            
            elif entity_type == 'ELLIPSE':
                center = entity.dxf.center
                major_axis = entity.dxf.major_axis
                return {
                    'type': 'ELLIPSE',
                    'center': (center.x, center.y),
                    'major_axis': (major_axis.x, major_axis.y),
                    'ratio': entity.dxf.ratio,
                    'start_param': entity.dxf.start_param,
                    'end_param': entity.dxf.end_param,
                    'color': 'purple'
                }
            
            return None
            
        except Exception as e:
            self.log_info(f"⚠️ 转换实体失败: {entity_type if 'entity_type' in locals() else 'UNKNOWN'}, 错误: {e}")
            return None
    
    def load_test_data(self):
        """加载测试数据"""
        self.entities = [
            # 普通直线
            {
                'type': 'LINE',
                'points': [(10, 10), (50, 50)],
                'color': 'blue'
            },
            # 普通圆
            {
                'type': 'CIRCLE',
                'center': (100, 100),
                'radius': 20,
                'color': 'green'
            },
            # 普通圆弧
            {
                'type': 'ARC',
                'center': (200, 100),
                'radius': 25,
                'start_angle': 0,
                'end_angle': 90,
                'color': 'red'
            },
            # 镜像圆弧
            {
                'type': 'ARC',
                'center': (300, 100),
                'radius': 20,
                'start_angle': 0,
                'end_angle': 180,
                'scale_x': -1.0,
                'scale_y': 1.0,
                'color': 'red'
            },
            # 椭圆
            {
                'type': 'ELLIPSE',
                'center': (150, 200),
                'major_axis': (30, 0),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': 2 * np.pi,
                'color': 'purple'
            }
        ]
        
        self.log_info(f"✅ 加载测试数据，共 {len(self.entities)} 个实体")
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        if not self.entities:
            return
        
        # 清空画布
        self.ax.clear()
        self.ax.set_title('DXF文件显示测试')
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        
        # 绘制实体
        for entity in self.entities:
            self.draw_entity(entity)
        
        # 调整视图
        self.ax.relim()
        self.ax.autoscale()
        
        # 刷新画布
        self.canvas.draw()
    
    def draw_entity(self, entity):
        """绘制实体"""
        try:
            entity_type = entity.get('type', 'UNKNOWN')
            color = entity.get('color', 'black')
            
            if entity_type == 'LINE':
                points = entity['points']
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                self.ax.plot(x_coords, y_coords, color=color, linewidth=2)
            
            elif entity_type == 'CIRCLE':
                center = entity['center']
                radius = entity['radius']
                circle = Circle(center, radius, fill=False, edgecolor=color, linewidth=2)
                self.ax.add_patch(circle)
            
            elif entity_type == 'ARC':
                center = entity['center']
                radius = entity['radius']
                start_angle = entity.get('start_angle', 0)
                end_angle = entity.get('end_angle', 360)
                
                # 角度单位转换
                if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                    start_angle = np.degrees(start_angle)
                    end_angle = np.degrees(end_angle)
                
                arc = Arc(center, 2*radius, 2*radius,
                         theta1=start_angle, theta2=end_angle,
                         edgecolor=color, linewidth=2, fill=False)
                self.ax.add_patch(arc)
            
            elif entity_type == 'ELLIPSE':
                center = entity['center']
                major_axis = entity['major_axis']
                ratio = entity.get('ratio', 1.0)
                
                a = np.linalg.norm(major_axis)
                b = a * ratio
                angle = np.degrees(np.arctan2(major_axis[1], major_axis[0]))
                
                ellipse = MplEllipse(center, 2*a, 2*b, angle=angle,
                                   fill=False, edgecolor=color, linewidth=2)
                self.ax.add_patch(ellipse)
        
        except Exception as e:
            self.log_info(f"⚠️ 绘制实体失败: {entity_type}, 错误: {e}")
    
    def log_info(self, message):
        """记录信息"""
        self.info_text.insert(tk.END, message + "\n")
        self.info_text.see(tk.END)
        self.root.update_idletasks()
        print(message)

def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleDXFTest(root)
    
    # 显示使用说明
    app.log_info("简化DXF测试程序")
    app.log_info("1. 点击'选择DXF文件'加载真实DXF文件")
    app.log_info("2. 点击'使用测试数据'加载模拟数据")
    app.log_info("3. 观察图形显示效果")
    app.log_info("注意: 需要安装ezdxf库才能读取真实DXF文件")
    
    root.mainloop()

if __name__ == "__main__":
    main()
