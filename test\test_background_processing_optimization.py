#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试后台处理优化效果
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_background_processing_optimization():
    """测试后台处理优化效果"""
    print("=== 测试后台处理优化效果 ===")
    
    print("\n1. 问题分析:")
    print("  原问题:")
    print("  - 🔍 检测到后台处理: 处理器文件(wall02.dxf) != 显示文件(wall01.dxf) (重复多次)")
    print("  - 显示手动分组组: 1/1, 实体数量: 56")
    print("  - 更新详细视图...")
    print("  - 更新全图概览...")
    print("  - 可视化更新成功")
    print("  - 多次调用 update_file_combo()")
    
    print("\n2. 修复方案:")
    print("  A. 减少重复日志输出:")
    print("     - 使用日志缓存机制，同一文件只输出一次后台处理检测日志")
    print("     - 避免频繁的状态检查日志")
    
    print("  B. 禁用后台处理的界面更新:")
    print("     - 创建后台处理器时禁用所有回调函数")
    print("     - 在 _show_group 方法中检查后台处理标记")
    print("     - 移除后台处理中的 update_file_combo() 调用")
    
    print("  C. 优化处理流程:")
    print("     - 后台处理器设置 _is_background_processing = True")
    print("     - 禁用 status_callback 和 progress_callback")
    print("     - 界面更新方法检查处理类型，跳过后台处理")
    
    print("\n3. 修复效果验证:")
    
    # 模拟修复后的处理流程
    class MockBackgroundProcessor:
        def __init__(self):
            self._is_background_processing = True
            self.status_callback = lambda status_type, data: None
            self.progress_callback = lambda current, total, message: None
            self.current_file = "wall02.dxf"
    
    class MockApp:
        def __init__(self):
            self.display_file = "wall01.dxf"
            self.processor = None
            self._background_log_cache = set()
        
        def _is_processing_display_file(self):
            """检查当前是否在处理显示文件（优化：减少重复日志）"""
            if not hasattr(self, 'display_file') or not self.display_file:
                return True
            
            if not hasattr(self, 'processor') or not self.processor:
                return True
            
            processor_file = getattr(self.processor, 'current_file', None)
            if not processor_file:
                return True
            
            processor_file_name = os.path.basename(processor_file) if processor_file else ""
            display_file_name = os.path.basename(self.display_file) if self.display_file else ""
            
            is_display = processor_file_name == display_file_name
            
            # 只在第一次检测到后台处理时输出日志，避免重复
            if not is_display:
                log_key = f"background_detected_{processor_file_name}"
                if log_key not in self._background_log_cache:
                    print(f"    🔍 检测到后台处理: 处理器文件({processor_file_name}) != 显示文件({display_file_name})")
                    self._background_log_cache.add(log_key)
            
            return is_display
        
        def _show_group(self, group, group_index=None):
            """显示指定组（支持后台处理检查）"""
            # 检查是否在后台处理中，如果是则跳过界面更新
            if hasattr(self.processor, '_is_background_processing') and self.processor._is_background_processing:
                print(f"    跳过后台处理的界面更新: 组{group_index if group_index else '未知'}")
                return
            
            # 检查当前是否在处理显示文件
            if not self._is_processing_display_file():
                print(f"    跳过后台文件的界面更新: 组{group_index if group_index else '未知'}")
                return
            
            # 正常的界面更新
            print("    更新详细视图...")
            print("    更新全图概览...")
            print("    可视化更新成功")
        
        def update_file_combo(self):
            """更新文件下拉菜单"""
            if not self._is_processing_display_file():
                print("    跳过后台处理的文件下拉菜单更新")
                return
            print("    更新文件下拉菜单")
    
    app = MockApp()
    
    print("\n  测试场景1: 显示文件处理")
    app.processor = MockBackgroundProcessor()
    app.processor.current_file = "wall01.dxf"  # 与显示文件相同
    app.processor._is_background_processing = False  # 前台处理
    
    print("    处理显示文件: wall01.dxf")
    app._show_group([], 1)
    app.update_file_combo()
    
    print("\n  测试场景2: 后台文件处理（第一次）")
    app.processor = MockBackgroundProcessor()
    app.processor.current_file = "wall02.dxf"  # 与显示文件不同
    
    print("    处理后台文件: wall02.dxf")
    app._show_group([], 1)
    app.update_file_combo()
    
    print("\n  测试场景3: 后台文件处理（重复调用）")
    print("    再次处理后台文件: wall02.dxf")
    app._show_group([], 2)  # 重复调用
    app._show_group([], 3)  # 重复调用
    app.update_file_combo()  # 重复调用
    
    print("\n=== 测试结果 ===")
    print("✅ 显示文件处理时正常更新界面")
    print("✅ 后台文件处理时跳过界面更新")
    print("✅ 重复的后台处理检测日志被优化")
    print("✅ 后台处理不再调用 update_file_combo()")
    
    print("\n🎯 优化要点:")
    print("1. 日志缓存机制: 同一文件的后台处理检测只输出一次日志")
    print("2. 回调禁用: 后台处理器的 status_callback 和 progress_callback 被禁用")
    print("3. 界面更新检查: _show_group 和 update_file_combo 检查处理类型")
    print("4. 标记机制: _is_background_processing 标记区分前台和后台处理")
    
    print("\n📋 被优化的操作:")
    print("- 🔍 检测到后台处理 (减少重复日志)")
    print("- 显示手动分组组 (后台处理时跳过)")
    print("- 更新详细视图 (后台处理时跳过)")
    print("- 更新全图概览 (后台处理时跳过)")
    print("- 可视化更新 (后台处理时跳过)")
    print("- update_file_combo() (后台处理时跳过)")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_background_processing_optimization()
