#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强版修复
"""

import sys
import os

def test_imports():
    """测试导入是否正常"""
    try:
        from main_enhanced import EnhancedCADProcessor, EnhancedCADApp
        from cad_visualizer import CADVisualizer
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_processor_creation():
    """测试处理器创建"""
    try:
        from main_enhanced import EnhancedCADProcessor
        processor = EnhancedCADProcessor()
        print("✓ 处理器创建成功")
        return True
    except Exception as e:
        print(f"✗ 处理器创建失败: {e}")
        return False

def test_visualizer_creation():
    """测试可视化器创建"""
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        print("✓ 可视化器创建成功")
        return True
    except Exception as e:
        print(f"✗ 可视化器创建失败: {e}")
        return False

def test_enhanced_methods():
    """测试增强的方法"""
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_visualizer import CADVisualizer
        
        processor = EnhancedCADProcessor()
        visualizer = CADVisualizer()
        
        # 测试_show_group方法的新签名
        # 创建一个模拟组
        mock_group = [{'type': 'LINE', 'layer': 'test', 'points': [(0, 0), (10, 10)]}]
        processor.all_groups = [mock_group]
        processor.current_file_entities = mock_group
        processor.auto_labeled_entities = []
        processor.labeled_entities = []
        processor.visualizer = visualizer
        
        # 测试带组索引的_show_group调用
        processor._show_group(mock_group, 1)
        print("✓ _show_group方法增强测试通过")
        
        # 测试组状态更新
        processor._update_groups_info()
        print("✓ 组状态更新测试通过")
        
        # 测试跳转方法
        processor.jump_to_group(1)
        print("✓ 跳转方法测试通过")
        
        return True
    except Exception as e:
        print(f"✗ 增强方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualizer_overview():
    """测试可视化器概览方法"""
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 测试带组索引的visualize_overview调用
        mock_entities = [{'type': 'LINE', 'layer': 'test', 'points': [(0, 0), (10, 10)]}]
        mock_group = [mock_entities[0]]
        
        visualizer.visualize_overview(
            all_entities=mock_entities,
            current_group_entities=mock_group,
            labeled_entities=[],
            processor=None,
            current_group_index=1,
            wall_fills=None,
            wall_fill_processor=None
        )
        print("✓ 可视化器概览方法增强测试通过")
        
        return True
    except Exception as e:
        print(f"✗ 可视化器概览方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_relabel_functionality():
    """测试重新分类功能"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 创建模拟数据
        mock_group = [
            {'type': 'LINE', 'layer': 'test', 'points': [(0, 0), (10, 10)], 'auto_labeled': True, 'label': 'wall'}
        ]
        processor.all_groups = [mock_group]
        processor.current_file = "test.dxf"
        processor.dataset = []
        processor.labeled_entities = []
        
        # 测试重新分类
        result = processor.relabel_group(1, 'door_window')
        if result:
            print("✓ 重新分类功能测试通过")
        else:
            print("✗ 重新分类功能测试失败")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 重新分类功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试增强版修复...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_processor_creation,
        test_visualizer_creation,
        test_enhanced_methods,
        test_visualizer_overview,
        test_relabel_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强版修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
