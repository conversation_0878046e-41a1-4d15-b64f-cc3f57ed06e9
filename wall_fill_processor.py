#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
墙体填充处理器 - 兼容性包装器
这个文件是为了保持向后兼容性，实际功能由 wall_fill_processor_enhanced_v2.py 提供
"""

try:
    from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
    
    # 为了向后兼容，创建一个别名
    WallFillProcessor = EnhancedWallFillProcessorV2
    
except ImportError as e:
    print(f"警告: 无法导入增强版墙体填充处理器: {e}")
    
    # 创建一个基本的占位符类
    class WallFillProcessor:
        """墙体填充处理器占位符类"""
        
        def __init__(self):
            print("警告: 使用墙体填充处理器占位符，功能受限")
        
        def process_wall_filling(self, entities, connection_threshold=20):
            """处理墙体填充（占位符实现）"""
            print("警告: 墙体填充功能不可用，请使用融合版本")
            return []
        
        def create_fill_polygons(self, wall_group):
            """创建填充多边形（占位符实现）"""
            print("警告: 填充多边形创建功能不可用")
            return [] 