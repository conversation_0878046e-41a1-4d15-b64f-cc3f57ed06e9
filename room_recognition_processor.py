#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别处理器
功能：
1. 建筑外轮廓识别
2. 房间识别（客厅、卧室、阳台、厨房、卫生间、杂物间、其他房间、设备平台）
3. 房间切分功能
4. 房间类型修改

依据：已识别的墙体组和门窗栏杆组
- 通过墙体和门窗组识别建筑外轮廓
- 通过墙体间的门窗来切分房间
"""

import numpy as np
from shapely.geometry import Polygon, Point, LineString, MultiPolygon
from shapely.ops import unary_union, polygonize
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon as MplPolygon
import tkinter as tk
from tkinter import ttk, messagebox
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict

# 尝试导入R树索引，如果不可用则使用简单实现
try:
    from rtree import index
    RTREE_AVAILABLE = True
except ImportError:
    RTREE_AVAILABLE = False
    print("⚠️ R树索引不可用，将使用简单空间查询")


class RoomRecognitionProcessor:
    """房间识别处理器"""
    
    def __init__(self):
        """初始化房间识别处理器"""
        self.rooms = []  # 识别到的房间列表
        self.building_outline = None  # 建筑外轮廓
        self.wall_groups = []  # 墙体组
        self.door_window_groups = []  # 门窗组

        # 空间索引
        self.spatial_index = None
        self.wall_lines = []
        self.door_window_lines = []

        # 拓扑关系缓存
        self.wall_connections = defaultdict(list)  # 墙-墙连接
        self.door_wall_map = {}  # 门-墙映射
        self.tolerance = 20  # 空间查询容差

        # Lazy Evaluation缓存
        self._room_cache = None
        self._building_outline_cache = None
        
        # 房间类型定义（增强版 - 包含新的分类）
        self.room_types = [
            '客厅', '卧室', '阳台', '厨房', '卫生间',
            '杂物间', '其他房间', '设备平台', '墙体空腔'
        ]

        # 房间类型颜色映射（增强版）
        self.room_colors = {
            '客厅': '#FFE4B5',      # 浅橙色
            '卧室': '#E6E6FA',      # 淡紫色
            '阳台': '#F0FFF0',      # 蜜瓜色
            '厨房': '#FFF8DC',      # 玉米丝色
            '卫生间': '#E0FFFF',    # 浅青色
            '杂物间': '#F5F5DC',    # 米色
            '其他房间': '#F0F8FF',  # 爱丽丝蓝
            '墙体空腔': '#D3D3D3',  # 浅灰色
            '设备平台': '#F5F5F5'   # 白烟色
        }
    
    def process_room_recognition(self, wall_groups, door_window_groups):
        """
        处理房间识别
        
        Args:
            wall_groups: 墙体组列表
            door_window_groups: 门窗组列表
            
        Returns:
            dict: 包含建筑外轮廓和房间信息的字典
        """
        try:
            print("🏠 开始房间识别处理...")
            
            self.wall_groups = wall_groups
            self.door_window_groups = door_window_groups
            
            # 1. 识别建筑外轮廓
            self.building_outline = self._identify_building_outline()
            
            # 2. 识别房间
            self.rooms = self._identify_rooms()
            
            # 3. 自动分类房间
            self._classify_rooms_automatically()
            
            result = {
                'building_outline': self.building_outline,
                'rooms': self.rooms,
                'wall_groups': self.wall_groups,
                'door_window_groups': self.door_window_groups
            }
            
            print(f"✅ 房间识别完成：建筑外轮廓 {'已识别' if self.building_outline else '未识别'}，房间数量 {len(self.rooms)}")
            
            return result

        except Exception as e:
            print(f"❌ 房间识别处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _build_spatial_index(self):
        """构建空间索引加速几何查询"""
        try:
            print("🔍 构建空间索引...")

            # 收集所有墙体线段
            self.wall_lines = []
            for group in self.wall_groups:
                for entity in group:
                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        for coord in line_coords:
                            if len(coord) >= 2:
                                self.wall_lines.append(LineString(coord))

            # 收集所有门窗线段
            self.door_window_lines = []
            for group in self.door_window_groups:
                for entity in group:
                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        for coord in line_coords:
                            if len(coord) >= 2:
                                self.door_window_lines.append(LineString(coord))

            # 构建R树索引（如果可用）
            if RTREE_AVAILABLE:
                self.spatial_index = index.Index()
                # 填充空间索引
                for i, line in enumerate(self.wall_lines):
                    self.spatial_index.insert(i, line.bounds)
                print(f"✅ R树索引构建完成: {len(self.wall_lines)} 条墙体线段")
            else:
                print("⚠️ 使用简单空间查询")

        except Exception as e:
            print(f"❌ 构建空间索引失败: {e}")
            self.spatial_index = None
    
    def _identify_building_outline(self):
        """优化建筑外轮廓识别"""
        try:
            print("🔍 识别建筑外轮廓...")

            if not self.wall_groups:
                print("❌ 没有墙体组，无法识别建筑外轮廓")
                return None

            # 使用已构建的墙体线段
            if not self.wall_lines:
                self._build_spatial_index()

            if not self.wall_lines:
                print("❌ 没有有效的墙体线段")
                return None

            # 合并所有墙体
            merged = unary_union(self.wall_lines).buffer(self.tolerance)

            # 提取最大多边形
            if isinstance(merged, MultiPolygon):
                largest_polygon = max(merged.geoms, key=lambda p: p.area)
            else:
                largest_polygon = merged

            print(f"✅ 识别到建筑外轮廓，面积: {largest_polygon.area:.2f}")

            return largest_polygon

        except Exception as e:
            print(f"❌ 识别建筑外轮廓失败: {e}")
            return None
    
    def _identify_rooms(self):
        """识别房间（增强版 - 实现详细的房间划分逻辑）"""
        try:
            print("🏠 开始详细房间识别...")

            if not self.wall_groups:
                print("❌ 没有墙体组，无法识别房间")
                return []

            # 构建空间索引
            self._build_spatial_index()

            # 使用并行处理优化门窗组处理
            temp_lines = self._process_door_window_groups_parallel()

            # 步骤2: 收集所有墙体实体和临时线条
            print("🔍 步骤2: 收集墙体实体和临时线条...")
            all_line_geometries = self._collect_all_line_geometries(temp_lines)

            if not all_line_geometries:
                print("❌ 没有有效的线段用于房间识别")
                return []

            # 步骤3: 检测围合区域
            print("🔍 步骤3: 检测围合区域...")
            all_polygons = list(polygonize(all_line_geometries))

            if not all_polygons:
                print("❌ 无法生成围合区域")
                return []

            print(f"✅ 检测到 {len(all_polygons)} 个围合区域")

            # 步骤4: 区域分组和分类
            print("🔍 步骤4: 区域分组和分类...")
            rooms = self._classify_regions(all_polygons)

            print(f"✅ 识别到 {len(rooms)} 个房间")

            return rooms

        except Exception as e:
            print(f"❌ 识别房间失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _process_door_window_groups_parallel(self):
        """并行处理门窗组"""
        try:
            print("🔍 并行处理门窗组...")

            with ThreadPoolExecutor() as executor:
                # 并行处理门窗组
                futures = [executor.submit(self._process_door_window_group, group)
                          for group in self.door_window_groups]
                temp_lines = []
                for future in futures:
                    temp_lines.extend(future.result())

            print(f"✅ 并行处理完成，生成 {len(temp_lines)} 条临时线条")
            return temp_lines

        except Exception as e:
            print(f"❌ 并行处理门窗组失败: {e}")
            # 回退到串行处理
            return self._detect_door_window_wall_intersections()

    def _process_door_window_group(self, door_window_group):
        """处理单个门窗组"""
        temp_lines = []
        tolerance = self.tolerance
        min_distance = 300
        alignment_tolerance = 10

        try:
            # 收集门窗组的线段
            door_window_lines = []
            for entity in door_window_group:
                line_coords = self._extract_line_coordinates(entity)
                if line_coords:
                    for coord in line_coords:
                        if len(coord) >= 2:
                            door_window_lines.append(LineString(coord))

            if not door_window_lines:
                return []

            # 使用空间索引查找相交的墙体
            intersection_points = []
            for dw_line in door_window_lines:
                if self.spatial_index and RTREE_AVAILABLE:
                    # 使用R树索引查询
                    possible_walls = [self.wall_lines[i]
                                    for i in self.spatial_index.intersection(dw_line.bounds)]
                else:
                    # 简单查询
                    possible_walls = self.wall_lines

                for wall_line in possible_walls:
                    # 碰撞检测
                    dw_buffered = dw_line.buffer(tolerance)
                    wall_buffered = wall_line.buffer(tolerance)

                    if dw_buffered.intersects(wall_buffered):
                        intersection = dw_buffered.intersection(wall_buffered)
                        if hasattr(intersection, 'centroid'):
                            point = intersection.centroid
                            intersection_points.append((point.x, point.y))

            # 去重交点
            unique_points = self._merge_close_points(intersection_points, tolerance)

            if len(unique_points) >= 2:
                # 生成临时线条
                temp_lines = self._create_temp_lines(unique_points)

            return temp_lines

        except Exception as e:
            print(f"❌ 处理门窗组失败: {e}")
            return []

    def _detect_door_window_wall_intersections(self):
        """检测门窗与墙体交点，生成临时线条"""
        temp_lines = []
        tolerance = 20  # 容差
        min_distance = 300  # 最小距离
        alignment_tolerance = 10  # 水平/垂直对齐容差
        large_distance_threshold = 400  # 大距离阈值

        try:
            print(f"🔍 检测门窗与墙体交点（容差: {tolerance}）...")

            # 收集所有墙体线段
            wall_lines = []
            for group in self.wall_groups:
                for entity in group:
                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        for coord in line_coords:
                            if len(coord) >= 2:
                                wall_lines.append(LineString(coord))

            print(f"✅ 收集到 {len(wall_lines)} 条墙体线段")

            # 检测每个门窗组
            for group_idx, door_window_group in enumerate(self.door_window_groups):
                print(f"🚪 检测门窗组 {group_idx + 1}...")

                # 收集门窗组的线段
                door_window_lines = []
                for entity in door_window_group:
                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        for coord in line_coords:
                            if len(coord) >= 2:
                                door_window_lines.append(LineString(coord))

                if not door_window_lines:
                    continue

                # 检测与墙体的交点
                intersection_points = []
                for dw_line in door_window_lines:
                    for wall_line in wall_lines:
                        # 检测交点（使用缓冲区增加容差）
                        dw_buffered = dw_line.buffer(tolerance)
                        wall_buffered = wall_line.buffer(tolerance)

                        if dw_buffered.intersects(wall_buffered):
                            intersection = dw_buffered.intersection(wall_buffered)

                            # 提取交点坐标
                            if hasattr(intersection, 'centroid'):
                                point = intersection.centroid
                                intersection_points.append((point.x, point.y))

                # 去重交点（距离小于容差的点合并）
                unique_points = self._merge_close_points(intersection_points, tolerance)

                if len(unique_points) >= 2:
                    print(f"   ✅ 找到 {len(unique_points)} 个交点")

                    # 检查交点是否在水平或垂直线上
                    temp_lines_from_group = self._create_temp_lines_from_intersections(
                        unique_points, door_window_group, min_distance,
                        alignment_tolerance, large_distance_threshold
                    )

                    temp_lines.extend(temp_lines_from_group)
                else:
                    print(f"   ⚠️ 交点不足（{len(unique_points)}个），跳过")

            print(f"✅ 生成 {len(temp_lines)} 条临时线条")
            return temp_lines

        except Exception as e:
            print(f"❌ 检测门窗与墙体交点失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _merge_close_points(self, points, tolerance):
        """合并距离小于容差的点"""
        if not points:
            return []

        unique_points = []
        for point in points:
            is_duplicate = False
            for existing_point in unique_points:
                distance = np.sqrt((point[0] - existing_point[0])**2 + (point[1] - existing_point[1])**2)
                if distance < tolerance:
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_points.append(point)

        return unique_points

    def _create_temp_lines_from_intersections(self, points, door_window_group,
                                            min_distance, alignment_tolerance, large_distance_threshold):
        """从交点创建临时线条"""
        temp_lines = []

        try:
            # 检查点是否在水平或垂直线上
            for i in range(len(points)):
                for j in range(i + 1, len(points)):
                    p1, p2 = points[i], points[j]
                    distance = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

                    if distance > min_distance:
                        # 检查是否水平对齐
                        if abs(p1[1] - p2[1]) <= alignment_tolerance:
                            print(f"   🔗 创建水平临时线条: {p1} -> {p2}")
                            temp_lines.append(LineString([p1, p2]))

                        # 检查是否垂直对齐
                        elif abs(p1[0] - p2[0]) <= alignment_tolerance:
                            print(f"   🔗 创建垂直临时线条: {p1} -> {p2}")
                            temp_lines.append(LineString([p1, p2]))

                        # 如果距离很大，复制门窗组线条
                        elif distance >= large_distance_threshold:
                            print(f"   📋 距离较大({distance:.1f})，复制门窗组线条")
                            for entity in door_window_group:
                                line_coords = self._extract_line_coordinates(entity)
                                if line_coords:
                                    for coord in line_coords:
                                        if len(coord) >= 2:
                                            temp_lines.append(LineString(coord))

            return temp_lines

        except Exception as e:
            print(f"❌ 创建临时线条失败: {e}")
            return []

    def _create_temp_lines(self, points):
        """生成优化后的临时线条"""
        lines = []
        sorted_points = sorted(points, key=lambda p: p[0])  # X轴排序

        # 水平线条检测
        for i in range(len(sorted_points)):
            group = [sorted_points[i]]
            for j in range(i+1, len(sorted_points)):
                if abs(sorted_points[j][1] - sorted_points[i][1]) < self.tolerance:
                    group.append(sorted_points[j])
            if len(group) > 1:
                group.sort(key=lambda p: p[0])
                lines.append(LineString([group[0], group[-1]]))

        # 垂直线条检测
        sorted_points_y = sorted(points, key=lambda p: p[1])  # Y轴排序
        for i in range(len(sorted_points_y)):
            group = [sorted_points_y[i]]
            for j in range(i+1, len(sorted_points_y)):
                if abs(sorted_points_y[j][0] - sorted_points_y[i][0]) < self.tolerance:
                    group.append(sorted_points_y[j])
            if len(group) > 1:
                group.sort(key=lambda p: p[1])
                lines.append(LineString([group[0], group[-1]]))

        return lines

    def _build_topology_cache(self):
        """构建拓扑关系缓存"""
        try:
            print("🔍 构建拓扑关系缓存...")

            self.wall_connections = defaultdict(list)  # 墙-墙连接
            self.door_wall_map = {}  # 门-墙映射

            # 构建墙体连接关系
            for i, wall in enumerate(self.wall_lines):
                for j, other in enumerate(self.wall_lines[i+1:], i+1):
                    if wall.distance(other) < self.tolerance:
                        self.wall_connections[i].append(j)
                        self.wall_connections[j].append(i)

            # 构建门窗-墙体映射
            for door in self.door_window_lines:
                closest_wall_idx = None
                min_distance = float('inf')
                for i, wall in enumerate(self.wall_lines):
                    distance = wall.distance(door)
                    if distance < min_distance:
                        min_distance = distance
                        closest_wall_idx = i

                if closest_wall_idx is not None:
                    self.door_wall_map[id(door)] = closest_wall_idx

            print(f"✅ 拓扑关系缓存构建完成: {len(self.wall_connections)} 个墙体连接")

        except Exception as e:
            print(f"❌ 构建拓扑关系缓存失败: {e}")

    def update_geometry(self, new_walls, new_doors):
        """增量更新几何数据"""
        try:
            print("🔄 增量更新几何数据...")

            # 只处理新增/修改的墙体
            updated_lines = self._process_new_walls(new_walls)

            # 只处理受影响的门窗
            affected_doors = self._find_affected_doors(new_walls)
            temp_lines = self._process_door_window_groups_parallel()

            # 局部重计算房间
            self._recalculate_rooms(updated_lines + temp_lines)

            print("✅ 增量更新完成")

        except Exception as e:
            print(f"❌ 增量更新失败: {e}")

    def _process_new_walls(self, new_walls):
        """处理新增墙体"""
        updated_lines = []
        for wall_group in new_walls:
            for entity in wall_group:
                line_coords = self._extract_line_coordinates(entity)
                if line_coords:
                    for coord in line_coords:
                        if len(coord) >= 2:
                            updated_lines.append(LineString(coord))
        return updated_lines

    def _find_affected_doors(self, new_walls):
        """查找受影响的门窗"""
        # 简化实现：返回所有门窗组
        return self.door_window_groups

    def _recalculate_rooms(self, updated_lines):
        """局部重计算房间"""
        # 简化实现：重新计算所有房间
        all_line_geometries = self.wall_lines + updated_lines
        all_polygons = list(polygonize(all_line_geometries))
        self.rooms = self._classify_regions(all_polygons)

    def debug_visualization(self, filename='room_debug.html'):
        """生成交互式调试可视化"""
        try:
            print(f"🎨 生成调试可视化: {filename}")

            # 创建简单的HTML可视化
            html_content = self._generate_debug_html()

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"✅ 调试可视化已保存: {filename}")

        except Exception as e:
            print(f"❌ 生成调试可视化失败: {e}")

    def _generate_debug_html(self):
        """生成调试HTML内容"""
        html_template = """<!DOCTYPE html>
<html>
<head>
    <title>房间识别调试</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .room {{ margin: 10px 0; padding: 10px; border: 1px solid #ccc; }}
        .stats {{ background: #f0f0f0; padding: 10px; margin: 10px 0; }}
    </style>
</head>
<body>
    <h1>房间识别调试信息</h1>

    <div class="stats">
        <h2>统计信息</h2>
        <p>墙体组数量: {wall_groups_count}</p>
        <p>门窗组数量: {door_window_groups_count}</p>
        <p>识别房间数量: {rooms_count}</p>
        <p>建筑外轮廓: {building_outline_status}</p>
    </div>

    <h2>房间详情</h2>
    {rooms_html}

</body>
</html>"""

        html = html_template.format(
            wall_groups_count=len(self.wall_groups),
            door_window_groups_count=len(self.door_window_groups),
            rooms_count=len(self.rooms),
            building_outline_status="已识别" if self.building_outline else "未识别",
            rooms_html=self._generate_rooms_html()
        )

        return html

    def _generate_rooms_html(self):
        """生成房间HTML内容"""
        rooms_html = ""
        for i, room in enumerate(self.rooms):
            color = self.room_colors.get(room['type'], '#CCCCCC')
            rooms_html += f"""
            <div class="room" style="border-left: 5px solid {color};">
                <h3>房间 {i+1}: {room['type']}</h3>
                <p>面积: {room['area']:.1f}</p>
                <p>最小宽度: {room.get('width', 0):.1f}</p>
                <p>长宽比: {room.get('aspect_ratio', 1.0):.2f}</p>
                <p>中心点: ({room['centroid'].x:.1f}, {room['centroid'].y:.1f})</p>
            </div>
            """
        return rooms_html

    @property
    def rooms_lazy(self):
        """Lazy Evaluation的房间属性"""
        if self._room_cache is None:
            self._room_cache = self._identify_rooms()
        return self._room_cache

    @property
    def building_outline_lazy(self):
        """Lazy Evaluation的建筑外轮廓属性"""
        if self._building_outline_cache is None:
            self._building_outline_cache = self._identify_building_outline()
        return self._building_outline_cache

    def invalidate_cache(self):
        """清除缓存，强制重新计算"""
        self._room_cache = None
        self._building_outline_cache = None

    def _collect_all_line_geometries(self, temp_lines):
        """收集所有墙体实体和临时线条"""
        all_line_geometries = []

        try:
            # 添加墙体线段
            wall_count = 0
            print(f"🔍 开始收集墙体线段，共 {len(self.wall_groups)} 个墙体组...")

            for group_idx, group in enumerate(self.wall_groups):
                print(f"   处理墙体组 {group_idx+1}: {len(group)} 个实体")

                for entity_idx, entity in enumerate(group):
                    # 调试：显示实体结构
                    entity_type = entity.get('type', 'UNKNOWN')
                    print(f"      实体 {entity_idx+1}: 类型={entity_type}")

                    # 显示实体的关键字段
                    if entity_type == 'LINE':
                        start = entity.get('start')
                        end = entity.get('end')
                        print(f"         start={start}, end={end}")
                    elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                        points = entity.get('points', [])
                        print(f"         points数量={len(points)}")
                    else:
                        # 显示实体的所有键
                        keys = list(entity.keys())
                        print(f"         可用键: {keys[:10]}...")  # 只显示前10个键

                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        print(f"         ✅ 提取到 {len(line_coords)} 条线段")
                        for coord in line_coords:
                            if len(coord) >= 2:
                                all_line_geometries.append(LineString(coord))
                                wall_count += 1
                    else:
                        print(f"         ❌ 未能提取线段坐标")

            print(f"✅ 收集到 {wall_count} 条墙体线段")

            # 添加临时线条
            temp_count = len(temp_lines)
            all_line_geometries.extend(temp_lines)

            print(f"✅ 添加 {temp_count} 条临时线条")
            print(f"✅ 总计 {len(all_line_geometries)} 条线段用于围合")

            return all_line_geometries

        except Exception as e:
            print(f"❌ 收集线段失败: {e}")
            return []

    def _classify_regions(self, all_polygons):
        """区域分组和分类"""
        rooms = []

        try:
            print(f"🏷️ 开始区域分类...")

            # 过滤有效多边形
            valid_polygons = []
            for polygon in all_polygons:
                if polygon.is_valid and polygon.area > 50:  # 最小面积阈值
                    valid_polygons.append(polygon)

            print(f"✅ 有效区域: {len(valid_polygons)} 个")

            if not valid_polygons:
                return []

            # 步骤3: 宽度小于400的区域合并为一个区域
            narrow_regions, wide_regions = self._separate_narrow_wide_regions(valid_polygons, 400)

            # 合并窄区域
            if narrow_regions:
                merged_narrow = self._merge_narrow_regions(narrow_regions)
                if merged_narrow:
                    room = {
                        'geometry': merged_narrow,
                        'type': '墙体空腔',
                        'area': merged_narrow.area,
                        'centroid': merged_narrow.centroid,
                        'width': self._calculate_min_width(merged_narrow)
                    }
                    rooms.append(room)
                    print(f"✅ 合并窄区域: 面积 {merged_narrow.area:.1f}, 宽度 {room['width']:.1f}")

            # 步骤4-9: 分类宽区域
            for polygon in wide_regions:
                room = self._classify_single_region(polygon)
                rooms.append(room)

            # 步骤5: 面积最大的标记为客厅
            if rooms:
                largest_room = max(rooms, key=lambda r: r['area'])
                if largest_room['type'] != '墙体空腔':  # 空腔不能是客厅
                    largest_room['type'] = '客厅'
                    print(f"✅ 最大区域标记为客厅: 面积 {largest_room['area']:.1f}")

            # 输出分类结果
            type_counts = {}
            for room in rooms:
                room_type = room['type']
                type_counts[room_type] = type_counts.get(room_type, 0) + 1

            print("📊 房间分类结果:")
            for room_type, count in type_counts.items():
                print(f"   {room_type}: {count} 个")

            return rooms

        except Exception as e:
            print(f"❌ 区域分类失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _separate_narrow_wide_regions(self, polygons, width_threshold):
        """分离窄区域和宽区域"""
        narrow_regions = []
        wide_regions = []

        for polygon in polygons:
            min_width = self._calculate_min_width(polygon)
            if min_width < width_threshold:
                narrow_regions.append(polygon)
            else:
                wide_regions.append(polygon)

        print(f"✅ 窄区域({width_threshold}以下): {len(narrow_regions)} 个")
        print(f"✅ 宽区域({width_threshold}以上): {len(wide_regions)} 个")

        return narrow_regions, wide_regions

    def _calculate_min_width(self, polygon):
        """计算多边形的最小宽度（短边）"""
        try:
            # 获取边界框
            minx, miny, maxx, maxy = polygon.bounds

            # 计算宽度和高度
            width = maxx - minx
            height = maxy - miny

            # 返回较小的值作为最小宽度
            return min(width, height)

        except Exception as e:
            print(f"❌ 计算最小宽度失败: {e}")
            return 0

    def _merge_narrow_regions(self, narrow_regions):
        """合并窄区域"""
        try:
            if not narrow_regions:
                return None

            if len(narrow_regions) == 1:
                return narrow_regions[0]

            # 使用unary_union合并所有窄区域
            merged = unary_union(narrow_regions)

            # 如果结果是MultiPolygon，选择最大的部分
            if isinstance(merged, MultiPolygon):
                merged = max(merged.geoms, key=lambda p: p.area)

            return merged

        except Exception as e:
            print(f"❌ 合并窄区域失败: {e}")
            return None

    def _classify_single_region(self, polygon):
        """增强版房间分类"""
        try:
            area = polygon.area
            min_width = self._calculate_min_width(polygon)
            bounds = polygon.bounds
            max_dimension = max(bounds[2] - bounds[0], bounds[3] - bounds[1])
            aspect_ratio = max_dimension / min_width if min_width > 0 else 1.0

            # 基于多种特征决策
            if min_width < 400:
                room_type = '墙体空腔'
            elif aspect_ratio > 3.0 and min_width < 2000:
                room_type = '阳台'
            elif 800 < area < 4000 and min_width > 1500:
                room_type = '卧室'
            elif 3000 < area < 8000 and min_width > 2000:
                room_type = '客厅'
            elif area < 3000 and min_width > 1200:
                room_type = '厨房' if aspect_ratio < 2.0 else '卫生间'
            else:
                room_type = '其他房间'

            room = {
                'geometry': polygon,
                'type': room_type,
                'area': area,
                'centroid': polygon.centroid,
                'width': min_width,
                'aspect_ratio': aspect_ratio
            }

            print(f"✅ 区域分类: {room_type}, 面积: {area:.1f}, 宽度: {min_width:.1f}, 长宽比: {aspect_ratio:.2f}")

            return room

        except Exception as e:
            print(f"❌ 分类单个区域失败: {e}")
            return {
                'geometry': polygon,
                'type': '其他房间',
                'area': polygon.area if polygon else 0,
                'centroid': polygon.centroid if polygon else Point(0, 0),
                'width': 0,
                'aspect_ratio': 1.0
            }
    
    def _extract_line_coordinates(self, entity):
        """统一实体坐标提取方法"""
        # 优先尝试标准结构
        if 'start' in entity and 'end' in entity:
            start = entity['start']
            end = entity['end']
            if isinstance(start, dict) and isinstance(end, dict):
                return [[(start.get('x', 0), start.get('y', 0)),
                        (end.get('x', 0), end.get('y', 0))]]

        # 尝试扁平化结构
        if all(k in entity for k in ['start_x', 'start_y', 'end_x', 'end_y']):
            return [[(entity['start_x'], entity['start_y']),
                    (entity['end_x'], entity['end_y'])]]

        # 处理多段线
        if 'points' in entity:
            points = entity['points']
            if isinstance(points, list) and len(points) >= 2:
                if all(isinstance(p, dict) for p in points):
                    coords = [(p.get('x', 0), p.get('y', 0)) for p in points]
                    return [coords] if len(coords) >= 2 else []
                if all(isinstance(p, (tuple, list)) for p in points):
                    return [points] if len(points) >= 2 else []

        # 最后尝试几何对象
        if 'geometry' in entity and 'coordinates' in entity['geometry']:
            coords = entity['geometry']['coordinates']
            if isinstance(coords, list) and len(coords) >= 2:
                return [coords]

        return []  # 所有方法均失败
    
    def _classify_rooms_automatically(self):
        """自动分类房间"""
        try:
            print("🤖 自动分类房间...")
            
            for room in self.rooms:
                area = room['area']
                
                # 基于面积的简单分类规则
                if area > 2000:
                    room['type'] = '客厅'
                elif area > 1000:
                    room['type'] = '卧室'
                elif area > 500:
                    if area < 800:
                        room['type'] = '卫生间'
                    else:
                        room['type'] = '厨房'
                elif area > 200:
                    room['type'] = '阳台'
                else:
                    room['type'] = '杂物间'
            
            print("✅ 房间自动分类完成")
            
        except Exception as e:
            print(f"❌ 房间自动分类失败: {e}")
    
    def update_room_type(self, room_index, new_type):
        """更新房间类型"""
        try:
            if 0 <= room_index < len(self.rooms):
                if new_type in self.room_types:
                    old_type = self.rooms[room_index]['type']
                    self.rooms[room_index]['type'] = new_type
                    print(f"✅ 房间 {room_index + 1} 类型已从 '{old_type}' 更改为 '{new_type}'")
                    return True
                else:
                    print(f"❌ 无效的房间类型: {new_type}")
                    return False
            else:
                print(f"❌ 无效的房间索引: {room_index}")
                return False
                
        except Exception as e:
            print(f"❌ 更新房间类型失败: {e}")
            return False
    
    def get_room_statistics(self):
        """获取房间统计信息"""
        try:
            stats = {}
            total_area = 0
            
            for room in self.rooms:
                room_type = room['type']
                area = room['area']
                
                if room_type not in stats:
                    stats[room_type] = {'count': 0, 'total_area': 0}
                
                stats[room_type]['count'] += 1
                stats[room_type]['total_area'] += area
                total_area += area
            
            # 添加总计信息
            stats['总计'] = {
                'count': len(self.rooms),
                'total_area': total_area
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取房间统计信息失败: {e}")
            return {}
    
    def export_room_data(self):
        """导出房间数据"""
        try:
            room_data = []
            
            for i, room in enumerate(self.rooms):
                data = {
                    'room_id': i + 1,
                    'type': room['type'],
                    'area': room['area'],
                    'centroid_x': room['centroid'].x,
                    'centroid_y': room['centroid'].y,
                    'color': self.room_colors.get(room['type'], '#F0F8FF')
                }
                room_data.append(data)
            
            return {
                'building_outline_area': self.building_outline.area if self.building_outline else 0,
                'total_rooms': len(self.rooms),
                'rooms': room_data,
                'statistics': self.get_room_statistics()
            }
            
        except Exception as e:
            print(f"❌ 导出房间数据失败: {e}")
            return None
