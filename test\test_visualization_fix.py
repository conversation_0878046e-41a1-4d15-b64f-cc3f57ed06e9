#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化修复
验证CADVisualizer初始化问题的修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_visualization_fix():
    """测试可视化修复"""
    print("🔄 测试可视化修复...")
    print("🎯 验证CADVisualizer初始化问题修复")
    print("=" * 70)
    
    try:
        # 测试CADVisualizer的正确初始化
        print("1. 测试CADVisualizer初始化:")
        from cad_visualizer import CADVisualizer
        
        # 测试默认初始化
        visualizer = CADVisualizer()
        print("  ✅ CADVisualizer默认初始化成功")
        
        # 检查图形属性
        if hasattr(visualizer, 'fig'):
            print(f"  ✅ 图形对象存在，大小: {visualizer.fig.get_size_inches()}")
        
        if hasattr(visualizer, 'ax_detail') and hasattr(visualizer, 'ax_overview'):
            print("  ✅ 详细视图和概览视图轴存在")
        
        # 测试图形大小调整
        print("\n2. 测试图形大小调整:")
        try:
            visualizer.fig.set_size_inches(10, 5)
            visualizer.fig.tight_layout()
            print("  ✅ 图形大小调整成功")
        except Exception as e:
            print(f"  ❌ 图形大小调整失败: {e}")
        
        # 测试主程序类初始化
        print("\n3. 测试主程序类初始化:")
        try:
            import tkinter as tk
            
            # 创建测试根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口，仅用于测试
            
            from main_enhanced_with_v2_fill import EnhancedCADAppV2
            
            # 测试类的创建（不完全初始化UI）
            print("  ✅ EnhancedCADAppV2类导入成功")
            
            # 检查关键方法是否存在
            methods_to_check = [
                '_create_visualization',
                '_clear_group_highlight',
                '_create_color_system_with_zoom',
                '_open_zoom_window'
            ]
            
            for method_name in methods_to_check:
                if hasattr(EnhancedCADAppV2, method_name):
                    print(f"  ✅ 方法 {method_name} 存在")
                else:
                    print(f"  ❌ 方法 {method_name} 不存在")
            
            root.destroy()
            
        except Exception as e:
            print(f"  ❌ 主程序类测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_matplotlib_integration():
    """测试matplotlib集成"""
    print("\n🧪 测试matplotlib集成:")
    
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        import matplotlib.pyplot as plt
        
        print("  ✅ matplotlib导入成功")
        print(f"  ✅ 后端设置: {matplotlib.get_backend()}")
        
        # 测试图形创建
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        print("  ✅ 图形创建成功")
        
        # 测试大小调整
        fig.set_size_inches(10, 5)
        print("  ✅ 图形大小调整成功")
        
        plt.close(fig)
        print("  ✅ 图形关闭成功")
        
    except Exception as e:
        print(f"  ❌ matplotlib集成测试失败: {e}")

def create_fix_summary():
    """创建修复总结"""
    print("\n💡 修复总结:")
    print("""
🔧 问题分析:
----------------------------------------
错误信息: CADVisualizer.__init__() got an unexpected keyword argument 'figsize'
原因: CADVisualizer的__init__方法不接受figsize参数

🔧 修复方案:
----------------------------------------
1. 移除figsize参数:
   - 使用CADVisualizer()默认初始化
   - 不传递figsize参数

2. 后续调整图形大小:
   - 使用visualizer.fig.set_size_inches(10, 5)
   - 调用visualizer.fig.tight_layout()

3. 固定画布大小:
   - 使用canvas_widget.config(width=800, height=400)
   - 使用pack(side='top', anchor='n')固定位置

🔧 修复后的代码:
----------------------------------------
```python
# 创建可视化器（使用默认大小）
self.visualizer = CADVisualizer()

# 创建画布并设置固定大小
self.canvas = FigureCanvasTkAgg(self.visualizer.get_figure(), canvas_frame)
canvas_widget = self.canvas.get_tk_widget()

# 设置画布的固定大小
canvas_widget.config(width=800, height=400)
canvas_widget.pack(side='top', anchor='n')

# 调整图形大小以适应固定画布
self.visualizer.fig.set_size_inches(10, 5)
self.visualizer.fig.tight_layout()
```

🎯 修复效果:
----------------------------------------
1. 可视化器正常初始化
2. 窗口大小固定为800x400像素
3. 图形内容自适应固定窗口
4. 保持原有的双视图布局（详细视图+概览视图）

🚀 验证方法:
----------------------------------------
1. 运行 main_enhanced_with_v2_fill.py
2. 观察可视化区域是否正常显示
3. 检查窗口大小是否固定
4. 测试缩放按钮功能
""")

def main():
    """主函数"""
    print("=" * 70)
    print("🧪 可视化修复测试")
    print("🎯 验证CADVisualizer初始化问题修复")
    print("=" * 70)
    
    success = test_visualization_fix()
    test_matplotlib_integration()
    
    if success:
        print("\n🎉 可视化修复验证完成！")
        print("\n✨ 修复内容:")
        print("  1. 移除了不支持的figsize参数")
        print("  2. 使用默认初始化后调整图形大小")
        print("  3. 通过画布配置实现固定窗口大小")
        print("  4. 保持了原有的功能完整性")
        
        create_fix_summary()
        
        print("\n🚀 现在可以正常运行程序:")
        print("  1. 运行 main_enhanced_with_v2_fill.py")
        print("  2. 可视化区域应该正常显示")
        print("  3. 窗口大小固定，内容自适应")
        print("  4. 所有UI改进功能正常工作")
    else:
        print("\n❌ 可视化修复验证失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
