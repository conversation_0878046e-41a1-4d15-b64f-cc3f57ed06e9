#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志输出模块
用于导出CAD分类标注工具的处理日志
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

class LogExporter:
    """日志导出器"""
    
    def __init__(self, app_instance=None):
        """
        初始化日志导出器
        
        Args:
            app_instance: 主程序实例，用于获取处理数据
        """
        self.app = app_instance
        self.log_dir = "测试日志"
        self.ensure_log_directory()
    
    def ensure_log_directory(self):
        """确保日志目录存在"""
        try:
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)
                print(f"📁 创建日志目录: {self.log_dir}")
        except Exception as e:
            print(f"❌ 创建日志目录失败: {e}")
    
    def generate_log_filename(self) -> str:
        """
        生成日志文件名
        格式: YYYYMMDD-HHMM.txt
        
        Returns:
            str: 日志文件名
        """
        now = datetime.now()
        filename = now.strftime("%Y%m%d-%H%M.txt")
        return os.path.join(self.log_dir, filename)
    
    def collect_processing_data(self) -> Dict[str, Any]:
        """
        收集处理数据
        
        Returns:
            Dict[str, Any]: 处理数据字典
        """
        data = {
            "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_files": 0,
            "processed_files": 0,
            "completed_files": 0,
            "failed_files": 0,
            "files_detail": [],
            "processing_summary": {},
            "system_info": self._get_system_info()
        }
        
        if not self.app:
            print("⚠️ 没有应用实例，无法收集详细数据")
            return data
        
        try:
            # 收集文件状态信息
            if hasattr(self.app, 'file_status') and self.app.file_status:
                data["total_files"] = len(self.app.file_status)
                
                for file_name, status_info in self.app.file_status.items():
                    file_detail = {
                        "file_name": file_name,
                        "processing_status": status_info.get('processing_status', 'unknown'),
                        "annotation_status": status_info.get('annotation_status', 'unknown'),
                        "file_path": status_info.get('file_path', ''),
                        "entities_count": 0,
                        "groups_count": 0,
                        "labeled_entities_count": 0,
                        "processing_time": "未知"
                    }
                    
                    # 统计处理状态
                    proc_status = status_info.get('processing_status', 'unprocessed')
                    if proc_status == 'completed':
                        data["completed_files"] += 1
                    elif proc_status == 'processing':
                        data["processed_files"] += 1
                    elif proc_status == 'failed':
                        data["failed_files"] += 1
                    
                    # 收集详细数据
                    if hasattr(self.app, 'file_data') and file_name in self.app.file_data:
                        file_data = self.app.file_data[file_name]
                        
                        entities = file_data.get('entities', [])
                        groups = file_data.get('groups', [])
                        labeled_entities = file_data.get('labeled_entities', [])
                        
                        file_detail["entities_count"] = len(entities) if entities else 0
                        file_detail["groups_count"] = len(groups) if groups else 0
                        file_detail["labeled_entities_count"] = len(labeled_entities) if labeled_entities else 0
                        file_detail["timestamp"] = file_data.get('timestamp', '未知')
                    
                    data["files_detail"].append(file_detail)
            
            # 收集处理器信息
            if hasattr(self.app, 'processor') and self.app.processor:
                processor_info = {
                    "current_file": getattr(self.app.processor, 'current_file', ''),
                    "current_group_index": getattr(self.app.processor, 'current_group_index', 0),
                    "total_entities": len(getattr(self.app.processor, 'current_file_entities', [])),
                    "total_groups": len(getattr(self.app.processor, 'all_groups', [])),
                    "labeled_entities": len(getattr(self.app.processor, 'labeled_entities', [])),
                    "auto_labeled_entities": len(getattr(self.app.processor, 'auto_labeled_entities', []))
                }
                data["processing_summary"] = processor_info
            
            # 收集显示文件信息
            if hasattr(self.app, 'display_file'):
                data["current_display_file"] = self.app.display_file
            
            print(f"✅ 数据收集完成: {data['total_files']}个文件")
            
        except Exception as e:
            print(f"❌ 数据收集失败: {e}")
            import traceback
            traceback.print_exc()
        
        return data
    
    def _get_system_info(self) -> Dict[str, str]:
        """获取系统信息"""
        import platform
        return {
            "python_version": sys.version,
            "platform": platform.platform(),
            "architecture": platform.architecture()[0],
            "processor": platform.processor(),
            "current_directory": os.getcwd()
        }
    
    def format_log_content(self, data: Dict[str, Any]) -> str:
        """
        格式化日志内容
        
        Args:
            data: 处理数据
            
        Returns:
            str: 格式化的日志内容
        """
        lines = []
        
        # 标题和时间
        lines.append("=" * 80)
        lines.append("CAD分类标注工具 - 处理日志")
        lines.append("=" * 80)
        lines.append(f"导出时间: {data['export_time']}")
        lines.append("")
        
        # 处理概要
        lines.append("📊 处理概要")
        lines.append("-" * 40)
        lines.append(f"总文件数: {data['total_files']}")
        lines.append(f"已完成: {data['completed_files']}")
        lines.append(f"处理中: {data['processed_files']}")
        lines.append(f"失败: {data['failed_files']}")
        lines.append(f"未处理: {data['total_files'] - data['completed_files'] - data['processed_files'] - data['failed_files']}")
        lines.append("")
        
        # 当前处理器状态
        if data.get('processing_summary'):
            lines.append("🔧 当前处理器状态")
            lines.append("-" * 40)
            summary = data['processing_summary']
            lines.append(f"当前文件: {summary.get('current_file', '无')}")
            lines.append(f"当前组索引: {summary.get('current_group_index', 0)}")
            lines.append(f"总实体数: {summary.get('total_entities', 0)}")
            lines.append(f"总组数: {summary.get('total_groups', 0)}")
            lines.append(f"已标注实体: {summary.get('labeled_entities', 0)}")
            lines.append(f"自动标注实体: {summary.get('auto_labeled_entities', 0)}")
            lines.append("")
        
        # 当前显示文件
        if data.get('current_display_file'):
            lines.append(f"📁 当前显示文件: {data['current_display_file']}")
            lines.append("")
        
        # 文件详细信息
        lines.append("📋 文件详细信息")
        lines.append("-" * 80)
        lines.append(f"{'文件名':<30} {'处理状态':<12} {'标注状态':<12} {'实体数':<8} {'组数':<6} {'已标注':<8}")
        lines.append("-" * 80)
        
        for file_detail in data['files_detail']:
            file_name = file_detail['file_name']
            if len(file_name) > 28:
                file_name = file_name[:25] + "..."
            
            proc_status = file_detail['processing_status']
            anno_status = file_detail['annotation_status']
            entities = file_detail['entities_count']
            groups = file_detail['groups_count']
            labeled = file_detail['labeled_entities_count']
            
            lines.append(f"{file_name:<30} {proc_status:<12} {anno_status:<12} {entities:<8} {groups:<6} {labeled:<8}")
        
        lines.append("")
        
        # 系统信息
        lines.append("💻 系统信息")
        lines.append("-" * 40)
        sys_info = data['system_info']
        lines.append(f"Python版本: {sys_info['python_version'].split()[0]}")
        lines.append(f"操作系统: {sys_info['platform']}")
        lines.append(f"架构: {sys_info['architecture']}")
        lines.append(f"处理器: {sys_info['processor']}")
        lines.append(f"工作目录: {sys_info['current_directory']}")
        lines.append("")
        
        # 结尾
        lines.append("=" * 80)
        lines.append("日志导出完成")
        lines.append("=" * 80)
        
        return "\n".join(lines)
    
    def export_log(self, trigger_reason: str = "手动触发") -> Optional[str]:
        """
        导出日志
        
        Args:
            trigger_reason: 触发原因
            
        Returns:
            Optional[str]: 导出的日志文件路径，失败时返回None
        """
        try:
            print(f"📝 开始导出日志 - 触发原因: {trigger_reason}")
            
            # 收集数据
            data = self.collect_processing_data()
            
            # 格式化内容
            content = self.format_log_content(data)
            
            # 生成文件名
            log_file = self.generate_log_filename()
            
            # 写入文件
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"触发原因: {trigger_reason}\n\n")
                f.write(content)
            
            print(f"✅ 日志导出成功: {log_file}")
            return log_file
            
        except Exception as e:
            print(f"❌ 日志导出失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def export_json_log(self, trigger_reason: str = "手动触发") -> Optional[str]:
        """
        导出JSON格式的日志（用于程序处理）
        
        Args:
            trigger_reason: 触发原因
            
        Returns:
            Optional[str]: 导出的JSON日志文件路径，失败时返回None
        """
        try:
            # 收集数据
            data = self.collect_processing_data()
            data["trigger_reason"] = trigger_reason
            
            # 生成JSON文件名
            now = datetime.now()
            json_filename = now.strftime("%Y%m%d-%H%M.json")
            json_file = os.path.join(self.log_dir, json_filename)
            
            # 写入JSON文件
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ JSON日志导出成功: {json_file}")
            return json_file
            
        except Exception as e:
            print(f"❌ JSON日志导出失败: {e}")
            import traceback
            traceback.print_exc()
            return None


def test_log_exporter():
    """测试日志导出器"""
    print("=== 测试日志导出器 ===")
    
    # 创建测试实例
    exporter = LogExporter()
    
    # 测试导出
    log_file = exporter.export_log("测试触发")
    json_file = exporter.export_json_log("测试触发")
    
    if log_file:
        print(f"📄 文本日志: {log_file}")
    if json_file:
        print(f"📄 JSON日志: {json_file}")
    
    print("=== 测试完成 ===")


if __name__ == "__main__":
    test_log_exporter()
