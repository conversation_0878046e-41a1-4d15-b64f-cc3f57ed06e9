#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文件切换修复效果
验证：
1. IndexError修复
2. 文件切换后自动跳转到第一个待标注组
3. 界面完整更新（预览图、组列表）
"""

import os
import sys
import tkinter as tk
from unittest.mock import Mock, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_indexerror_fixes():
    """测试IndexError修复"""
    print("=" * 60)
    print("测试IndexError修复")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = EnhancedCADProcessorWithV2Fill(root)
        
        # 创建模拟的TreeView
        app.group_tree = Mock()
        
        # 测试1：values为空列表的情况
        print("\n🔍 测试1: values为空列表")
        app.group_tree.selection.return_value = ['item1']
        app.group_tree.item.return_value = {'text': '组1', 'values': []}
        
        # 创建模拟事件
        event = Mock()
        
        try:
            app.on_group_double_click(event)
            print("✅ 空values列表处理成功，没有IndexError")
        except IndexError as e:
            print(f"❌ 仍然出现IndexError: {e}")
        except Exception as e:
            print(f"✅ 其他异常（预期）: {e}")
        
        # 测试2：values为None的情况
        print("\n🔍 测试2: values为None")
        app.group_tree.item.return_value = {'text': '组2', 'values': None}
        
        try:
            app.on_group_double_click(event)
            print("✅ None values处理成功，没有IndexError")
        except IndexError as e:
            print(f"❌ 仍然出现IndexError: {e}")
        except Exception as e:
            print(f"✅ 其他异常（预期）: {e}")
        
        # 测试3：正常values的情况
        print("\n🔍 测试3: 正常values")
        app.group_tree.item.return_value = {'text': '组3', 'values': ['待标注', '墙体', '5个实体']}
        
        try:
            app.on_group_double_click(event)
            print("✅ 正常values处理成功")
        except Exception as e:
            print(f"✅ 其他异常（预期）: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_first_unlabeled_group_finding():
    """测试查找第一个未标注组的逻辑"""
    print("\n" + "=" * 60)
    print("测试查找第一个未标注组")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        from main_enhanced import EnhancedCADProcessor
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.processor = EnhancedCADProcessor()
        
        # 设置测试数据
        app.processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],  # 已标注
            [{'type': 'LINE'}],  # 未标注
            [{'type': 'CIRCLE', 'label': 'door'}],  # 已标注
            [{'type': 'ARC'}],  # 未标注
        ]
        
        app.processor.groups_info = [
            {'status': 'labeled'},
            {'status': 'unlabeled'},
            {'status': 'labeled'},
            {'status': 'unlabeled'},
        ]
        
        # 测试查找第一个未标注组
        print("\n🔍 测试查找第一个未标注组")
        first_unlabeled = app._find_first_unlabeled_group()
        
        if first_unlabeled == 1:  # 索引1对应组2
            print(f"✅ 正确找到第一个未标注组: 索引{first_unlabeled} (组{first_unlabeled + 1})")
        else:
            print(f"❌ 找到的组索引不正确: {first_unlabeled}")
        
        # 测试所有组都已标注的情况
        print("\n🔍 测试所有组都已标注")
        app.processor.groups_info = [
            {'status': 'labeled'},
            {'status': 'labeled'},
            {'status': 'labeled'},
            {'status': 'labeled'},
        ]
        
        first_unlabeled = app._find_first_unlabeled_group()
        if first_unlabeled is None:
            print("✅ 正确识别所有组都已标注")
        else:
            print(f"❌ 应该返回None，但返回了: {first_unlabeled}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_switching_ui_update():
    """测试文件切换时的界面更新"""
    print("\n" + "=" * 60)
    print("测试文件切换界面更新")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        from main_enhanced import EnhancedCADProcessor
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.processor = EnhancedCADProcessor()
        
        # 模拟界面组件
        app.update_group_list = Mock()
        app.update_stats = Mock()
        app.canvas = Mock()
        
        # 模拟处理器组件
        app.processor.visualizer = Mock()
        app.processor.current_file_entities = [{'type': 'LINE'}]
        app.processor.all_groups = [
            [{'type': 'LINE'}],  # 未标注
            [{'type': 'CIRCLE', 'label': 'door'}],  # 已标注
        ]
        app.processor.groups_info = [
            {'status': 'unlabeled'},
            {'status': 'labeled'},
        ]
        app.processor.labeled_entities = []
        app.processor.jump_to_first_unlabeled_group = Mock(return_value=True)
        
        # 测试完整界面更新
        print("\n🔍 测试文件切换的完整界面更新")
        
        app._perform_complete_ui_update_for_file_switch("test_file.dxf")
        
        # 验证各个更新方法是否被调用
        update_checks = {
            'update_group_list': app.update_group_list.called,
            'update_stats': app.update_stats.called,
            'canvas.draw': app.canvas.draw.called,
            'jump_to_first_unlabeled_group': app.processor.jump_to_first_unlabeled_group.called,
        }
        
        print("\n📋 界面更新方法调用检查:")
        all_called = True
        for method, called in update_checks.items():
            status = "✅" if called else "❌"
            print(f"  {method}: {status}")
            if not called:
                all_called = False
        
        if all_called:
            print("\n✅ 所有界面更新方法都被正确调用")
        else:
            print("\n❌ 部分界面更新方法未被调用")
        
        root.destroy()
        return all_called
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processor_jump_to_first_unlabeled():
    """测试处理器的跳转到第一个未标注组方法"""
    print("\n" + "=" * 60)
    print("测试处理器跳转方法")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置测试数据
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],  # 已标注
            [{'type': 'LINE'}],  # 未标注
            [{'type': 'CIRCLE'}],  # 未标注
        ]
        
        processor.groups_info = [
            {'status': 'labeled'},
            {'status': 'unlabeled'},
            {'status': 'unlabeled'},
        ]
        
        # 模拟jump_to_group方法
        processor.jump_to_group = Mock()
        processor.get_next_unlabeled_group = Mock(return_value=2)  # 返回组2
        
        # 测试跳转方法
        print("\n🔍 测试跳转到第一个未标注组")
        result = processor.jump_to_first_unlabeled_group()
        
        if result:
            print("✅ 跳转方法执行成功")
            if processor.jump_to_group.called:
                print("✅ jump_to_group方法被调用")
                call_args = processor.jump_to_group.call_args[0]
                print(f"  调用参数: 组{call_args[0]}")
            else:
                print("❌ jump_to_group方法未被调用")
        else:
            print("❌ 跳转方法执行失败")
        
        return result
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试文件切换修复效果...")
    
    test1 = test_indexerror_fixes()
    test2 = test_first_unlabeled_group_finding()
    test3 = test_file_switching_ui_update()
    test4 = test_processor_jump_to_first_unlabeled()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = {
        "IndexError修复": test1,
        "查找第一个未标注组": test2,
        "文件切换界面更新": test3,
        "处理器跳转方法": test4,
    }
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！文件切换修复成功。")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码。")
