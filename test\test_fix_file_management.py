#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件管理功能修复
"""

import sys
import os
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_initialization():
    """测试初始化是否正确"""
    print("=== 测试初始化 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 检查必要的属性是否存在
        required_attrs = [
            'current_file',
            'current_folder', 
            'all_files',
            'file_status',
            'file_data',
            'file_combo',
            'should_stop_background'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(app, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ 缺少属性: {missing_attrs}")
            return False
        else:
            print("✅ 所有必要属性都已初始化")
            
        # 检查初始值
        print(f"current_file: '{app.current_file}'")
        print(f"current_folder: '{app.current_folder}'")
        print(f"all_files: {app.all_files}")
        print(f"file_status: {app.file_status}")
        print(f"should_stop_background: {app.should_stop_background}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_combo_update():
    """测试文件下拉菜单更新"""
    print("\n=== 测试文件下拉菜单更新 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 模拟一些文件数据
        app.all_files = []
        app.current_file = ""
        
        # 测试空文件列表
        app.update_file_combo()
        print("✅ 空文件列表更新成功")
        
        # 模拟有文件的情况
        app.all_files = ['test1.dxf', 'test2.dxf']
        app.current_file = 'test1.dxf'
        app.file_status = {
            'test1.dxf': {
                'processing_status': 'completed',
                'annotation_status': 'incomplete'
            },
            'test2.dxf': {
                'processing_status': 'processing',
                'annotation_status': 'unannotated'
            }
        }
        
        app.update_file_combo()
        print("✅ 有文件列表更新成功")
        
        # 检查下拉菜单的值
        if hasattr(app.file_combo, 'get'):
            current_value = app.file_combo.get()
            print(f"当前选中: {current_value}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 文件下拉菜单更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scan_folder():
    """测试文件夹扫描功能"""
    print("\n=== 测试文件夹扫描功能 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 创建临时测试文件夹
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一些测试文件
            test_files = ['test1.dxf', 'test2.dwg', 'test3.txt', 'test4.dxf']
            for file_name in test_files:
                file_path = os.path.join(temp_dir, file_name)
                with open(file_path, 'w') as f:
                    f.write("test content")
            
            # 设置文件夹并扫描
            app.current_folder = temp_dir
            app._scan_folder_files()
            
            # 检查结果
            expected_cad_files = 3  # test1.dxf, test2.dwg, test4.dxf
            actual_cad_files = len(app.all_files)
            
            if actual_cad_files == expected_cad_files:
                print(f"✅ 扫描到 {actual_cad_files} 个CAD文件（符合预期）")
                
                # 检查文件状态初始化
                for file_path in app.all_files:
                    file_name = os.path.basename(file_path)
                    if file_name in app.file_status:
                        status = app.file_status[file_name]
                        if (status.get('processing_status') == 'unprocessed' and 
                            status.get('annotation_status') == 'unannotated'):
                            print(f"✅ {file_name} 状态初始化正确")
                        else:
                            print(f"❌ {file_name} 状态初始化错误: {status}")
                    else:
                        print(f"❌ {file_name} 状态未初始化")
                
                return True
            else:
                print(f"❌ 扫描结果错误: 期望 {expected_cad_files} 个，实际 {actual_cad_files} 个")
                return False
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ 文件夹扫描测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件管理功能修复测试")
    print("=" * 50)
    
    tests = [
        ("初始化测试", test_initialization),
        ("文件下拉菜单更新测试", test_file_combo_update),
        ("文件夹扫描测试", test_scan_folder)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
