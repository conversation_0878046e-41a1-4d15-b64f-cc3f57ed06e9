#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试门窗识别修复
验证包含匹配逻辑是否正确
"""

def test_door_window_matching():
    """测试门窗匹配逻辑"""
    print("🚀 测试门窗匹配修复...")
    print("="*80)
    
    # 模拟实际可能的标签值
    test_labels = [
        # 纯标签
        '门',
        '窗', 
        'door',
        'window',
        '门窗',
        '栏杆',
        'railing',
        
        # 带数字的标签（从截图看到的）
        '门 5',
        '门5',
        '门 10',
        '门 11',
        '门 4',
        
        # 可能的复合标签
        '自动标注 门',
        '门 已完成',
        '门窗组',
        'door_1',
        'window_2',
        
        # 墙体标签（对比）
        '墙',
        '墙体',
        'wall',
        '墙 1',
        '墙体 2',
        
        # 其他标签
        '其他',
        'other',
        '',
    ]
    
    # 当前的匹配规则（修复后）
    wall_patterns = ['wall', '墙体', '墙']
    door_patterns = ['door', 'window', '门', '窗', '门窗', 'railing', '栏杆']
    
    print("📊 标签匹配测试:")
    
    wall_matches = 0
    door_matches = 0
    no_matches = 0
    
    for label in test_labels:
        label_lower = label.lower()
        
        # 使用包含匹配（修复后的逻辑）
        is_wall = any(wall_pattern in label_lower for wall_pattern in wall_patterns)
        is_door = any(door_pattern in label_lower for door_pattern in door_patterns)
        
        if is_wall:
            print(f"   🧱 墙体: '{label}' -> 匹配")
            wall_matches += 1
        elif is_door:
            print(f"   🚪 门窗: '{label}' -> 匹配")
            door_matches += 1
        else:
            print(f"   ❓ 其他: '{label}' -> 无匹配")
            no_matches += 1
    
    print(f"\n📊 匹配统计:")
    print(f"   墙体匹配: {wall_matches}")
    print(f"   门窗匹配: {door_matches}")
    print(f"   无匹配: {no_matches}")
    print(f"   总计: {len(test_labels)}")
    
    # 验证关键的门标签是否都能匹配
    key_door_labels = ['门', '门 5', '门5', '门 10', '门 11', '门 4']
    door_success = 0
    
    print(f"\n🔍 关键门标签验证:")
    for label in key_door_labels:
        label_lower = label.lower()
        is_door = any(door_pattern in label_lower for door_pattern in door_patterns)
        
        if is_door:
            print(f"   ✅ '{label}' -> 可识别")
            door_success += 1
        else:
            print(f"   ❌ '{label}' -> 无法识别")
    
    print(f"\n📊 关键门标签成功率: {door_success}/{len(key_door_labels)} ({door_success/len(key_door_labels)*100:.1f}%)")
    
    # 测试分类逻辑
    print(f"\n🔧 测试分类逻辑:")
    
    # 模拟实体组
    mock_groups = [
        # 门组（从截图推测的实际情况）
        [
            {'label': '门 5', 'type': 'LINE'},
            {'label': '门 5', 'type': 'LINE'},
            {'label': '门 5', 'type': 'LINE'},
        ],
        [
            {'label': '门 10', 'type': 'LINE'},
            {'label': '门 10', 'type': 'LINE'},
        ],
        [
            {'label': '门', 'type': 'LINE'},
            {'label': '门', 'type': 'LINE'},
        ],
        # 墙组
        [
            {'label': '墙', 'type': 'LINE'},
            {'label': '墙', 'type': 'LINE'},
        ],
    ]
    
    wall_groups = []
    door_window_groups = []
    
    for group_idx, group in enumerate(mock_groups):
        wall_entities = []
        door_window_entities = []
        
        debug_labels = []
        for entity in group:
            label = entity.get('label', '').lower()
            if label:
                debug_labels.append(label)
            
            # 使用修复后的匹配逻辑
            if any(wall_pattern in label for wall_pattern in wall_patterns):
                wall_entities.append(entity)
            elif any(door_pattern in label for door_pattern in door_patterns):
                door_window_entities.append(entity)
        
        unique_labels = list(set(debug_labels))
        print(f"   组 {group_idx+1}: 标签 {unique_labels}")
        print(f"      墙体实体: {len(wall_entities)}")
        print(f"      门窗实体: {len(door_window_entities)}")
        
        if wall_entities:
            wall_groups.append(wall_entities)
            print(f"      -> 归类为墙体组")
        
        if door_window_entities:
            door_window_groups.append(door_window_entities)
            print(f"      -> 归类为门窗组")
    
    print(f"\n📊 最终分类结果:")
    print(f"   墙体组: {len(wall_groups)} 个")
    print(f"   门窗组: {len(door_window_groups)} 个")
    
    # 验证修复效果
    expected_door_groups = 3  # 应该有3个门组
    expected_wall_groups = 1  # 应该有1个墙组
    
    success = (len(door_window_groups) == expected_door_groups and 
               len(wall_groups) == expected_wall_groups and
               door_success == len(key_door_labels))
    
    if success:
        print(f"\n✅ 修复验证成功！")
        print(f"   - 门窗组识别正确: {len(door_window_groups)}/{expected_door_groups}")
        print(f"   - 墙体组识别正确: {len(wall_groups)}/{expected_wall_groups}")
        print(f"   - 关键标签识别正确: {door_success}/{len(key_door_labels)}")
    else:
        print(f"\n❌ 修复验证失败")
        print(f"   - 门窗组: 期望{expected_door_groups}, 实际{len(door_window_groups)}")
        print(f"   - 墙体组: 期望{expected_wall_groups}, 实际{len(wall_groups)}")
        print(f"   - 关键标签: 期望{len(key_door_labels)}, 实际{door_success}")
    
    return success

def main():
    """主函数"""
    print("🚀 门窗识别修复测试")
    print("="*80)
    
    try:
        success = test_door_window_matching()
        
        print("\n" + "="*80)
        print("📊 修复总结:")
        
        if success:
            print("🎉 门窗识别修复成功！")
            print("\n🔧 修复内容:")
            print("   1. ✅ 将精确匹配改为包含匹配")
            print("   2. ✅ 支持带数字的标签（如'门 5'）")
            print("   3. ✅ 支持复合标签（如'自动标注 门'）")
            print("   4. ✅ 保持向下兼容性")
            
            print("\n💡 修复前后对比:")
            print("   修复前: label in ['door', '门'] -> 只匹配'门'")
            print("   修复后: '门' in label -> 匹配'门', '门 5', '门10'等")
            
            print("\n✅ 现在应该可以正确识别门窗组了！")
        else:
            print("❌ 门窗识别修复失败")
            print("💡 请检查匹配逻辑")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
