#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试V2程序启动修复
验证房间识别模块属性错误是否已修复
"""

import sys
import os
import tkinter as tk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_startup_fix():
    """测试启动修复"""
    print("🔧 测试V2程序启动修复...")
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("✅ 主应用模块导入成功")
        
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("🔄 正在创建应用实例...")
        
        # 创建应用实例 - 这里之前会出现 'room_processor' 属性错误
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功 - 属性错误已修复！")
        
        # 验证房间识别模块属性
        if hasattr(app, 'room_processor'):
            print("✅ room_processor 属性存在")
            if app.room_processor:
                print("✅ room_processor 已正确初始化")
            else:
                print("⚠️ room_processor 为 None（可能是模块导入问题）")
        else:
            print("❌ room_processor 属性不存在")
            return False
        
        if hasattr(app, 'room_ui'):
            print("✅ room_ui 属性存在")
        else:
            print("❌ room_ui 属性不存在")
            return False
        
        # 验证初始化方法
        init_methods = [
            '_pre_init_room_recognition',
            '_complete_room_recognition_init'
        ]
        
        for method in init_methods:
            if hasattr(app, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
        
        # 清理
        root.destroy()
        
        print("🎉 V2程序启动修复验证成功！")
        return True
        
    except AttributeError as e:
        if "'room_processor'" in str(e):
            print(f"❌ 房间识别模块属性错误仍然存在: {e}")
            return False
        else:
            print(f"❌ 其他属性错误: {e}")
            return False
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_initialization_order():
    """测试初始化顺序"""
    print("\n📋 测试初始化顺序...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import inspect
        
        # 检查 __init__ 方法的源码
        init_source = inspect.getsource(EnhancedCADAppV2.__init__)
        
        # 验证预初始化在父类初始化之前
        pre_init_pos = init_source.find('_pre_init_room_recognition')
        super_init_pos = init_source.find('super().__init__')
        
        if pre_init_pos != -1 and super_init_pos != -1:
            if pre_init_pos < super_init_pos:
                print("✅ 房间识别预初始化在父类初始化之前 - 顺序正确")
            else:
                print("❌ 初始化顺序错误")
                return False
        else:
            print("❌ 找不到初始化方法调用")
            return False
        
        # 验证完成初始化在父类初始化之后
        complete_init_pos = init_source.find('_complete_room_recognition_init')
        
        if complete_init_pos != -1:
            if complete_init_pos > super_init_pos:
                print("✅ 房间识别完成初始化在父类初始化之后 - 顺序正确")
            else:
                print("❌ 完成初始化顺序错误")
                return False
        else:
            print("❌ 找不到完成初始化方法调用")
            return False
        
        print("✅ 初始化顺序验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 初始化顺序测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始V2程序启动修复验证...")
    print("=" * 60)
    
    # 执行测试
    test1_result = test_startup_fix()
    test2_result = test_initialization_order()
    
    print("\n" + "=" * 60)
    
    if test1_result and test2_result:
        print("🎉 V2程序启动修复验证全部通过！")
        print("\n📝 修复总结:")
        print("1. ✅ 添加了 _pre_init_room_recognition() 预初始化方法")
        print("2. ✅ 在父类初始化之前调用预初始化")
        print("3. ✅ 确保 room_processor 属性在UI创建前就存在")
        print("4. ✅ 添加了 _complete_room_recognition_init() 完成初始化")
        print("5. ✅ 修复了 'room_processor' 属性不存在的错误")
        
        print("\n🎯 现在V2程序可以正常启动，房间识别模块已完全集成！")
        
    else:
        print("❌ 部分测试失败，请检查修复。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
