#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试墙体填充空腔识别修复
"""

import sys
import os

def test_cavity_detection_enabled():
    """测试空腔识别是否已启用"""
    try:
        print("测试空腔识别是否已启用:")
        
        # 检查代码中是否移除了"已禁用"标记
        with open('wall_fill_processor_enhanced_v2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "识别到的空腔数量: 0（已禁用）" in content:
            print("✗ 空腔识别仍然被禁用")
            return False
        else:
            print("✓ 空腔识别已启用")
        
        # 检查是否使用了完整的填充处理
        if "create_fill_polygons_enhanced" in content:
            print("✓ 使用完整的填充处理方法")
        else:
            print("✗ 未使用完整的填充处理方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 空腔识别启用检查失败: {e}")
        return False

def test_cavity_identification_methods():
    """测试空腔识别方法是否存在"""
    try:
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2

        processor = EnhancedWallFillProcessorV2()
        
        print("测试空腔识别方法:")
        
        # 检查空腔识别方法是否存在
        methods = [
            '_identify_cavities',
            '_identify_cavities_from_all_entities',
            '_find_inner_polygons',
            '_build_fill_result_with_cavities',
            'create_fill_polygons_enhanced'
        ]
        
        for method in methods:
            if hasattr(processor, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 空腔识别方法检查失败: {e}")
        return False

def test_enhanced_fill_processing():
    """测试增强填充处理"""
    try:
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2

        processor = EnhancedWallFillProcessorV2()
        
        print("测试增强填充处理:")
        
        # 创建测试墙体组（外轮廓）
        outer_wall_group = [
            {
                'type': 'LINE',
                'points': [(0, 0), (100, 0)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(100, 0), (100, 100)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(100, 100), (0, 100)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(0, 100), (0, 0)],
                'layer': 'wall'
            }
        ]
        
        # 创建测试空腔组（内部小矩形）
        cavity_entities = [
            {
                'type': 'LINE',
                'points': [(30, 30), (70, 30)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(70, 30), (70, 70)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(70, 70), (30, 70)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(30, 70), (30, 30)],
                'layer': 'wall'
            }
        ]
        
        # 合并所有实体
        all_entities = outer_wall_group + cavity_entities
        
        print(f"外轮廓实体数量: {len(outer_wall_group)}")
        print(f"空腔实体数量: {len(cavity_entities)}")
        print(f"总实体数量: {len(all_entities)}")
        
        # 测试增强填充处理
        fill_result = processor.create_fill_polygons_enhanced(outer_wall_group, all_entities)
        
        print(f"填充结果类型: {type(fill_result)}")
        if fill_result:
            print(f"填充区域数量: {len(fill_result.get('fill_polygons', []))}")
            print(f"空腔数量: {len(fill_result.get('cavities', []))}")
            
            if len(fill_result.get('cavities', [])) > 0:
                print("✓ 成功识别到空腔")
                return True
            else:
                print("✗ 未识别到空腔")
                return False
        else:
            print("✗ 填充处理失败")
            return False
        
    except Exception as e:
        print(f"✗ 增强填充处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_debug_area_calculation():
    """测试调试面积计算方法"""
    try:
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2

        processor = EnhancedWallFillProcessorV2()
        
        print("测试调试面积计算方法:")
        
        # 创建简单的测试墙体组
        test_wall_group = [
            {
                'type': 'LINE',
                'points': [(0, 0), (50, 0)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(50, 0), (50, 50)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(50, 50), (0, 50)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(0, 50), (0, 0)],
                'layer': 'wall'
            }
        ]
        
        print(f"测试墙体组实体数量: {len(test_wall_group)}")
        
        # 运行调试面积计算
        processor.debug_area_calculation(test_wall_group)
        
        print("✓ 调试面积计算方法执行成功")
        return True
        
    except Exception as e:
        print(f"✗ 调试面积计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cavity_polygon_creation():
    """测试空腔多边形创建"""
    try:
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2

        processor = EnhancedWallFillProcessorV2()
        
        print("测试空腔多边形创建:")
        
        # 创建空腔实体组
        cavity_entities = [
            {
                'type': 'LINE',
                'points': [(10, 10), (20, 10)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(20, 10), (20, 20)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(20, 20), (10, 20)],
                'layer': 'wall'
            },
            {
                'type': 'LINE',
                'points': [(10, 20), (10, 10)],
                'layer': 'wall'
            }
        ]
        
        print(f"空腔实体数量: {len(cavity_entities)}")
        
        # 测试空腔多边形创建
        cavity_polygon = processor._create_cavity_polygon_from_entities(cavity_entities)
        
        if cavity_polygon:
            print(f"✓ 成功创建空腔多边形，面积: {cavity_polygon.area}")
            return True
        else:
            print("✗ 空腔多边形创建失败")
            return False
        
    except Exception as e:
        print(f"✗ 空腔多边形创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cavity_detection_integration():
    """测试空腔检测集成"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试空腔检测集成:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查是否使用V2处理器
        if hasattr(app, '_auto_fill_walls_impl'):
            print("✓ 自动填充实现方法存在")
        else:
            print("✗ 自动填充实现方法不存在")
            root.destroy()
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 空腔检测集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试墙体填充空腔识别修复...")
    print("=" * 60)
    
    tests = [
        ("空腔识别启用检查", test_cavity_detection_enabled),
        ("空腔识别方法检查", test_cavity_identification_methods),
        ("空腔多边形创建", test_cavity_polygon_creation),
        ("调试面积计算", test_debug_area_calculation),
        ("增强填充处理", test_enhanced_fill_processing),
        ("空腔检测集成", test_cavity_detection_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 墙体填充空腔识别修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
