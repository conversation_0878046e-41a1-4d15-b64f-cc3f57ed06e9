#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配色方案切换修复
"""

import tkinter as tk
import time

def test_color_switch_fix():
    """测试配色方案切换修复"""
    print("🚀 开始配色方案切换修复测试...")
    print("="*60)
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建根窗口
        root = tk.Tk()
        root.title("配色方案切换测试")
        root.geometry("800x600")
        
        print("✅ 创建根窗口成功")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        print("✅ 创建应用实例成功")
        
        # 更新界面确保初始化完成
        root.update()
        time.sleep(1)
        
        # 模拟实体组
        print("\n🎯 模拟实体组...")
        app.entity_groups = {
            0: {'label': 'wall', 'status': 'auto_labeled', 'entities': []},
            1: {'label': 'door_window', 'status': 'auto_labeled', 'entities': []},
            2: {'label': 'furniture', 'status': 'manual_labeled', 'entities': []},
            3: {'label': 'sofa', 'status': 'manual_labeled', 'entities': []}
        }
        print("✅ 实体组模拟完成")
        
        # 测试颜色获取方法
        print("\n🔍 测试颜色获取方法...")
        
        # 创建测试类实例
        class ColorTester:
            def __init__(self, app):
                self.app = app
                self.root = app.root
            
            def get_overview_entity_colors(self):
                """获取全图预览中实体的颜色"""
                entity_colors = {}
                
                try:
                    if hasattr(self.app, 'entity_groups'):
                        for group_id, group_info in self.app.entity_groups.items():
                            label = group_info.get('label', 'unlabeled')
                            status = group_info.get('status', 'unknown')
                            
                            # 从配色方案获取颜色
                            if hasattr(self.app, 'current_color_scheme') and self.app.current_color_scheme:
                                color = self.app.current_color_scheme.get(label, '#808080')
                                entity_colors[f"group_{group_id}_{label}"] = f"current_scheme:{color}"
                            else:
                                entity_colors[f"group_{group_id}_{label}"] = "no_scheme:unknown"
                
                except Exception as e:
                    print(f"❌ 获取实体颜色失败: {e}")
                
                return entity_colors
            
            def analyze_detailed_color_changes(self, colors_before, colors_after):
                """分析详细的颜色变化"""
                print("="*50)
                
                all_entities = set(colors_before.keys()) | set(colors_after.keys())
                
                changes_detected = 0
                for entity_type in sorted(all_entities):
                    before = colors_before.get(entity_type, 'unknown')
                    after = colors_after.get(entity_type, 'unknown')
                    
                    print(f"{entity_type}:")
                    print(f"   配色方案1: {before}")
                    print(f"   配色方案2: {after}")
                    
                    if before != after:
                        print(f"   ✅ 颜色已改变: {before} → {after}")
                        changes_detected += 1
                    else:
                        print(f"   ❌ 颜色未改变")
                    print()
                
                return changes_detected
        
        tester = ColorTester(app)
        
        # 获取初始颜色
        print("📊 获取初始颜色...")
        initial_colors = tester.get_overview_entity_colors()
        print(f"初始颜色: {len(initial_colors)}个实体")
        for entity, color in initial_colors.items():
            print(f"   {entity}: {color}")
        
        # 创建测试配色方案1
        print("\n🎨 应用配色方案1...")
        test_scheme_1 = {
            'wall': '#FF0000',      # 红色
            'door_window': '#0000FF', # 蓝色
            'furniture': '#00FF00',   # 绿色
            'sofa': '#800080',       # 紫色
            'unlabeled': '#808080'   # 灰色
        }
        
        # 更新配色方案
        app.current_color_scheme.update(test_scheme_1)
        print("✅ 配色方案1设置完成")
        
        # 获取配色方案1后的颜色
        scheme1_colors = tester.get_overview_entity_colors()
        print(f"配色方案1颜色: {len(scheme1_colors)}个实体")
        
        # 创建测试配色方案2
        print("\n🎨 应用配色方案2...")
        test_scheme_2 = {
            'wall': '#8B4513',      # 棕色
            'door_window': '#FF6347', # 番茄红
            'furniture': '#32CD32',   # 酸橙绿
            'sofa': '#9370DB',       # 中紫色
            'unlabeled': '#A9A9A9'   # 深灰色
        }
        
        # 更新配色方案
        app.current_color_scheme.update(test_scheme_2)
        print("✅ 配色方案2设置完成")
        
        # 获取配色方案2后的颜色
        scheme2_colors = tester.get_overview_entity_colors()
        print(f"配色方案2颜色: {len(scheme2_colors)}个实体")
        
        # 分析颜色变化
        print("\n📊 详细颜色变化分析:")
        changes_detected = tester.analyze_detailed_color_changes(scheme1_colors, scheme2_colors)
        
        # 测试结果
        print("\n" + "="*60)
        print("📊 测试结果:")
        print(f"   初始颜色数量: {len(initial_colors)}")
        print(f"   配色方案1颜色数量: {len(scheme1_colors)}")
        print(f"   配色方案2颜色数量: {len(scheme2_colors)}")
        print(f"   检测到的颜色变化: {changes_detected}")
        
        if changes_detected > 0:
            print("✅ 配色方案切换功能正常")
            success = True
        else:
            print("❌ 配色方案切换功能异常")
            success = False
        
        # 关闭窗口
        root.destroy()
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始配色方案切换修复测试...")
    print("="*80)
    
    try:
        success = test_color_switch_fix()
        
        print("\n" + "="*80)
        print("📊 测试结果总结:")
        print(f"   配色方案切换修复: {'✅ 通过' if success else '❌ 失败'}")
        
        if success:
            print("\n🎉 配色方案切换修复成功！")
            print("\n💡 修复要点:")
            print("   1. 使用 get_overview_entity_colors() 而不是 get_entity_colors()")
            print("   2. 使用 current_color_scheme.update() 更新配色方案")
            print("   3. 正确的颜色变化检测逻辑")
        else:
            print("\n⚠️ 配色方案切换修复失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
