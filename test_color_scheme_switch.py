#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试配色方案切换功能

测试配色方案切换后实体颜色是否真的改变
"""

import sys
import os
import tkinter as tk
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class ColorSchemeSwitchTester:
    """配色方案切换测试器"""
    
    def __init__(self):
        self.app = None
        self.root = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("🚀 设置配色方案切换测试环境...")
        
        try:
            from main_enhanced_with_v2_fill import EnhancedCADAppV2
            
            # 创建根窗口
            self.root = tk.Tk()
            self.root.title("配色方案切换测试")
            self.root.geometry("800x600")
            
            # 创建应用实例
            print("🔄 正在创建应用实例...")
            self.app = EnhancedCADAppV2(self.root)
            print("✅ 应用实例创建成功")
            
            # 创建测试实体
            self.create_test_entities()
            
            return True
            
        except Exception as e:
            print(f"❌ 设置测试环境失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_test_entities(self):
        """创建测试实体"""
        print("🔧 创建测试实体...")
        
        # 创建测试实体组
        test_entities = [
            {'type': 'LINE', 'layer': 'WALL', 'label': 'wall', 'status': 'auto_labeled'},
            {'type': 'LINE', 'layer': 'DOOR', 'label': 'door_window', 'status': 'auto_labeled'},
            {'type': 'LWPOLYLINE', 'layer': 'FURNITURE', 'label': 'furniture', 'status': 'manual_labeled'},
            {'type': 'LWPOLYLINE', 'layer': 'SOFA', 'label': 'sofa', 'status': 'manual_labeled'}
        ]
        
        # 设置到应用中
        self.app.entities = test_entities
        
        # 设置组信息
        self.app.entity_groups = {
            0: {'entities': [test_entities[0]], 'label': 'wall', 'status': 'auto_labeled'},
            1: {'entities': [test_entities[1]], 'label': 'door_window', 'status': 'auto_labeled'},
            2: {'entities': [test_entities[2]], 'label': 'furniture', 'status': 'manual_labeled'},
            3: {'entities': [test_entities[3]], 'label': 'sofa', 'status': 'manual_labeled'}
        }
        
        print("✅ 测试实体创建完成")
    
    def get_entity_colors(self, stage_name):
        """获取实体颜色"""
        print(f"🎨 获取{stage_name}实体颜色...")
        
        colors = {}
        
        try:
            for group_id, group_info in self.app.entity_groups.items():
                label = group_info.get('label', 'unlabeled')
                sample_entity = group_info.get('entities', [{}])[0]
                sample_entity['label'] = label
                
                # 使用增强的颜色获取方法
                if hasattr(self.app, '_get_entity_color_from_scheme_enhanced'):
                    color = self.app._get_entity_color_from_scheme_enhanced(sample_entity)
                    colors[f"group_{group_id}_{label}"] = color
                else:
                    colors[f"group_{group_id}_{label}"] = "method_not_found"
            
            # 输出颜色信息
            for entity_type, color in colors.items():
                print(f"   {entity_type}: {color}")
            
        except Exception as e:
            print(f"❌ 获取{stage_name}颜色失败: {e}")
            import traceback
            traceback.print_exc()
        
        return colors
    
    def check_color_scheme_attributes(self):
        """检查配色方案属性"""
        print("🔍 检查配色方案属性...")
        
        attrs = {}
        
        if hasattr(self.app, 'current_color_scheme'):
            attrs['current_color_scheme'] = self.app.current_color_scheme
            print(f"   current_color_scheme: {self.app.current_color_scheme}")
        else:
            print("   ❌ current_color_scheme 不存在")
        
        if hasattr(self.app, 'color_scheme'):
            attrs['color_scheme'] = self.app.color_scheme
            print(f"   color_scheme: {self.app.color_scheme}")
        else:
            print("   ❌ color_scheme 不存在")
        
        if hasattr(self.app, 'default_color_scheme'):
            attrs['default_color_scheme'] = self.app.default_color_scheme
            print(f"   default_color_scheme: {self.app.default_color_scheme}")
        else:
            print("   ❌ default_color_scheme 不存在")
        
        return attrs
    
    def test_color_scheme_switch(self):
        """测试配色方案切换"""
        print("\n" + "="*60)
        print("📋 测试配色方案切换")
        print("="*60)
        
        try:
            # 1. 检查初始状态
            print("🔍 1. 检查初始状态...")
            initial_attrs = self.check_color_scheme_attributes()
            initial_colors = self.get_entity_colors("初始状态")
            
            # 2. 设置第一个配色方案
            print("\n🎨 2. 设置第一个配色方案...")
            test_scheme_1 = {
                'wall': '#FF0000',      # 红色
                'door_window': '#0000FF', # 蓝色
                'furniture': '#00FF00',   # 绿色
                'sofa': '#800080',       # 紫色
                'unlabeled': '#808080',  # 灰色
                'background': '#FFFFFF', # 白色背景
                'highlight': '#FFFF00'   # 黄色高亮
            }
            
            # 设置配色方案
            self.app.current_color_scheme = test_scheme_1
            if hasattr(self.app, 'color_scheme'):
                self.app.color_scheme = test_scheme_1
            
            print("✅ 配色方案1设置完成")
            
            # 应用配色方案
            if hasattr(self.app, 'apply_color_scheme_v2'):
                self.app.apply_color_scheme_v2()
                print("✅ 配色方案1应用完成")
            
            # 检查设置后的状态
            scheme1_attrs = self.check_color_scheme_attributes()
            scheme1_colors = self.get_entity_colors("配色方案1")
            
            # 3. 设置第二个配色方案
            print("\n🎨 3. 设置第二个配色方案...")
            test_scheme_2 = {
                'wall': '#8B4513',      # 棕色
                'door_window': '#FF6347', # 番茄红
                'furniture': '#32CD32',   # 酸橙绿
                'sofa': '#9370DB',       # 中紫色
                'unlabeled': '#A9A9A9',  # 深灰色
                'background': '#F0F8FF', # 爱丽丝蓝背景
                'highlight': '#FFD700'   # 金色高亮
            }
            
            # 设置配色方案
            self.app.current_color_scheme = test_scheme_2
            if hasattr(self.app, 'color_scheme'):
                self.app.color_scheme = test_scheme_2
            
            print("✅ 配色方案2设置完成")
            
            # 应用配色方案
            if hasattr(self.app, 'apply_color_scheme_v2'):
                self.app.apply_color_scheme_v2()
                print("✅ 配色方案2应用完成")
            
            # 检查设置后的状态
            scheme2_attrs = self.check_color_scheme_attributes()
            scheme2_colors = self.get_entity_colors("配色方案2")
            
            # 4. 分析颜色变化
            print("\n📊 4. 分析颜色变化...")
            self.analyze_color_changes(initial_colors, scheme1_colors, scheme2_colors)
            
            return True
            
        except Exception as e:
            print(f"❌ 配色方案切换测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_color_changes(self, initial_colors, scheme1_colors, scheme2_colors):
        """分析颜色变化"""
        print("📈 颜色变化分析:")
        
        for entity_type in initial_colors:
            initial = initial_colors.get(entity_type, 'unknown')
            scheme1 = scheme1_colors.get(entity_type, 'unknown')
            scheme2 = scheme2_colors.get(entity_type, 'unknown')
            
            print(f"   {entity_type}:")
            print(f"     初始: {initial}")
            print(f"     方案1: {scheme1}")
            print(f"     方案2: {scheme2}")
            
            if initial != scheme1:
                print(f"     ✅ 初始→方案1: 颜色已改变")
            else:
                print(f"     ❌ 初始→方案1: 颜色未改变")
            
            if scheme1 != scheme2:
                print(f"     ✅ 方案1→方案2: 颜色已改变")
            else:
                print(f"     ❌ 方案1→方案2: 颜色未改变")
            
            print()
    
    def run_test(self):
        """运行测试"""
        print("🚀 开始配色方案切换测试...")
        print("="*80)
        
        # 设置测试环境
        if not self.setup_test_environment():
            print("❌ 测试环境设置失败，终止测试")
            return False
        
        # 执行配色方案切换测试
        success = self.test_color_scheme_switch()
        
        if success:
            print("\n🎉 配色方案切换测试完成！")
        else:
            print("\n❌ 配色方案切换测试失败！")
        
        # 保持窗口打开一段时间
        if self.root:
            print("\n🖼️ 测试界面将在5秒后关闭...")
            self.root.after(5000, self.root.destroy)
            self.root.mainloop()
        
        return success

def main():
    """主函数"""
    tester = ColorSchemeSwitchTester()
    
    try:
        success = tester.run_test()
        if success:
            print("✅ 测试成功完成")
        else:
            print("❌ 测试失败")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
