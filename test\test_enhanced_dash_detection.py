#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的虚线识别功能
"""

import sys
import os

def test_enhanced_dash_detection():
    """测试增强的虚线识别功能"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        print("测试增强的虚线识别功能:")
        
        # 测试各种虚线类型
        test_linetypes = [
            # 标准虚线类型
            'DASHED', 'DASH', 'DASHDOT', 'DASHDOTDOT', 'DOT', 'DOTTED',
            'HIDDEN', 'CENTER', 'PHANTOM', 'DIVIDE',
            
            # AutoCAD ISO虚线类型
            'ACAD_ISO02W100', 'ACAD_ISO03W100', 'ACAD_ISO04W100',
            
            # 包含关键词的类型
            'MY_DASH_LINE', 'CUSTOM_DOT', 'SPECIAL_HIDDEN',
            
            # 中文虚线类型
            '虚线', '点线', '中心线', '隐藏线', '幻影线',
            
            # 非虚线类型
            'CONTINUOUS', 'SOLID', 'BYLAYER', 'BYBLOCK', 'CUSTOM_SOLID'
        ]
        
        print(f"测试线型数量: {len(test_linetypes)}")
        
        dash_count = 0
        solid_count = 0
        
        for linetype in test_linetypes:
            is_dashed = processor._is_dashed_linetype(linetype)
            status = "虚线" if is_dashed else "实线"
            print(f"  {linetype:20s} -> {status}")
            
            if is_dashed:
                dash_count += 1
            else:
                solid_count += 1
        
        print(f"\n识别结果统计:")
        print(f"  虚线类型: {dash_count}")
        print(f"  实线类型: {solid_count}")
        
        # 验证关键的虚线类型是否被正确识别
        key_dash_types = ['DASHED', 'DOT', 'HIDDEN', 'ACAD_ISO02W100', '虚线']
        all_recognized = True
        
        for linetype in key_dash_types:
            if not processor._is_dashed_linetype(linetype):
                print(f"✗ 关键虚线类型 {linetype} 未被识别")
                all_recognized = False
        
        if all_recognized:
            print("✓ 所有关键虚线类型都被正确识别")
            return True
        else:
            print("✗ 部分关键虚线类型未被识别")
            return False
        
    except Exception as e:
        print(f"✗ 增强虚线识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dash_entity_extraction():
    """测试虚线实体提取"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        print("测试虚线实体提取:")
        
        # 创建模拟的DXF实体
        class MockDXFEntity:
            def __init__(self, linetype, layer):
                self.dxf = MockDXF(linetype, layer)
            
            def dxftype(self):
                return 'LINE'
        
        class MockDXF:
            def __init__(self, linetype, layer):
                self.linetype = linetype
                self.layer = layer
                self.color = 256
        
        # 测试不同线型的实体
        test_entities = [
            MockDXFEntity('DASHED', 'A-WINDOW'),
            MockDXFEntity('HIDDEN', 'A-WINDOW'),
            MockDXFEntity('虚线', 'A-WINDOW'),
            MockDXFEntity('CONTINUOUS', 'A-WINDOW'),
            MockDXFEntity('ACAD_ISO02W100', 'A-WINDOW')
        ]
        
        print(f"测试实体数量: {len(test_entities)}")
        
        dash_entities = 0
        solid_entities = 0
        
        for i, entity in enumerate(test_entities):
            entity_data = processor._extract_entity_data(entity, None)
            is_dashed = entity_data.get('is_dashed', False)
            linetype = entity_data.get('linetype', 'UNKNOWN')
            
            status = "虚线" if is_dashed else "实线"
            print(f"  实体 {i+1}: 线型={linetype:15s} -> {status}")
            
            if is_dashed:
                dash_entities += 1
            else:
                solid_entities += 1
        
        print(f"\n提取结果统计:")
        print(f"  虚线实体: {dash_entities}")
        print(f"  实线实体: {solid_entities}")
        
        # 验证虚线实体是否被正确识别
        if dash_entities >= 4:  # 应该有4个虚线实体
            print("✓ 虚线实体提取正常")
            return True
        else:
            print("✗ 虚线实体提取异常")
            return False
        
    except Exception as e:
        print(f"✗ 虚线实体提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dash_window_integration():
    """测试虚线门窗集成"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        print("测试虚线门窗集成:")
        
        # 创建包含各种虚线类型的A-WINDOW实体
        test_entities = [
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 0), (100, 0)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'HIDDEN',
                'is_dashed': True,
                'points': [(100, 0), (100, 50)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': '虚线',
                'is_dashed': True,
                'points': [(100, 50), (0, 50)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'ACAD_ISO02W100',
                'is_dashed': True,
                'points': [(0, 50), (0, 0)]
            }
        ]
        
        print(f"测试实体数量: {len(test_entities)}")
        for i, entity in enumerate(test_entities):
            print(f"  实体 {i+1}: 线型={entity['linetype']}, 虚线={entity['is_dashed']}")
        
        # 设置处理器状态
        processor.current_file_entities = test_entities
        
        # 运行门窗识别
        door_window_layers = processor.processor._detect_special_layers(
            test_entities, 
            processor.processor.door_window_layer_patterns, 
            debug=False, 
            layer_type="门窗"
        )
        
        door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
        
        if door_window_entities:
            door_window_groups = processor.processor._group_special_entities_by_layer(
                door_window_entities, connection_threshold=50, entity_type="door_window"
            )
            
            # 自动标注
            for group in door_window_groups:
                for entity in group:
                    entity['label'] = 'door_window'
                    entity['auto_labeled'] = True
        
        # 检查结果
        labeled_entities = [e for e in test_entities if e.get('label') == 'door_window']
        dash_labeled = [e for e in labeled_entities if e.get('is_dashed', False)]
        
        print(f"\n集成结果:")
        print(f"  标注为门窗的实体: {len(labeled_entities)}")
        print(f"  其中虚线实体: {len(dash_labeled)}")
        
        if len(dash_labeled) == 4:
            print("✓ 所有虚线实体都被正确识别为门窗")
            return True
        else:
            print("✗ 部分虚线实体未被识别为门窗")
            return False
        
    except Exception as e:
        print(f"✗ 虚线门窗集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试增强的虚线识别功能...")
    print("=" * 60)
    
    tests = [
        ("增强虚线识别", test_enhanced_dash_detection),
        ("虚线实体提取", test_dash_entity_extraction),
        ("虚线门窗集成", test_dash_window_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 增强虚线识别功能测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
