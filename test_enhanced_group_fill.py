#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的组填充功能
验证借鉴墙体自动填充轮廓识别逻辑的组填充功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_group_fill():
    """测试增强的组填充功能"""
    print("=" * 80)
    print("🧪 测试增强的组填充功能")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        print("✅ 成功导入必要模块")
        
        # 检查关键方法是否存在
        key_methods = [
            '_perform_group_fill_v2',
            '_update_visualization_with_group_fills',
            '_update_visualization_after_fill_change'
        ]
        
        print("\n📋 检查关键方法:")
        for method in key_methods:
            if hasattr(EnhancedCADAppV2, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - 缺失")
                return False
        
        # 检查墙体填充处理器的关键方法
        processor_methods = [
            '_identify_outer_contour_improved',
            '_merge_overlapping_segments',
            '_close_gaps_in_segments',
            '_complete_missing_wall_ends',
            '_ensure_closed_path',
            '_identify_cavities_from_all_entities'
        ]
        
        print("\n📋 检查墙体填充处理器方法:")
        for method in processor_methods:
            if hasattr(EnhancedWallFillProcessorV2, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - 缺失")
                return False
        
        # 测试轮廓识别逻辑
        success = test_contour_identification()
        if not success:
            return False
        
        # 测试组填充流程
        success = test_group_fill_workflow()
        if not success:
            return False
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contour_identification():
    """测试轮廓识别逻辑"""
    print("\n" + "=" * 60)
    print("🔍 测试轮廓识别逻辑")
    print("=" * 60)
    
    try:
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        processor = EnhancedWallFillProcessorV2()
        
        # 创建测试组数据（模拟一个简单的矩形）
        test_group = [
            {
                'type': 'LINE',
                'points': [(0, 0), (100, 0)],  # 底边
                'layer': 'WALL'
            },
            {
                'type': 'LINE', 
                'points': [(100, 0), (100, 100)],  # 右边
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(100, 100), (0, 100)],  # 顶边
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(0, 100), (0, 0)],  # 左边
                'layer': 'WALL'
            }
        ]
        
        print(f"📋 测试数据: {len(test_group)} 个线段组成的矩形")
        
        # 测试改进的外轮廓识别
        print("🔧 执行改进的外轮廓识别...")
        outer_contour = processor._identify_outer_contour_improved(test_group)
        
        if outer_contour:
            print(f"✅ 成功识别外轮廓，面积: {outer_contour.area:.2f}")
            print(f"   轮廓类型: {outer_contour.geom_type}")
            
            # 验证面积是否合理（应该接近10000）
            expected_area = 100 * 100
            if abs(outer_contour.area - expected_area) < 100:
                print(f"✅ 轮廓面积验证通过 (期望: {expected_area}, 实际: {outer_contour.area:.2f})")
            else:
                print(f"❌ 轮廓面积验证失败 (期望: {expected_area}, 实际: {outer_contour.area:.2f})")
                return False
        else:
            print("❌ 无法识别外轮廓")
            return False
        
        # 测试带间隙的情况
        print("\n🔧 测试间隙处理...")
        test_group_with_gap = test_group.copy()
        # 修改一个线段，创建间隙
        test_group_with_gap[0] = {
            'type': 'LINE',
            'points': [(0, 0), (90, 0)],  # 底边缩短10单位，创建间隙
            'layer': 'WALL'
        }
        
        outer_contour_gap = processor._identify_outer_contour_improved(test_group_with_gap)
        
        if outer_contour_gap:
            print(f"✅ 成功处理间隙并识别外轮廓，面积: {outer_contour_gap.area:.2f}")
        else:
            print("❌ 无法处理间隙")
            return False
        
        print("✅ 轮廓识别逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 轮廓识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_fill_workflow():
    """测试组填充工作流程"""
    print("\n" + "=" * 60)
    print("🔄 测试组填充工作流程")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 检查V2墙体填充处理器是否可用
        if not app.wall_fill_processor_v2:
            print("❌ V2墙体填充处理器不可用")
            root.destroy()
            return False
        
        print("✅ V2墙体填充处理器可用")
        
        # 创建模拟组数据
        test_group = [
            {
                'type': 'LINE',
                'points': [(0, 0), (100, 0)],
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(100, 0), (100, 100)],
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(100, 100), (0, 100)],
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(0, 100), (0, 0)],
                'layer': 'WALL'
            }
        ]
        
        print(f"📋 测试组数据: {len(test_group)} 个实体")
        
        # 测试组填充方法
        print("🔧 执行组填充...")
        success = app._perform_group_fill_v2(test_group, 0)
        
        if success:
            print("✅ 组填充执行成功")
            
            # 检查填充结果是否保存
            if hasattr(app, 'group_fill_results') and 0 in app.group_fill_results:
                fill_result = app.group_fill_results[0]
                print(f"✅ 填充结果已保存")
                print(f"   填充多边形数量: {len(fill_result.get('fill_polygons', []))}")
                print(f"   空腔数量: {len(fill_result.get('cavities', []))}")
                
                # 验证填充多边形
                if fill_result.get('fill_polygons'):
                    polygon = fill_result['fill_polygons'][0]
                    print(f"   主填充多边形面积: {polygon.area:.2f}")
                else:
                    print("❌ 未生成填充多边形")
                    root.destroy()
                    return False
            else:
                print("❌ 填充结果未保存")
                root.destroy()
                return False
        else:
            print("❌ 组填充执行失败")
            root.destroy()
            return False
        
        root.destroy()
        print("✅ 组填充工作流程测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 组填充工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_features():
    """测试高级功能"""
    print("\n" + "=" * 60)
    print("🚀 测试高级功能")
    print("=" * 60)
    
    try:
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        processor = EnhancedWallFillProcessorV2()
        
        # 测试重叠线段合并
        print("🔧 测试重叠线段合并...")
        overlapping_segments = [
            ((0, 0), (50, 0)),
            ((25, 0), (75, 0)),  # 重叠
            ((50, 0), (100, 0))
        ]
        
        merged_segments = processor._merge_overlapping_segments(overlapping_segments)
        print(f"   原始线段: {len(overlapping_segments)}, 合并后: {len(merged_segments)}")
        
        # 测试间隙闭合
        print("🔧 测试间隙闭合...")
        gap_segments = [
            ((0, 0), (90, 0)),    # 底边有间隙
            ((100, 0), (100, 100)),  # 右边
            ((100, 100), (0, 100)),  # 顶边
            ((0, 100), (0, 0))    # 左边
        ]
        
        closed_segments = processor._close_gaps_in_segments(gap_segments, gap_threshold=20)
        print(f"   原始线段: {len(gap_segments)}, 闭合后: {len(closed_segments)}")
        
        print("✅ 高级功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 高级功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始增强组填充功能测试...")
    
    # 执行所有测试
    tests = [
        ("基本功能测试", test_enhanced_group_fill),
        ("高级功能测试", test_advanced_features)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！增强的组填充功能已成功实现。")
        print("\n📝 功能特性:")
        print("1. ✅ 使用墙体自动填充的高级轮廓识别逻辑")
        print("2. ✅ 合并重叠线段")
        print("3. ✅ 智能闭合间隙（阈值20单位）")
        print("4. ✅ 补全缺失墙端")
        print("5. ✅ 确保形成封闭路径")
        print("6. ✅ 识别和处理空腔")
        print("7. ✅ 可视化显示填充结果")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    print("=" * 80)
