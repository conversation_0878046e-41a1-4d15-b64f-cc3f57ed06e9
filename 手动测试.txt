PS C:\A-BCXM\CAD分类标注工具C01> & C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/A-BCXM/CAD分类标注工具C01/main_enhanced_with_v2_fill.py
✅ 实体组预览区域创建完成（已恢复原有尺寸和显示）
✅ 实体全图概览区域创建完成（已恢复原有尺寸和显示）
✅ 原有配色系统功能已恢复
✅ 配色系统区域创建完成（已恢复原有功能）
✅ 原有缩放按钮功能已恢复
✅ 缩放按钮区域创建完成（已恢复原有功能）      
✅ 四区域可视化布局创建完成，支持按比例自动适应
✅ 可视化器已创建，使用原有的单一画布模式      
✅ 可视化器兼容性设置完成（原有模式）
✅ 日志导出器初始化成功
🔍 开始扫描文件夹: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file
📋 文件夹中有 4 个项目
  检查: cad_annotation_data.json
    文件扩展名: '.json'
    ❌ 跳过非CAD文件: cad_annotation_data.json
  检查: kitch01.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: kitch01.dxf
      完整路径: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\kitch01.dxf
  检查: kongqiang.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: kongqiang.dxf
      完整路径: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\kongqiang.dxf
  检查: wall01.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: wall01.dxf
      完整路径: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\wall01.dxf
🎯 扫描结果: 找到 3 个CAD文件
✅ 成功找到以下CAD文件:
    kitch01.dxf
    kongqiang.dxf
    wall01.dxf
📁 使用第一个文件: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (未处理, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
🚀 启动分离式多文件处理流程
============================================================
📋 阶段1：批量处理所有文件（仅显示进度，不更新界面）

📄 处理文件 1/3: kitch01.dxf
  文件路径: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\kitch01.dxf
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
  📊 进度更新: 33.3% - kitch01.dxf
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: 3.640 - 4.266
  校正参数: 2.017 - 2.644
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: -1.088 - -0.535
  校正参数: 0.535 - 1.088

============================================================
🔍 专业DXF读取器 - 坐标系手性校正摘要
============================================================
总实体数: 533
圆弧实体: 372 (校正: 0)
椭圆实体: 2 (校正: 2)
右手坐标系 (RHS): 372
左手坐标系 (LHS): 2

📐 校正详情:
  ELLIPSE (左手坐标系):
    原始参数: (3.639540612752146, 4.266436669646277)
    校正参数: (2.0167486375333095, 2.64364469442744)
  ELLIPSE (左手坐标系):
    原始参数: (-1.0883951813113, -0.5350782450734349)
    校正参数: (0.5350782450734348, 1.0883951813113004)
============================================================
特殊图层识别结果:
  墙体图层: set()
  门窗图层: set()
  栏杆图层: set()
跳过后台处理的自动标注可视化更新
去重: 533 → 517 个实体
同图层分组阈值: 40
内部优化后组数: 130
包围合并后组数: 4
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/4, 实体数量: 441
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 4
  💾 缓存数据已保存
  ✅ 处理成功

📄 处理文件 2/3: kongqiang.dxf
  文件路径: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\kongqiang.dxf
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
  📊 进度更新: 66.7% - kongqiang.dxf
🔍 墙体图层: 1 个图层，39 个实体
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: set()
  栏杆图层: set()
开始合并包含的墙体组，共 5 个墙体组
发现包含关系: 组0 包含 组1
发现包含关系: 组2 包含 组3
墙体组合并完成: 5 -> 3
跳过后台处理的自动标注可视化更新
去重: 62 → 56 个实体
同图层分组阈值: 40
内部优化后组数: 1
包围合并后组数: 1
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/1, 实体数量: 56
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 1
  💾 缓存数据已保存
  ✅ 处理成功

📄 处理文件 3/3: wall01.dxf
  📊 进度更新: 100.0% - wall01.dxf
  文件路径: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\wall01.dxf
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
🔍 墙体图层: 1 个图层，38 个实体
🔍 门窗图层: 1 个图层，6 个实体
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: {'A-WINDOW'}
  栏杆图层: set()
开始合并包含的墙体组，共 3 个墙体组
发现包含关系: 组1 包含 组2
墙体组合并完成: 3 -> 2
跳过后台处理的自动标注可视化更新
同图层分组阈值: 40
内部优化后组数: 1
包围合并后组数: 1
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/1, 实体数量: 12
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 1
  💾 缓存数据已保存
  ✅ 处理成功

============================================================
📋 阶段1完成：批量处理结果
  ✅ 成功处理: 3 个文件
  ❌ 处理失败: 0 个文件

📋 阶段2：界面更新和显示
  💾 所有文件识别内容已写入缓存
📁 使用第一个文件: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (已完成, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
  📋 文件选择下拉菜单已更新
🖥️ 切换到第一个文件并更新界面: kitch01.dxf
🔧 验证和修复缓存数据: kitch01.dxf
  修复了 533 个实体
✅ 数据验证和修复完成: kitch01.dxf，共修复 533 个实体
  🔄 开始完整界面更新...
⚠️ 没有处理器，跳过组列表更新
    ✅ 组列表和统计信息更新完成
    ✅ 界面控件更新完成
📁 使用display_file: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (已完成, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
    ✅ 画布刷新完成
✅ 文件 kitch01.dxf 数据加载和界面更新完成
  ✅ 文件数据加载完成
⚠️ 没有处理器，跳过组列表更新
  ✅ 组列表更新完成
  ⚠️ 跳过详细视图更新: 没有有效的组数据
  ⚠️ 跳过全图概览更新: 没有有效的可视化器或实体数据
  ✅ 可视化更新完成
  ✅ 统计信息更新完成
📁 使用display_file: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (已完成, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
  ✅ 文件下拉菜单更新完成
✅ 界面更新完成: kitch01.dxf
✅ 分离式多文件处理流程完成
📁 使用display_file: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (已完成, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
✅ 已保存当前文件数据: kitch01.dxf
🔧 验证和修复缓存数据: kongqiang.dxf
  修复了 101 个实体
✅ 数据验证和修复完成: kongqiang.dxf，共修复 101 个实体
  🔄 开始完整界面更新...
⚠️ 没有处理器，跳过组列表更新
    ✅ 组列表和统计信息更新完成
    ✅ 界面控件更新完成
📁 使用display_file: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (已完成, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
    ✅ 画布刷新完成
✅ 文件 kongqiang.dxf 数据加载和界面更新完成
🔄 执行文件切换的完整界面更新: kongqiang.dxf
⚠️ 没有处理器，跳过组列表更新
  ✅ 组列表更新完成
  ⚠️ 跳过详细视图更新: 没有有效的组数据
  ⚠️ 跳过全图概览更新: 没有有效的可视化器或实体数据
  ✅ 可视化更新完成
  ✅ 统计信息更新完成
📁 使用display_file: kongqiang.dxf
📁 设置当前选择: [kongqiang.dxf] (已完成, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kongqiang.dxf
  ✅ 文件下拉菜单更新完成
✅ 文件切换的完整界面更新完成: kongqiang.dxf
✅ 成功切换到文件: kongqiang.dxf