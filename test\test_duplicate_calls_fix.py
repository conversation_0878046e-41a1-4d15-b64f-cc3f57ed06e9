#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重复调用修复效果
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_duplicate_calls_fix():
    """测试重复调用修复效果"""
    print("=== 测试重复调用修复效果 ===")
    
    print("\n1. 问题分析:")
    print("  原问题1: 一个文件处理完成后，有4次处理状态更新、两次视图更新")
    print("    - 🔄 处理状态更新: manual_group (第1次)")
    print("    - 更新详细视图... 更新全图概览... 可视化更新成功")
    print("    - 🔄 处理状态更新: manual_group (第2次，重复)")
    print("    - 更新详细视图... 更新全图概览... 可视化更新成功")
    print("    - 🔄 处理状态更新: completed (第1次)")
    print("    - 🔄 处理状态更新: completed (第2次，重复)")
    
    print("  原问题2: 处理后续文件时仍在更新组列表、视图")
    print("    - 显示手动分组组: 1/1, 实体数量: 12")
    print("    - 📋 组列表更新完成：wall01.dxf (应该跳过)")
    print("    - 更新详细视图... 更新全图概览... (应该跳过)")
    
    print("\n2. 修复方案:")
    
    print("  A. 避免重复调用 _show_next_manual_group:")
    print("     - 添加 _showing_manual_group 标记")
    print("     - 在方法开始时检查标记，如果正在执行则跳过")
    print("     - 在 finally 块中清除标记")
    
    print("  B. 避免重复调用 _show_completion_message:")
    print("     - 添加 _completion_shown 标记")
    print("     - 完成信息只显示一次")
    print("     - 每次开始新文件处理时重置标记")
    
    print("  C. 强化后台处理检查:")
    print("     - 后台处理器完全禁用 status_callback")
    print("     - 在所有状态回调前检查 _is_background_processing 标记")
    print("     - 在所有可视化更新前检查后台处理标记")
    
    print("\n3. 修复效果验证:")
    
    # 模拟修复后的处理流程
    class MockProcessor:
        def __init__(self, is_background=False):
            self._is_background_processing = is_background
            self._showing_manual_group = False
            self._completion_shown = False
            self.status_callback = self._mock_status_callback if not is_background else None
            self.visualizer = None if is_background else MockVisualizer()
            self.canvas = None if is_background else MockCanvas()
            self.pending_manual_groups = [["entity1", "entity2"]]
            self.current_manual_group_index = 0
            self.call_count = {"manual_group": 0, "completed": 0, "visualization": 0}
        
        def _mock_status_callback(self, status_type, data):
            self.call_count[status_type] = self.call_count.get(status_type, 0) + 1
            print(f"    状态回调: {status_type} (第{self.call_count[status_type]}次)")
        
        def _show_next_manual_group(self):
            """模拟修复后的方法"""
            # 添加调用标记，避免重复调用
            if hasattr(self, '_showing_manual_group') and self._showing_manual_group:
                print("    ⚠️ _show_next_manual_group 正在执行中，跳过重复调用")
                return
            
            self._showing_manual_group = True
            
            try:
                # 检查是否为后台处理，如果是则跳过状态回调
                if self.status_callback and not getattr(self, '_is_background_processing', False):
                    self.status_callback("manual_group", {
                        'index': 1,
                        'total': 1,
                        'entity_count': 2
                    })
                
                # 更新可视化（检查是否为后台处理）
                if self.visualizer and self.canvas and not getattr(self, '_is_background_processing', False):
                    self.call_count["visualization"] = self.call_count.get("visualization", 0) + 1
                    print(f"    可视化更新 (第{self.call_count['visualization']}次)")
                elif getattr(self, '_is_background_processing', False):
                    print("    跳过后台处理的可视化更新")
            
            finally:
                # 确保标记被清除
                self._showing_manual_group = False
        
        def _show_completion_message(self):
            """模拟修复后的方法"""
            # 添加完成标记，避免重复调用
            if hasattr(self, '_completion_shown') and self._completion_shown:
                print("    ⚠️ 完成信息已显示，跳过重复调用")
                return
            
            self._completion_shown = True
            
            # 检查是否为后台处理，如果是则跳过状态回调
            if self.status_callback and not getattr(self, '_is_background_processing', False):
                self.status_callback("completed", "所有文件处理完成")
        
        def reset_for_new_file(self):
            """开始新文件处理时重置标记"""
            if hasattr(self, '_showing_manual_group'):
                delattr(self, '_showing_manual_group')
            if hasattr(self, '_completion_shown'):
                delattr(self, '_completion_shown')
    
    class MockVisualizer:
        pass
    
    class MockCanvas:
        pass
    
    print("\n  测试场景1: 前台文件处理（修复前会重复调用）")
    processor = MockProcessor(is_background=False)
    
    print("    第1次调用 _show_next_manual_group:")
    processor._show_next_manual_group()
    
    print("    第2次调用 _show_next_manual_group (应该被跳过):")
    processor._show_next_manual_group()
    
    print("    第1次调用 _show_completion_message:")
    processor._show_completion_message()
    
    print("    第2次调用 _show_completion_message (应该被跳过):")
    processor._show_completion_message()
    
    print(f"    结果统计: manual_group={processor.call_count.get('manual_group', 0)}次, completed={processor.call_count.get('completed', 0)}次, visualization={processor.call_count.get('visualization', 0)}次")
    
    print("\n  测试场景2: 后台文件处理（应该跳过所有界面更新）")
    bg_processor = MockProcessor(is_background=True)
    
    print("    后台调用 _show_next_manual_group:")
    bg_processor._show_next_manual_group()
    
    print("    后台调用 _show_completion_message:")
    bg_processor._show_completion_message()
    
    print(f"    结果统计: manual_group={bg_processor.call_count.get('manual_group', 0)}次, completed={bg_processor.call_count.get('completed', 0)}次, visualization={bg_processor.call_count.get('visualization', 0)}次")
    
    print("\n  测试场景3: 新文件处理时标记重置")
    processor.reset_for_new_file()
    print("    重置标记后，再次调用应该正常执行:")
    processor._show_next_manual_group()
    processor._show_completion_message()
    
    print(f"    结果统计: manual_group={processor.call_count.get('manual_group', 0)}次, completed={processor.call_count.get('completed', 0)}次")
    
    print("\n=== 测试结果 ===")
    print("✅ 前台处理: 重复调用被成功阻止")
    print("✅ 后台处理: 界面更新被完全跳过")
    print("✅ 标记重置: 新文件处理时正常工作")
    
    print("\n🎯 修复要点:")
    print("1. 重复调用检查: 使用标记机制避免同一方法的重复执行")
    print("2. 后台处理检查: 在所有界面更新前检查 _is_background_processing")
    print("3. 状态回调控制: 后台处理器完全禁用状态回调")
    print("4. 标记管理: 每次开始新文件处理时重置所有标记")
    
    print("\n📋 被修复的重复调用:")
    print("- _show_next_manual_group() 重复调用")
    print("- _show_completion_message() 重复调用")
    print("- status_callback('manual_group', ...) 重复调用")
    print("- status_callback('completed', ...) 重复调用")
    print("- 可视化更新的重复调用")
    print("- 后台处理时的界面更新调用")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_duplicate_calls_fix()
