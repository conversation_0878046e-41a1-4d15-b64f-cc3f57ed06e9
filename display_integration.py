#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
显示控制器集成模块 - 简化与现有系统的集成
提供装饰器、混入类和工具函数来集成显示控制器
"""

import functools
from typing import Any, Dict, Optional, Callable
from display_controller import DisplayController, DataChangeType, DataChangeEvent


class DisplayControllerMixin:
    """显示控制器混入类 - 为现有类添加显示控制功能"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._display_controller: Optional[DisplayController] = None
        self._display_integration_enabled = True
    
    def set_display_controller(self, display_controller: DisplayController):
        """设置显示控制器"""
        self._display_controller = display_controller

    
    def notify_display_change(self, change_type: DataChangeType, data: Any, 
                            source: str = "", metadata: Dict[str, Any] = None):
        """通知显示变化"""
        if self._display_controller and self._display_integration_enabled:
            self._display_controller.notify_data_change(
                change_type, data, source, metadata or {}
            )
    
    def enable_display_integration(self, enabled: bool = True):
        """启用/禁用显示集成"""
        self._display_integration_enabled = enabled


def display_change_monitor(change_type: DataChangeType, 
                         extract_data: Callable = None,
                         source_name: str = ""):
    """
    显示变化监控装饰器
    
    Args:
        change_type: 数据变化类型
        extract_data: 数据提取函数，从方法参数中提取需要的数据
        source_name: 数据源名称
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # 执行原方法
            result = func(self, *args, **kwargs)
            
            # 如果对象有显示控制器，通知变化
            if hasattr(self, '_display_controller') and self._display_controller:
                try:
                    # 提取数据
                    if extract_data:
                        data = extract_data(self, *args, **kwargs)
                    else:
                        data = args[0] if args else None
                    
                    # 构建元数据
                    metadata = {
                        'method_name': func.__name__,
                        'args_count': len(args),
                        'kwargs_keys': list(kwargs.keys()),
                        'result': result
                    }
                    
                    # 通知显示控制器
                    self._display_controller.notify_data_change(
                        change_type, 
                        data, 
                        source_name or f"{self.__class__.__name__}.{func.__name__}",
                        metadata
                    )
                    
                except Exception as e:
                    print(f"⚠️ 显示变化监控失败: {e}")
            
            return result
        return wrapper
    return decorator


class DisplayIntegrationHelper:
    """显示集成辅助类"""
    
    @staticmethod
    def create_integrated_controller(app_instance, visualizer=None, canvas=None, color_system=None):
        """创建集成的显示控制器"""
        # 创建显示控制器
        controller = DisplayController(visualizer, canvas, color_system)
        
        # 自动检测和设置组件
        if not visualizer and hasattr(app_instance, 'visualizer'):
            controller.set_visualizer(app_instance.visualizer)
        
        if not canvas and hasattr(app_instance, 'canvas'):
            controller.set_canvas(app_instance.canvas)
        
        if not color_system and hasattr(app_instance, 'color_system'):
            controller.set_color_system(app_instance.color_system)
        elif not color_system and hasattr(app_instance, 'current_color_scheme'):
            # 创建简单的配色系统包装
            controller.set_color_system(SimpleColorSystemWrapper(app_instance))
        
        # 设置数据源
        if hasattr(app_instance, 'processor'):
            controller.set_data_source(app_instance.processor)
        
        return controller
    
    @staticmethod
    def integrate_with_processor(processor_instance, display_controller):
        """与处理器集成"""
        # 为处理器添加显示控制器
        if hasattr(processor_instance, 'set_display_controller'):
            processor_instance.set_display_controller(display_controller)
        else:
            processor_instance._display_controller = display_controller
        
        # 注册关键方法的监控
        DisplayIntegrationHelper._add_method_monitors(processor_instance)
    
    @staticmethod
    def _add_method_monitors(instance):
        """为实例添加方法监控"""
        # 监控组数据变化
        if hasattr(instance, 'update_group_data'):
            original_method = instance.update_group_data
            
            @functools.wraps(original_method)
            def monitored_update_group_data(*args, **kwargs):
                result = original_method(*args, **kwargs)
                if hasattr(instance, '_display_controller') and instance._display_controller:
                    instance._display_controller.notify_data_change(
                        DataChangeType.GROUP_DATA,
                        args[0] if args else None,
                        f"{instance.__class__.__name__}.update_group_data"
                    )
                return result
            
            instance.update_group_data = monitored_update_group_data
        
        # 监控组类型变化
        if hasattr(instance, 'set_group_type'):
            original_method = instance.set_group_type
            
            @functools.wraps(original_method)
            def monitored_set_group_type(*args, **kwargs):
                result = original_method(*args, **kwargs)
                if hasattr(instance, '_display_controller') and instance._display_controller:
                    instance._display_controller.notify_data_change(
                        DataChangeType.GROUP_TYPE,
                        args[:2] if len(args) >= 2 else args,
                        f"{instance.__class__.__name__}.set_group_type"
                    )
                return result
            
            instance.set_group_type = monitored_set_group_type


class SimpleColorSystemWrapper:
    """简单的配色系统包装器"""
    
    def __init__(self, app_instance):
        self.app_instance = app_instance
    
    @property
    def current_color_scheme(self):
        """获取当前配色方案"""
        if hasattr(self.app_instance, 'current_color_scheme'):
            return self.app_instance.current_color_scheme
        return {}
    
    def get_category_color(self, category):
        """获取类别颜色"""
        scheme = self.current_color_scheme
        return scheme.get(category, scheme.get('other', '#808080'))


def integrate_display_controller(app_class):
    """类装饰器：为应用类集成显示控制器"""
    
    class IntegratedApp(app_class, DisplayControllerMixin):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            
            # 自动创建和集成显示控制器
            self._auto_integrate_display_controller()
        
        def _auto_integrate_display_controller(self):
            """自动集成显示控制器"""
            try:
                # 创建显示控制器
                controller = DisplayIntegrationHelper.create_integrated_controller(self)
                self.set_display_controller(controller)
                
                # 与处理器集成
                if hasattr(self, 'processor') and self.processor:
                    DisplayIntegrationHelper.integrate_with_processor(
                        self.processor, controller
                    )
                
                print("✅ 显示控制器自动集成完成")
                
            except Exception as e:
                print(f"❌ 显示控制器自动集成失败: {e}")
    
    return IntegratedApp


# 便捷的装饰器函数
def monitor_dxf_data_change(func):
    """监控DXF数据变化"""
    return display_change_monitor(DataChangeType.DXF_FILE_DATA)(func)

def monitor_group_data_change(func):
    """监控组数据变化"""
    return display_change_monitor(DataChangeType.GROUP_DATA)(func)

def monitor_group_type_change(func):
    """监控组类型变化"""
    return display_change_monitor(DataChangeType.GROUP_TYPE)(func)

def monitor_group_fill_change(func):
    """监控组填充变化"""
    return display_change_monitor(DataChangeType.GROUP_FILL)(func)

def monitor_wall_fill_change(func):
    """监控墙体填充变化"""
    return display_change_monitor(DataChangeType.WALL_FILL)(func)

def monitor_room_fill_change(func):
    """监控房间填充变化"""
    return display_change_monitor(DataChangeType.ROOM_FILL)(func)

def monitor_color_scheme_change(func):
    """监控配色方案变化"""
    return display_change_monitor(DataChangeType.COLOR_SCHEME)(func)


# 批量监控工具
class BatchDisplayMonitor:
    """批量显示监控工具"""
    
    def __init__(self, display_controller: DisplayController):
        self.display_controller = display_controller
        self._monitoring_enabled = True
    
    def __enter__(self):
        """进入批量监控模式"""
        self.display_controller.batch_update_enabled = True
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出批量监控模式"""
        # 强制执行一次批量更新
        if self.display_controller._batch_timer:
            self.display_controller._batch_timer.cancel()
        self.display_controller._perform_batch_update()
    
    def notify_change(self, change_type: DataChangeType, data: Any, 
                     source: str = "", metadata: Dict[str, Any] = None):
        """通知变化"""
        if self._monitoring_enabled:
            self.display_controller.notify_data_change(
                change_type, data, source, metadata or {}
            )
    
    def disable_monitoring(self):
        """禁用监控"""
        self._monitoring_enabled = False
    
    def enable_monitoring(self):
        """启用监控"""
        self._monitoring_enabled = True


# 使用示例和测试函数
def test_display_integration():
    """测试显示集成功能"""
    print("🧪 测试显示集成功能...")
    
    # 创建模拟的应用实例
    class MockApp:
        def __init__(self):
            self.visualizer = None
            self.canvas = None
            self.current_color_scheme = {'wall': '#000000', 'door': '#FF0000'}
    
    app = MockApp()
    
    # 创建集成的显示控制器
    controller = DisplayIntegrationHelper.create_integrated_controller(app)
    
    # 测试数据变化通知
    controller.notify_data_change(
        DataChangeType.GROUP_DATA,
        [{'type': 'LINE', 'layer': 'WALL'}],
        "test_source"
    )
    
    # 获取统计信息
    stats = controller.get_display_stats()
    print(f"📊 显示统计: {stats}")
    
    print("✅ 显示集成测试完成")


if __name__ == "__main__":
    test_display_integration()
