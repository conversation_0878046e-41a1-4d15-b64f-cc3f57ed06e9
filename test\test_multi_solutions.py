#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多解决方案重构器
验证针对镜像操作导致的坐标系手性变化问题的各种解决方案
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Arc, Ellipse as MplEllipse
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_multi_solutions():
    """测试多解决方案重构器"""
    print("🔄 测试多解决方案重构器...")
    print("🎯 针对镜像操作导致的坐标系手性变化问题")
    print("=" * 60)
    
    try:
        from multi_solution_reconstructor import MultiSolutionReconstructor
        
        # 创建多解决方案重构器
        reconstructor = MultiSolutionReconstructor(sample_points_count=30)
        
        # 创建测试实体 - 重点测试镜像问题
        test_entities = [
            # 普通圆弧（无镜像）
            {
                'type': 'ARC',
                'center': (50, 50),
                'radius': 25,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'arcs',
                'color': 'red'
            },
            # X轴镜像圆弧（问题案例）
            {
                'type': 'ARC',
                'center': (150, 50),
                'radius': 25,
                'start_angle': 0,
                'end_angle': 90,
                'scale_x': -1.0,
                'scale_y': 1.0,
                'layer': 'arcs',
                'color': 'red'
            },
            # Y轴镜像圆弧（问题案例）
            {
                'type': 'ARC',
                'center': (250, 50),
                'radius': 25,
                'start_angle': 0,
                'end_angle': 90,
                'scale_x': 1.0,
                'scale_y': -1.0,
                'layer': 'arcs',
                'color': 'red'
            },
            # 普通椭圆弧（无镜像）
            {
                'type': 'ELLIPSE',
                'center': (100, 150),
                'major_axis': (30, 15),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'layer': 'ellipses',
                'color': 'purple'
            },
            # Y轴镜像椭圆弧（问题案例）
            {
                'type': 'ELLIPSE',
                'center': (200, 150),
                'major_axis': (30, 15),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'scale_x': 1.0,
                'scale_y': -1.0,
                'layer': 'ellipses',
                'color': 'purple'
            }
        ]
        
        print("🔄 使用所有解决方案处理测试实体...")
        
        # 使用所有解决方案重构
        solutions = reconstructor.reconstruct_with_all_solutions(test_entities)
        
        print(f"✅ 处理完成，共生成 {len(solutions)} 种解决方案")
        
        # 显示各方案统计
        for solution_name, entities in solutions.items():
            print(f"\n📊 {solution_name}:")
            print(f"  实体数量: {len(entities)}")
            
            # 统计重构方法
            methods = {}
            for entity in entities:
                method = entity.get('reconstruction_method', 'original')
                methods[method] = methods.get(method, 0) + 1
            
            for method, count in methods.items():
                print(f"  {method}: {count}")
        
        # 创建可视化对比
        create_comparison_plot(solutions)
        
        print("\n🎯 测试重点分析:")
        print("1. 普通圆弧/椭圆弧 - 所有方案应该结果相似")
        print("2. X轴镜像圆弧 - 关键测试案例，观察角度是否正确")
        print("3. Y轴镜像圆弧 - 另一个关键测试案例")
        print("4. 镜像椭圆弧 - 椭圆的镜像处理效果")
        
        print("\n💡 方案对比:")
        print("- 原始显示: 可能显示错误（180度旋转问题）")
        print("- 角度修正法: 直接修正角度，简单有效")
        print("- 坐标变换法: 通过逆变换处理")
        print("- 几何重建法: 基于采样点重建")
        print("- 采样定向法: 通过采样确定方向")
        print("- 用户建议法: 完整图形截取，最精确")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_comparison_plot(solutions):
    """创建对比图"""
    try:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('多解决方案对比 - 镜像问题处理效果', fontsize=16)
        
        solution_names = ["原始显示", "角度修正法", "坐标变换法", 
                         "几何重建法", "采样定向法", "用户建议法"]
        
        for ax, solution_name in zip(axes.flat, solution_names):
            ax.set_title(solution_name, fontsize=12)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            entities = solutions.get(solution_name, [])
            draw_entities_on_ax(entities, ax)
        
        # 调整布局
        plt.tight_layout()
        plt.show()
        
        print("📊 可视化对比图已显示")
        
    except Exception as e:
        print(f"⚠️ 创建对比图失败: {e}")

def draw_entities_on_ax(entities, ax):
    """在指定轴上绘制实体"""
    for entity in entities:
        try:
            entity_type = entity.get('type', 'UNKNOWN')
            color = entity.get('color', 'black')
            
            if entity_type == 'LINE':
                points = entity['points']
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                ax.plot(x_coords, y_coords, color=color, linewidth=1.5)
            
            elif entity_type == 'CIRCLE':
                center = entity['center']
                radius = entity['radius']
                circle = Circle(center, radius, fill=False, edgecolor=color, linewidth=1.5)
                ax.add_patch(circle)
            
            elif entity_type == 'ARC':
                center = entity['center']
                radius = entity['radius']
                start_angle = entity.get('start_angle', 0)
                end_angle = entity.get('end_angle', 360)
                
                # 简化的角度处理
                if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                    start_angle = np.degrees(start_angle)
                    end_angle = np.degrees(end_angle)
                
                arc = Arc(center, 2*radius, 2*radius,
                         theta1=start_angle, theta2=end_angle,
                         edgecolor=color, linewidth=2, fill=False)
                ax.add_patch(arc)
            
            elif entity_type == 'ELLIPSE':
                center = entity['center']
                major_axis = entity['major_axis']
                ratio = entity.get('ratio', 1.0)
                
                a = np.linalg.norm(major_axis)
                b = a * ratio
                angle = np.degrees(np.arctan2(major_axis[1], major_axis[0]))
                
                ellipse = MplEllipse(center, 2*a, 2*b, angle=angle,
                                   fill=False, edgecolor=color, linewidth=2)
                ax.add_patch(ellipse)
            
            elif entity_type == 'POLYLINE':
                # 重构后的多段线
                points = entity['points']
                if len(points) >= 2:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    
                    # 使用特殊样式表示重构结果
                    ax.plot(x_coords, y_coords, color=color, linewidth=3, 
                           alpha=0.8)
                    
                    # 标记采样点
                    ax.scatter(x_coords, y_coords, color=color, s=15, alpha=0.6)
        
        except Exception as e:
            print(f"⚠️ 绘制实体失败: {entity_type}, 错误: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 多解决方案重构器测试")
    print("🎯 解决镜像操作导致的坐标系手性变化问题")
    print("=" * 60)
    
    success = test_multi_solutions()
    
    if success:
        print("\n🎉 测试完成！")
        print("💡 建议:")
        print("  1. 观察对比图中各方案的处理效果")
        print("  2. 重点关注镜像圆弧的角度是否正确")
        print("  3. 评估哪种方案最适合您的需求")
        print("  4. 运行 dxf_display_test.py 进行交互式测试")
    else:
        print("\n❌ 测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
