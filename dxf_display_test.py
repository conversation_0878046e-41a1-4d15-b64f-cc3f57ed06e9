#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF文件显示测试小程序
专门用于测试圆弧椭圆重构方案的显示效果
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Circle, Arc, Ellipse as MplEllipse
import matplotlib.font_manager as fm
import numpy as np
import os
import sys

# 设置中文字体
def setup_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试常见的中文字体
        chinese_fonts = [
            'SimHei',           # 黑体
            'Microsoft YaHei',  # 微软雅黑
            'SimSun',          # 宋体
            'KaiTi',           # 楷体
            'FangSong',        # 仿宋
            'DejaVu Sans'      # 备用字体
        ]

        for font_name in chinese_fonts:
            try:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                # 测试字体是否可用
                fig, ax = plt.subplots(figsize=(1, 1))
                ax.text(0.5, 0.5, '测试', fontsize=12)
                plt.close(fig)
                print(f"✅ 使用中文字体: {font_name}")
                return True
            except:
                continue

        print("⚠️ 未找到合适的中文字体，使用默认字体")
        return False
    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")
        return False

# 初始化字体
setup_chinese_font()

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DXFDisplayTest:
    """DXF文件显示测试程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("DXF文件显示测试 - 圆弧椭圆重构验证")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.current_file = None
        self.raw_entities = []
        self.reconstructed_entities = []
        self.multi_solution_results = {}
        
        # 创建界面
        self.create_widgets()
        
        # 导入多种重构器
        try:
            from arc_ellipse_reconstructor import ArcEllipseReconstructor
            from multi_solution_reconstructor import MultiSolutionReconstructor
            from coordinate_system_detector import CoordinateSystemDetector
            self.reconstructor = ArcEllipseReconstructor(sample_points_count=50)
            self.multi_reconstructor = MultiSolutionReconstructor(sample_points_count=50)
            self.coord_detector = CoordinateSystemDetector()
            print("✅ 重构器加载成功 - 支持多种解决方案")
            print("✅ 坐标系手性检测器加载成功")
        except ImportError as e:
            print(f"⚠️ 重构器加载失败: {e}")
            self.reconstructor = None
            self.multi_reconstructor = None
            self.coord_detector = None
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择
        ttk.Button(control_frame, text="选择DXF文件", 
                  command=self.select_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 文件路径显示
        self.file_label = ttk.Label(control_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # 解决方案选择
        self.current_solution = tk.StringVar(value="原始显示")
        solution_label = ttk.Label(control_frame, text="解决方案:")
        solution_label.pack(side=tk.LEFT, padx=(0, 5))

        self.solution_combo = ttk.Combobox(control_frame, textvariable=self.current_solution,
                                          values=["原始显示", "角度修正法", "坐标变换法",
                                                 "几何重建法", "采样定向法", "用户建议法", "手性校正法"],
                                          state="readonly", width=12)
        self.solution_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.solution_combo.bind('<<ComboboxSelected>>', self.on_solution_changed)

        # 显示选项
        self.show_endpoints = tk.BooleanVar(value=True)
        self.show_full_shape = tk.BooleanVar(value=False)
        self.show_comparison = tk.BooleanVar(value=False)

        ttk.Checkbutton(control_frame, text="显示端点",
                       variable=self.show_endpoints,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Checkbutton(control_frame, text="显示完整图形",
                       variable=self.show_full_shape,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Checkbutton(control_frame, text="对比模式",
                       variable=self.show_comparison,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        ttk.Button(control_frame, text="刷新显示", 
                  command=self.update_display).pack(side=tk.LEFT, padx=(0, 10))
        
        # 显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图形
        self.fig, self.axes = plt.subplots(2, 4, figsize=(20, 10))
        self.fig.suptitle('DXF文件多解决方案对比测试', fontsize=16)

        # 设置子图标题
        solution_titles = ["原始显示", "角度修正法", "坐标变换法", "几何重建法",
                          "采样定向法", "用户建议法", "手性校正法", "对比分析"]

        for i, (ax, title) in enumerate(zip(self.axes.flat, solution_titles)):
            ax.set_title(title, fontsize=12)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, display_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 信息面板
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 统计信息
        self.info_text = tk.Text(info_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def select_file(self):
        """选择DXF文件"""
        file_path = filedialog.askopenfilename(
            title="选择DXF文件",
            filetypes=[
                ("DXF文件", "*.dxf"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.current_file = file_path
            self.file_label.config(text=f"文件: {os.path.basename(file_path)}")
            self.load_and_process_file()
    
    def load_and_process_file(self):
        """加载并处理DXF文件"""
        if not self.current_file:
            return
        
        try:
            # 加载DXF文件
            self.log_info(f"🔄 加载文件: {os.path.basename(self.current_file)}")
            self.raw_entities = self.load_dxf_file(self.current_file)
            self.log_info(f"✅ 加载完成，共 {len(self.raw_entities)} 个实体")
            
            # 统计原始实体类型
            entity_types = {}
            for entity in self.raw_entities:
                entity_type = entity.get('type', 'UNKNOWN')
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            self.log_info("📊 原始实体统计:")
            for entity_type, count in entity_types.items():
                self.log_info(f"  {entity_type}: {count}")
            
            # 多解决方案重构处理
            if self.multi_reconstructor:
                self.log_info("\n🔄 开始多解决方案重构处理...")
                self.log_info("🎯 问题分析:")
                self.log_info("  1. 镜像操作改变坐标系手性（左/右手系）")
                self.log_info("  2. 圆弧角度定义在镜像坐标系中反转方向")
                self.log_info("  3. 镜像轴为水平/垂直时，圆弧呈现180度旋转外观")
                self.log_info("\n📐 提供多种解决方案:")

                self.multi_solution_results = self.multi_reconstructor.reconstruct_with_all_solutions(self.raw_entities.copy())

                for solution_name, entities in self.multi_solution_results.items():
                    self.log_info(f"  ✅ {solution_name}: {len(entities)} 个实体")

                # 默认使用用户建议法
                self.reconstructed_entities = self.multi_solution_results.get("用户建议法", self.raw_entities.copy())
                self.log_info(f"\n🔧 当前使用方案: {self.current_solution.get()}")
            else:
                self.reconstructed_entities = self.raw_entities.copy()
                self.log_info("⚠️ 多解决方案重构器未加载，使用原始数据")
            
            # 更新显示
            self.update_display()
            
        except Exception as e:
            error_msg = f"❌ 加载文件失败: {str(e)}"
            self.log_info(error_msg)
            messagebox.showerror("错误", error_msg)

    def on_solution_changed(self, event=None):
        """解决方案切换事件处理"""
        if self.multi_solution_results:
            solution_name = self.current_solution.get()
            self.reconstructed_entities = self.multi_solution_results.get(solution_name, self.raw_entities.copy())
            self.log_info(f"🔄 切换到解决方案: {solution_name}")
            self.update_display()

    def load_dxf_file(self, file_path):
        """加载DXF文件"""
        entities = []

        # 尝试使用ezdxf库读取真实DXF文件
        try:
            import ezdxf
            doc = ezdxf.readfile(file_path)
            msp = doc.modelspace()

            self.log_info("📖 使用ezdxf库读取真实DXF文件")

            for entity in msp:
                entity_dict = self.convert_ezdxf_entity(entity)
                if entity_dict:
                    entities.append(entity_dict)

            self.log_info(f"✅ 成功读取 {len(entities)} 个实体")

        except ImportError:
            self.log_info("⚠️ ezdxf库未安装，使用模拟数据")
            entities = self.create_test_entities()
        except Exception as e:
            self.log_info(f"⚠️ 读取DXF文件失败: {e}，使用模拟数据")
            entities = self.create_test_entities()

        return entities

    def convert_ezdxf_entity(self, entity):
        """转换ezdxf实体为内部格式"""
        try:
            entity_type = entity.dxftype()

            if entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                return {
                    'type': 'LINE',
                    'points': [(start.x, start.y), (end.x, end.y)],
                    'layer': entity.dxf.layer,
                    'color': 'blue'
                }

            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                return {
                    'type': 'CIRCLE',
                    'center': (center.x, center.y),
                    'radius': entity.dxf.radius,
                    'layer': entity.dxf.layer,
                    'color': 'green'
                }

            elif entity_type == 'ARC':
                center = entity.dxf.center

                # 提取拉伸方向信息（关键！）
                extrusion_dir = getattr(entity.dxf, 'extrusion', (0, 0, 1))

                return {
                    'type': 'ARC',
                    'center': (center.x, center.y),
                    'radius': entity.dxf.radius,
                    'start_angle': entity.dxf.start_angle,
                    'end_angle': entity.dxf.end_angle,
                    'layer': entity.dxf.layer,
                    'color': 'red',
                    'extrusion_direction': (extrusion_dir.x, extrusion_dir.y, extrusion_dir.z)
                }

            elif entity_type == 'ELLIPSE':
                center = entity.dxf.center
                major_axis = entity.dxf.major_axis

                # 提取拉伸方向信息（关键！）
                extrusion_dir = getattr(entity.dxf, 'extrusion', (0, 0, 1))

                return {
                    'type': 'ELLIPSE',
                    'center': (center.x, center.y),
                    'major_axis': (major_axis.x, major_axis.y),
                    'ratio': entity.dxf.ratio,
                    'start_param': entity.dxf.start_param,
                    'end_param': entity.dxf.end_param,
                    'layer': entity.dxf.layer,
                    'color': 'purple',
                    'extrusion_direction': (extrusion_dir.x, extrusion_dir.y, extrusion_dir.z)
                }

            elif entity_type == 'LWPOLYLINE':
                points = []
                for point in entity.get_points():
                    # 确保只取前两个坐标（x, y），处理不同的点格式
                    if hasattr(point, 'x') and hasattr(point, 'y'):
                        points.append((float(point.x), float(point.y)))
                    elif isinstance(point, (list, tuple)) and len(point) >= 2:
                        points.append((float(point[0]), float(point[1])))
                    else:
                        # 跳过无效点
                        continue
                return {
                    'type': 'POLYLINE',
                    'points': points,
                    'layer': entity.dxf.layer,
                    'color': 'orange'
                }

            else:
                # 其他类型暂不处理
                return None

        except Exception as e:
            self.log_info(f"⚠️ 转换实体失败: {entity_type if 'entity_type' in locals() else 'UNKNOWN'}, 错误: {e}")
            return None

    def create_test_entities(self):
        """创建测试实体"""
        return [
            # 正常直线
            {
                'type': 'LINE',
                'points': [(10, 10), (50, 50)],
                'layer': 'lines',
                'color': 'blue'
            },
            # 正常圆
            {
                'type': 'CIRCLE',
                'center': (100, 100),
                'radius': 20,
                'layer': 'circles',
                'color': 'green'
            },
            # 正常圆弧
            {
                'type': 'ARC',
                'center': (200, 100),
                'radius': 25,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'arcs',
                'color': 'red'
            },
            # 跨越0度圆弧
            {
                'type': 'ARC',
                'center': (300, 100),
                'radius': 20,
                'start_angle': 270,
                'end_angle': 45,
                'layer': 'arcs',
                'color': 'red'
            },
            # 镜像圆弧
            {
                'type': 'ARC',
                'center': (400, 100),
                'radius': 22,
                'start_angle': 0,
                'end_angle': 180,
                'scale_x': -1.0,
                'scale_y': 1.0,
                'layer': 'arcs',
                'color': 'red'
            },
            # 正常椭圆
            {
                'type': 'ELLIPSE',
                'center': (150, 200),
                'major_axis': (30, 0),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': 2 * np.pi,
                'layer': 'ellipses',
                'color': 'purple'
            },
            # 椭圆弧
            {
                'type': 'ELLIPSE',
                'center': (250, 200),
                'major_axis': (25, 15),
                'ratio': 0.7,
                'start_param': 0,
                'end_param': np.pi,
                'layer': 'ellipses',
                'color': 'purple'
            },
            # 镜像椭圆
            {
                'type': 'ELLIPSE',
                'center': (350, 200),
                'major_axis': (20, 12),
                'ratio': 0.8,
                'start_param': 0,
                'end_param': 1.5 * np.pi,
                'scale_x': 1.0,
                'scale_y': -1.0,
                'layer': 'ellipses',
                'color': 'purple'
            }
        ]
    
    def update_display(self):
        """更新显示"""
        if not self.raw_entities:
            return

        # 清空所有画布
        for ax in self.axes.flat:
            ax.clear()

        # 重新设置标题和网格
        solution_titles = ["原始显示", "角度修正法", "坐标变换法",
                          "几何重建法", "采样定向法", "用户建议法"]

        for ax, title in zip(self.axes.flat, solution_titles):
            ax.set_title(title, fontsize=12)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)

        if self.show_comparison.get():
            # 对比模式：显示所有解决方案
            self.display_all_solutions()
        else:
            # 单一模式：只显示当前选择的解决方案
            self.display_single_solution()

        # 调整视图
        for ax in self.axes.flat:
            ax.relim()
            ax.autoscale()

        # 刷新画布
        self.canvas.draw()

    def display_all_solutions(self):
        """显示所有解决方案的对比"""
        if not self.multi_solution_results:
            return

        solution_names = ["原始显示", "角度修正法", "坐标变换法", "几何重建法",
                         "采样定向法", "用户建议法", "手性校正法"]

        for i, (ax, solution_name) in enumerate(zip(self.axes.flat, solution_names)):
            entities = self.multi_solution_results.get(solution_name, [])
            self.draw_entities(entities, ax, solution_name)

            # 为当前选择的方案添加高亮边框
            if solution_name == self.current_solution.get():
                ax.patch.set_edgecolor('red')
                ax.patch.set_linewidth(3)
            else:
                ax.patch.set_edgecolor('black')
                ax.patch.set_linewidth(1)

    def display_single_solution(self):
        """显示单一解决方案"""
        # 只在第一个子图显示当前方案
        current_entities = self.reconstructed_entities
        self.draw_entities(current_entities, self.axes[0, 0], self.current_solution.get())

        # 显示端点和完整图形（如果选中）
        if self.show_endpoints.get() or self.show_full_shape.get():
            self.draw_additional_info(self.axes[0, 0])

        # 其他子图显示提示信息
        for i, ax in enumerate(self.axes.flat[1:], 1):
            ax.text(0.5, 0.5, f'切换到"对比模式"\n查看所有解决方案',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=12, alpha=0.5)
    
    def draw_entities(self, entities, ax, mode):
        """绘制实体"""
        for entity in entities:
            try:
                entity_type = entity.get('type', 'UNKNOWN')
                color = entity.get('color', 'black')
                
                if entity_type == 'LINE':
                    points = entity['points']
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    ax.plot(x_coords, y_coords, color=color, linewidth=1.5, label=f'{mode}-LINE')
                
                elif entity_type == 'CIRCLE':
                    center = entity['center']
                    radius = entity['radius']
                    circle = Circle(center, radius, fill=False, edgecolor=color, linewidth=1.5)
                    ax.add_patch(circle)
                
                elif entity_type == 'ARC':
                    # 使用多段线绘制圆弧以正确处理变换
                    center = np.array(entity['center'])
                    radius = entity['radius']
                    start_angle = entity.get('start_angle', 0)
                    end_angle = entity.get('end_angle', 360)

                    # 处理镜像变换
                    scale_x = entity.get('scale_x', 1.0)
                    scale_y = entity.get('scale_y', 1.0)
                    is_mirrored = (scale_x * scale_y) < 0

                    # 角度单位转换
                    if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                        start_angle = np.degrees(start_angle)
                        end_angle = np.degrees(end_angle)

                    # 处理镜像对角度的影响
                    if is_mirrored:
                        original_start = start_angle
                        original_end = end_angle
                        start_angle = (360 - original_end) % 360
                        end_angle = (360 - original_start) % 360

                    # 生成圆弧点
                    if end_angle < start_angle:
                        end_angle += 360

                    num_points = max(int(30 * (end_angle - start_angle) / 360), 3)
                    angles = np.linspace(np.radians(start_angle), np.radians(end_angle), num_points)

                    x_coords = center[0] + radius * np.cos(angles)
                    y_coords = center[1] + radius * np.sin(angles)

                    # 应用缩放变换
                    if scale_x != 1.0 or scale_y != 1.0:
                        x_coords = x_coords * scale_x
                        y_coords = y_coords * scale_y

                    ax.plot(x_coords, y_coords, color=color, linewidth=2, alpha=0.8)

                    # 标记端点
                    if len(x_coords) > 0:
                        ax.scatter([x_coords[0], x_coords[-1]], [y_coords[0], y_coords[-1]],
                                 color=color, s=30, alpha=0.7, zorder=5)
                
                elif entity_type == 'ELLIPSE':
                    # 使用多段线绘制椭圆以正确处理变换
                    center = np.array(entity['center'])
                    major_axis = np.array(entity['major_axis'])
                    ratio = entity.get('ratio', 1.0)
                    start_param = entity.get('start_param', 0)
                    end_param = entity.get('end_param', 2 * np.pi)

                    # 计算椭圆参数
                    a = np.linalg.norm(major_axis)
                    b = a * ratio
                    angle = np.arctan2(major_axis[1], major_axis[0])

                    # 处理镜像变换
                    scale_x = entity.get('scale_x', 1.0)
                    scale_y = entity.get('scale_y', 1.0)
                    is_mirrored = (scale_x * scale_y) < 0

                    if is_mirrored:
                        angle = -angle
                        if abs(end_param - start_param) < 2 * np.pi - 0.1:
                            original_start = start_param
                            original_end = end_param
                            start_param = 2 * np.pi - original_end
                            end_param = 2 * np.pi - original_start

                    # 生成椭圆点
                    if end_param < start_param:
                        end_param += 2 * np.pi

                    param_range = end_param - start_param
                    num_points = max(int(40 * param_range / (2 * np.pi)), 3)
                    params = np.linspace(start_param, end_param, num_points)

                    # 计算椭圆上的点
                    x_std = a * np.cos(params)
                    y_std = b * np.sin(params)

                    # 应用旋转
                    cos_angle = np.cos(angle)
                    sin_angle = np.sin(angle)
                    x_rot = x_std * cos_angle - y_std * sin_angle
                    y_rot = x_std * sin_angle + y_std * cos_angle

                    # 平移到中心
                    x_coords = center[0] + x_rot
                    y_coords = center[1] + y_rot

                    # 应用缩放变换
                    if scale_x != 1.0 or scale_y != 1.0:
                        x_coords = x_coords * scale_x
                        y_coords = y_coords * scale_y

                    ax.plot(x_coords, y_coords, color=color, linewidth=2, alpha=0.8)

                    # 标记端点
                    if len(x_coords) > 0:
                        ax.scatter([x_coords[0], x_coords[-1]], [y_coords[0], y_coords[-1]],
                                 color=color, s=30, alpha=0.7, zorder=5)
                
                elif entity_type == 'POLYLINE':
                    # 重构后的多段线
                    points = entity['points']
                    if len(points) >= 2:
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        
                        # 如果是重构的实体，使用特殊样式
                        if 'original_type' in entity:
                            original_type = entity['original_type']
                            ax.plot(x_coords, y_coords, color=color, linewidth=2, 
                                   linestyle='-', alpha=0.8, 
                                   label=f'{mode}-{original_type}→POLYLINE')
                            
                            # 标记采样点
                            ax.scatter(x_coords, y_coords, color=color, s=10, alpha=0.6)
                        else:
                            ax.plot(x_coords, y_coords, color=color, linewidth=1.5)
                
            except Exception as e:
                self.log_info(f"⚠️ 绘制实体失败: {entity_type}, 错误: {e}")

    def draw_additional_info(self, ax):
        """绘制附加信息：端点、完整图形等"""
        if not self.reconstructed_entities:
            return

        for entity in self.reconstructed_entities:
            if 'reconstruction_info' not in entity:
                continue

            info = entity['reconstruction_info']
            original_type = entity.get('original_type', '')

            try:
                # 绘制端点
                if self.show_endpoints.get() and 'start_point' in info and 'end_point' in info:
                    start_point = info['start_point']
                    end_point = info['end_point']

                    # 起点用绿色圆圈标记
                    ax.scatter(start_point[0], start_point[1], color='green', s=50,
                              marker='o', edgecolor='darkgreen', linewidth=2,
                              label='起点', zorder=10)

                    # 终点用红色方块标记
                    ax.scatter(end_point[0], end_point[1], color='red', s=50,
                              marker='s', edgecolor='darkred', linewidth=2,
                              label='终点', zorder=10)

                    # 添加端点标注
                    ax.annotate('起点', start_point, xytext=(5, 5),
                               textcoords='offset points', fontsize=8, color='green')
                    ax.annotate('终点', end_point, xytext=(5, 5),
                               textcoords='offset points', fontsize=8, color='red')

                # 绘制完整图形（虚线）
                if self.show_full_shape.get():
                    center = info['center']

                    if original_type == 'ARC':
                        # 绘制完整圆
                        radius = info['radius']
                        full_circle = Circle(center, radius, fill=False,
                                           edgecolor='gray', linewidth=1,
                                           linestyle='--', alpha=0.5,
                                           label='完整圆')
                        ax.add_patch(full_circle)

                        # 标记中心点
                        ax.scatter(center[0], center[1], color='blue', s=30,
                                  marker='+', linewidth=2, label='中心点')

                    elif original_type == 'ELLIPSE':
                        # 绘制完整椭圆
                        a = info['a']
                        b = info['b']
                        angle_deg = np.degrees(info['angle'])

                        full_ellipse = MplEllipse(center, 2*a, 2*b, angle=angle_deg,
                                                fill=False, edgecolor='gray',
                                                linewidth=1, linestyle='--',
                                                alpha=0.5, label='完整椭圆')
                        ax.add_patch(full_ellipse)

                        # 标记中心点
                        ax.scatter(center[0], center[1], color='blue', s=30,
                                  marker='+', linewidth=2, label='中心点')

                        # 绘制长轴方向
                        major_axis_end = np.array(center) + a * np.array([np.cos(info['angle']), np.sin(info['angle'])])
                        ax.plot([center[0], major_axis_end[0]],
                               [center[1], major_axis_end[1]],
                               color='blue', linewidth=1, linestyle=':',
                               alpha=0.7, label='长轴方向')

            except Exception as e:
                self.log_info(f"⚠️ 绘制附加信息失败: {original_type}, 错误: {e}")

    def log_info(self, message):
        """记录信息"""
        self.info_text.insert(tk.END, message + "\n")
        self.info_text.see(tk.END)
        self.root.update_idletasks()
        print(message)

def main():
    """主函数"""
    root = tk.Tk()
    app = DXFDisplayTest(root)
    
    # 显示使用说明
    help_text = """
DXF文件多解决方案对比测试程序使用说明：

🎯 问题分析：
1. 镜像操作改变坐标系手性（左/右手系）
2. 圆弧角度定义在镜像坐标系中反转方向
3. 镜像轴为水平/垂直时，圆弧呈现180度旋转外观

🔧 提供的解决方案：
1. 原始显示 - 不做任何处理（可能有问题）
2. 角度修正法 - 直接修正镜像后的角度
3. 坐标变换法 - 通过逆变换恢复原始坐标系
4. 几何重建法 - 基于几何点变换重建图形
5. 采样定向法 - 通过采样点确定正确方向
6. 用户建议法 - 基于完整图形截取的精确方法

📋 操作说明：
1. 点击"选择DXF文件"按钮选择要测试的DXF文件
2. 程序自动使用所有方案处理文件
3. 使用"解决方案"下拉框切换不同的处理方案
4. 勾选"对比模式"同时查看所有方案的效果
5. 使用其他选项控制显示细节：
   - "显示端点"：显示起点（绿色圆）和终点（红色方块）
   - "显示完整图形"：显示完整的圆/椭圆参考
6. 查看信息面板了解各方案的处理详情

🎯 测试重点：
- 对比各方案对镜像圆弧的处理效果
- 验证角度方向是否正确
- 检查端点位置是否准确
- 评估各方案的适用性

💡 推荐方案：
- 用户建议法：基于完整图形截取，精度最高
- 几何重建法：通用性强，适合复杂变换
- 角度修正法：简单直接，适合简单镜像

注意：程序支持真实DXF文件和模拟测试数据
"""
    
    app.log_info(help_text)
    
    root.mainloop()

if __name__ == "__main__":
    main()
