#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配色系统背景修复
验证背景色只应用到CAD填充控制区域（右侧概览），而不影响整个图像预览框
"""

import sys
import os
sys.path.append('.')

def test_background_color_application():
    """测试背景色应用范围"""
    print("🧪 测试配色系统背景修复...")
    
    try:
        # 导入必要的模块
        from cad_visualizer import CADVisualizer
        import matplotlib.pyplot as plt
        
        # 创建可视化器实例
        visualizer = CADVisualizer()
        
        # 记录初始背景色
        initial_fig_bg = visualizer.fig.get_facecolor()
        initial_detail_bg = visualizer.ax_detail.get_facecolor()
        initial_overview_bg = visualizer.ax_overview.get_facecolor()
        
        print(f"📊 初始背景色:")
        print(f"  - 整体图形: {initial_fig_bg}")
        print(f"  - 详细视图: {initial_detail_bg}")
        print(f"  - 概览视图: {initial_overview_bg}")
        
        # 应用测试配色方案
        test_color_scheme = {
            'background': '#FFE4E1',  # 浅粉色背景
            'wall': '#000000',
            'door_window': '#FF0000'
        }
        
        print(f"\n🎨 应用测试配色方案...")
        visualizer.update_color_scheme(test_color_scheme)
        
        # 检查应用后的背景色
        after_fig_bg = visualizer.fig.get_facecolor()
        after_detail_bg = visualizer.ax_detail.get_facecolor()
        after_overview_bg = visualizer.ax_overview.get_facecolor()
        
        print(f"\n📊 应用后背景色:")
        print(f"  - 整体图形: {after_fig_bg}")
        print(f"  - 详细视图: {after_detail_bg}")
        print(f"  - 概览视图: {after_overview_bg}")
        
        # 验证修复效果
        print(f"\n✅ 修复验证:")
        
        # 整体图形背景应该保持不变
        if initial_fig_bg == after_fig_bg:
            print(f"  ✅ 整体图形背景保持不变: {after_fig_bg}")
        else:
            print(f"  ❌ 整体图形背景被改变: {initial_fig_bg} -> {after_fig_bg}")
        
        # 详细视图背景应该保持不变
        if initial_detail_bg == after_detail_bg:
            print(f"  ✅ 详细视图背景保持不变: {after_detail_bg}")
        else:
            print(f"  ❌ 详细视图背景被改变: {initial_detail_bg} -> {after_detail_bg}")
        
        # 概览视图背景应该改变为新的配色
        if after_overview_bg != initial_overview_bg:
            print(f"  ✅ 概览视图背景已更新: {initial_overview_bg} -> {after_overview_bg}")
        else:
            print(f"  ❌ 概览视图背景未更新: {after_overview_bg}")
        
        # 检查是否符合预期
        expected_overview_bg = test_color_scheme['background']
        # matplotlib颜色可能会被转换，所以我们检查是否接近
        print(f"  📋 期望的概览背景色: {expected_overview_bg}")
        
        print(f"\n🎯 修复结果:")
        if (initial_fig_bg == after_fig_bg and 
            initial_detail_bg == after_detail_bg and 
            after_overview_bg != initial_overview_bg):
            print("  ✅ 修复成功！背景色只应用到CAD填充控制区域")
            return True
        else:
            print("  ❌ 修复失败！背景色应用范围不正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_scheme_persistence():
    """测试配色方案持久化"""
    print(f"\n🧪 测试配色方案持久化...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 应用配色方案
        test_scheme = {
            'background': '#F0F8FF',  # 爱丽丝蓝
            'wall': '#8B4513',
            'door_window': '#FF6347'
        }
        
        visualizer.update_color_scheme(test_scheme)
        
        # 检查配色方案是否被保存
        if hasattr(visualizer, 'color_scheme'):
            print("  ✅ 可视化器有color_scheme属性")
            
            # 检查关键颜色是否被更新
            if 'background' in visualizer.color_scheme:
                print(f"  ✅ 背景色已保存: {visualizer.color_scheme['background']}")
            
            if 'wall' in visualizer.color_scheme:
                print(f"  ✅ 墙体颜色已保存: {visualizer.color_scheme['wall']}")
                
            return True
        else:
            print("  ❌ 可视化器缺少color_scheme属性")
            return False
            
    except Exception as e:
        print(f"❌ 持久化测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始配色系统背景修复测试\n")
    
    # 测试背景色应用范围
    bg_test_result = test_background_color_application()
    
    # 测试配色方案持久化
    persistence_test_result = test_color_scheme_persistence()
    
    print(f"\n📋 测试总结:")
    print(f"  - 背景色应用范围: {'✅ 通过' if bg_test_result else '❌ 失败'}")
    print(f"  - 配色方案持久化: {'✅ 通过' if persistence_test_result else '❌ 失败'}")
    
    if bg_test_result and persistence_test_result:
        print(f"\n🎉 所有测试通过！配色系统背景修复成功")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")
