#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试逻辑层面的重复调用修复效果
验证：
1. 状态管理避免重复调用
2. 滚动状态管理
3. 逻辑控制而非时间控制
"""

import os
import sys
import tkinter as tk
from unittest.mock import Mock, MagicMock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_state_management():
    """测试状态管理机制"""
    print("=" * 60)
    print("测试状态管理机制")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.status_var = Mock()
        
        print("\n🔍 测试1: 智能组列表更新状态管理")
        
        # 第一次调用
        app._smart_update_group_list("test_reason_1")
        
        # 检查状态
        if hasattr(app, '_update_state'):
            print(f"✅ 更新状态已初始化: {app._update_state}")
        else:
            print("❌ 更新状态未初始化")
        
        print("\n🔍 测试2: 重复调用检测")
        
        # 模拟正在更新的状态
        app._update_state['group_list_updating'] = True
        
        # 尝试再次调用（应该被跳过）
        with patch('builtins.print') as mock_print:
            app._smart_update_group_list("test_reason_2")
            
            # 检查是否输出了跳过信息
            skip_called = any("跳过重复的组列表更新请求" in str(call) for call in mock_print.call_args_list)
            if skip_called:
                print("✅ 重复调用被正确跳过")
            else:
                print("❌ 重复调用没有被跳过")
        
        # 重置状态
        app._update_state['group_list_updating'] = False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scroll_state_management():
    """测试滚动状态管理"""
    print("\n" + "=" * 60)
    print("测试滚动状态管理")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        app.group_tree = Mock()
        
        # 模拟TreeView数据
        mock_items = ['item1', 'item2', 'item3']
        app.group_tree.get_children.return_value = mock_items
        
        def mock_item(item_id, key):
            if key == 'text':
                return "组1"
            elif key == 'values':
                return ['未标注', '待标注', '4']
            return []
        
        app.group_tree.item.side_effect = mock_item
        
        print("\n🔍 测试滚动状态初始化")
        
        # 第一次调用滚动
        app._scroll_to_first_unlabeled_group()
        
        # 检查状态是否初始化
        if hasattr(app, '_scroll_state'):
            print(f"✅ 滚动状态已初始化: {app._scroll_state}")
        else:
            print("❌ 滚动状态未初始化")
        
        print("\n🔍 测试重复滚动到同一组的检测")
        
        # 重置mock调用计数
        app.group_tree.see.reset_mock()
        
        # 再次调用滚动到同一组（应该被跳过）
        app._scroll_to_first_unlabeled_group()
        
        # 检查是否跳过了重复滚动
        if app.group_tree.see.call_count == 0:
            print("✅ 重复滚动到同一组被正确跳过")
        else:
            print(f"❌ 重复滚动没有被跳过，调用次数: {app.group_tree.see.call_count}")
        
        print("\n🔍 测试滚动中状态检测")
        
        # 模拟正在滚动的状态
        app._scroll_state['scrolling'] = True
        
        # 重置mock
        app.group_tree.see.reset_mock()
        
        # 尝试滚动（应该被跳过）
        app._scroll_to_first_unlabeled_group()
        
        if app.group_tree.see.call_count == 0:
            print("✅ 滚动中状态检测正确，重复调用被跳过")
        else:
            print(f"❌ 滚动中状态检测失败，调用次数: {app.group_tree.see.call_count}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logical_vs_time_control():
    """测试逻辑控制 vs 时间控制"""
    print("\n" + "=" * 60)
    print("测试逻辑控制 vs 时间控制")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.status_var = Mock()
        app.update_group_list = Mock()
        
        print("\n🔍 测试逻辑控制机制")
        
        # 连续多次快速调用
        call_count = 0
        for i in range(5):
            app._smart_update_group_list(f"test_call_{i}")
            if app.update_group_list.called:
                call_count += 1
                app.update_group_list.reset_mock()
        
        print(f"连续5次调用，实际执行次数: {call_count}")
        
        if call_count == 1:
            print("✅ 逻辑控制生效：只执行了1次实际更新")
        else:
            print(f"❌ 逻辑控制可能失效：执行了{call_count}次更新")
        
        print("\n🔍 测试状态重置后的正常调用")
        
        # 确保状态已重置
        if hasattr(app, '_update_state'):
            app._update_state['group_list_updating'] = False
        
        # 重置mock
        app.update_group_list.reset_mock()
        
        # 再次调用（应该正常执行）
        app._smart_update_group_list("after_reset")
        
        if app.update_group_list.called:
            print("✅ 状态重置后正常调用成功")
        else:
            print("❌ 状态重置后调用失败")
        
        root.destroy()
        return call_count == 1
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_scenario():
    """测试集成场景：模拟真实的状态更新流程"""
    print("\n" + "=" * 60)
    print("测试集成场景")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.status_var = Mock()
        app.update_group_list = Mock()
        app.display_file = "test.dxf"
        app.processor = Mock()
        app.processor.current_file = "test.dxf"
        
        print("\n🔍 模拟真实的状态更新流程")
        
        # 模拟一系列状态更新
        status_updates = [
            ("manual_group", {"index": 1, "total": 5, "entity_count": 10}),
            ("group_labeled", ("group1", "wall", 10)),
            ("manual_group", {"index": 2, "total": 5, "entity_count": 8}),
            ("completed", "处理完成"),
            ("manual_complete", "手动标注完成"),
        ]
        
        update_count = 0
        for status_type, data in status_updates:
            app.update_group_list.reset_mock()
            app._handle_status_update_clean(status_type, data)
            if app.update_group_list.called:
                update_count += 1
        
        print(f"5个状态更新，触发的组列表更新次数: {update_count}")
        
        # 理想情况下，应该只有需要更新界面的状态才触发更新
        # 根据逻辑，manual_group、group_labeled、completed、manual_complete 都需要更新
        expected_updates = 4  # 预期的更新次数
        
        if update_count <= expected_updates:
            print(f"✅ 集成测试成功：更新次数合理 ({update_count} <= {expected_updates})")
        else:
            print(f"❌ 集成测试失败：更新次数过多 ({update_count} > {expected_updates})")
        
        root.destroy()
        return update_count <= expected_updates
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_time_dependency():
    """测试没有时间依赖性"""
    print("\n" + "=" * 60)
    print("测试没有时间依赖性")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.status_var = Mock()
        app.update_group_list = Mock()
        
        print("\n🔍 测试立即连续调用（无时间间隔）")
        
        # 立即连续调用，不依赖时间间隔
        app._smart_update_group_list("call_1")
        first_called = app.update_group_list.called
        
        app.update_group_list.reset_mock()
        app._smart_update_group_list("call_2")  # 立即第二次调用
        second_called = app.update_group_list.called
        
        print(f"第一次调用执行: {first_called}")
        print(f"第二次调用执行: {second_called}")
        
        if first_called and not second_called:
            print("✅ 逻辑控制生效：第一次执行，第二次被跳过（无时间依赖）")
        else:
            print("❌ 逻辑控制可能失效")
        
        root.destroy()
        return first_called and not second_called
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试逻辑层面的重复调用修复效果...")
    
    test1 = test_state_management()
    test2 = test_scroll_state_management()
    test3 = test_logical_vs_time_control()
    test4 = test_integration_scenario()
    test5 = test_no_time_dependency()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = {
        "状态管理机制": test1,
        "滚动状态管理": test2,
        "逻辑控制机制": test3,
        "集成场景测试": test4,
        "无时间依赖性": test5,
    }
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！逻辑层面的重复调用修复成功。")
        print("\n📋 修复特点：")
        print("- 使用状态管理而非时间控制")
        print("- 逻辑层面避免重复调用")
        print("- 无时间依赖性，响应更快")
        print("- 状态清晰，易于调试")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码。")
