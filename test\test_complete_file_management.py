#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的文件管理功能测试
"""

import sys
import os
import tkinter as tk
import tempfile
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_environment():
    """创建测试环境"""
    print("=== 创建测试环境 ===")
    
    # 创建临时文件夹
    temp_dir = tempfile.mkdtemp(prefix="cad_test_")
    print(f"测试文件夹: {temp_dir}")
    
    # 创建测试DXF文件（简单的文本文件模拟）
    test_files = [
        'building1.dxf',
        'building2.dxf', 
        'building3.dxf',
        'plan.dwg',
        'readme.txt'  # 非CAD文件，应该被过滤
    ]
    
    for file_name in test_files:
        file_path = os.path.join(temp_dir, file_name)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# 测试文件: {file_name}\n")
            f.write("# 这是一个模拟的CAD文件\n")
            f.write("LINE\n0,0\n10,10\n")
    
    print(f"创建了 {len(test_files)} 个测试文件")
    return temp_dir

def test_application_startup():
    """测试应用启动"""
    print("\n=== 测试应用启动 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = EnhancedCADAppV2(root)
        print("✅ 应用启动成功")
        
        # 检查界面元素是否创建
        if hasattr(app, 'file_combo') and app.file_combo:
            print("✅ 文件选择下拉菜单已创建")
        else:
            print("❌ 文件选择下拉菜单未创建")
            
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_folder_selection_and_scanning(test_folder):
    """测试文件夹选择和扫描"""
    print(f"\n=== 测试文件夹选择和扫描 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 模拟文件夹选择
        app.current_folder = test_folder
        app._scan_folder_files()
        
        # 检查扫描结果
        expected_cad_files = 4  # building1.dxf, building2.dxf, building3.dxf, plan.dwg
        actual_cad_files = len(app.all_files)
        
        if actual_cad_files == expected_cad_files:
            print(f"✅ 正确扫描到 {actual_cad_files} 个CAD文件")
        else:
            print(f"❌ 扫描结果错误: 期望 {expected_cad_files}，实际 {actual_cad_files}")
            print(f"扫描到的文件: {[os.path.basename(f) for f in app.all_files]}")
        
        # 检查文件状态初始化
        all_status_correct = True
        for file_path in app.all_files:
            file_name = os.path.basename(file_path)
            if file_name in app.file_status:
                status = app.file_status[file_name]
                if (status.get('processing_status') != 'unprocessed' or 
                    status.get('annotation_status') != 'unannotated'):
                    all_status_correct = False
                    print(f"❌ {file_name} 状态初始化错误: {status}")
            else:
                all_status_correct = False
                print(f"❌ {file_name} 状态未初始化")
        
        if all_status_correct:
            print("✅ 所有文件状态初始化正确")
        
        root.destroy()
        return actual_cad_files == expected_cad_files and all_status_correct
        
    except Exception as e:
        print(f"❌ 文件夹扫描测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_status_management():
    """测试文件状态管理"""
    print(f"\n=== 测试文件状态管理 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 模拟文件数据
        app.all_files = ['test1.dxf', 'test2.dxf', 'test3.dxf']
        app.current_file = 'test1.dxf'
        
        # 设置不同的文件状态
        app.file_status = {
            'test1.dxf': {
                'processing_status': 'completed',
                'annotation_status': 'incomplete'
            },
            'test2.dxf': {
                'processing_status': 'processing',
                'annotation_status': 'unannotated'
            },
            'test3.dxf': {
                'processing_status': 'unprocessed',
                'annotation_status': 'unannotated'
            }
        }
        
        # 测试状态显示格式
        app.update_file_combo()
        
        # 检查下拉菜单的值
        if app.file_combo and hasattr(app.file_combo, 'cget'):
            values = app.file_combo.cget('values')
            print(f"下拉菜单选项: {values}")
            
            # 检查当前文件是否标红
            current_file_found = False
            for value in values:
                if '[test1.dxf]' in value:
                    current_file_found = True
                    print(f"✅ 当前文件正确标红: {value}")
                    break
            
            if not current_file_found:
                print("❌ 当前文件未正确标红")
                return False
        
        print("✅ 文件状态管理测试通过")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 文件状态管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_save_load():
    """测试数据保存和加载"""
    print(f"\n=== 测试数据保存和加载 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建临时文件夹
        with tempfile.TemporaryDirectory() as temp_dir:
            root = tk.Tk()
            root.withdraw()
            
            app = EnhancedCADAppV2(root)
            
            # 设置测试数据
            app.current_folder = temp_dir
            app.all_files = [os.path.join(temp_dir, 'test.dxf')]
            app.file_status = {
                'test.dxf': {
                    'processing_status': 'completed',
                    'annotation_status': 'completed'
                }
            }
            app.file_data = {
                'test.dxf': {
                    'entities': ['entity1', 'entity2'],
                    'groups': ['group1'],
                    'labeled_entities': ['entity1'],
                    'dataset': [],
                    'groups_info': [],
                    'group_fill_status': {},
                    'timestamp': '2024-01-01T12:00:00'
                }
            }
            
            # 测试保存
            app.save_all_data()
            
            # 检查保存文件是否存在
            save_path = os.path.join(temp_dir, 'cad_annotation_data.json')
            if os.path.exists(save_path):
                print("✅ 数据文件保存成功")
                
                # 清空数据
                app.file_status = {}
                app.file_data = {}
                
                # 测试加载
                app.load_all_data()
                
                # 检查数据是否正确恢复
                if ('test.dxf' in app.file_status and 
                    'test.dxf' in app.file_data):
                    print("✅ 数据加载成功")
                    
                    # 检查数据完整性
                    status = app.file_status['test.dxf']
                    data = app.file_data['test.dxf']
                    
                    if (status.get('processing_status') == 'completed' and
                        len(data.get('entities', [])) == 2):
                        print("✅ 数据完整性验证通过")
                        root.destroy()
                        return True
                    else:
                        print("❌ 数据完整性验证失败")
                else:
                    print("❌ 数据加载失败")
            else:
                print("❌ 数据文件保存失败")
            
            root.destroy()
            return False
        
    except Exception as e:
        print(f"❌ 数据保存加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("完整文件管理功能测试")
    print("=" * 60)
    
    # 创建测试环境
    test_folder = create_test_environment()
    
    try:
        # 运行测试
        tests = [
            ("应用启动测试", lambda: test_application_startup()),
            ("文件夹扫描测试", lambda: test_folder_selection_and_scanning(test_folder)),
            ("文件状态管理测试", lambda: test_file_status_management()),
            ("数据保存加载测试", lambda: test_data_save_load())
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 {test_name}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"💥 {test_name} 异常: {e}")
        
        print(f"\n" + "=" * 60)
        print(f"测试结果: {passed}/{total} 项通过")
        
        if passed == total:
            print("🎉 所有测试通过！文件管理功能完全正常。")
            print("\n📋 功能验证完成:")
            print("✅ 应用正常启动")
            print("✅ 文件夹扫描正确")
            print("✅ 文件状态管理正常")
            print("✅ 数据保存加载正常")
            print("\n🚀 可以安全使用文件管理功能！")
        else:
            print("⚠️ 部分测试失败，需要进一步检查。")
    
    finally:
        # 清理测试环境
        try:
            import shutil
            shutil.rmtree(test_folder)
            print(f"\n🧹 测试环境已清理: {test_folder}")
        except:
            print(f"\n⚠️ 请手动清理测试文件夹: {test_folder}")

if __name__ == "__main__":
    main()
