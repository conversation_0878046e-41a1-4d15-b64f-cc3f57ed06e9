#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的圆弧变换测试
"""

import sys
import os
import math

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import TransformationMatrix, CADDataProcessor

def create_mock_arc():
    """创建模拟圆弧"""
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    class MockDxf:
        def __init__(self):
            self.center = MockPoint(0, 0)
            self.radius = 1.0
            self.start_angle = 0
            self.end_angle = 90
    
    class MockDoc:
        def __init__(self):
            self.header = {'$ANGDIR': 0}
    
    class MockArc:
        def __init__(self):
            self.dxf = MockDxf()
            self.doc = MockDoc()
    
    return MockArc()

def test_basic_transformation():
    """测试基本变换功能"""
    print("=== 基本变换测试 ===")
    
    # 创建变换矩阵
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    # 测试1: 无变换
    matrix = TransformationMatrix(base_point, 0, (1.0, 1.0))
    point = (1.0, 0.0)
    result = matrix.apply(point)
    print(f"无变换: {point} → {result}")
    
    # 测试2: 镜像检测
    matrix_mirror = TransformationMatrix(base_point, 0, (1.0, -1.0))
    is_mirrored = matrix_mirror.is_mirrored()
    print(f"镜像检测: {is_mirrored}")
    
    return True

def test_arc_transformation():
    """测试圆弧变换"""
    print("\n=== 圆弧变换测试 ===")
    
    processor = CADDataProcessor()
    arc = create_mock_arc()
    
    # 测试场景
    test_cases = [
        {
            'name': '无变换',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (1.0, 1.0)
        },
        {
            'name': 'X轴镜像',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (1.0, -1.0)
        },
        {
            'name': 'Y轴镜像',
            'base_point': type('obj', (object,), {'x': 0, 'y': 0})(),
            'rotation': 0,
            'scale': (-1.0, 1.0)
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        
        try:
            matrix = TransformationMatrix(case['base_point'], case['rotation'], case['scale'])
            result = processor._transform_arc(arc, matrix)
            
            print(f"原始圆弧: 中心(0, 0), 半径=1.0, 角度=0°-90°")
            print(f"变换后: 中心({result['center'][0]:.2f}, {result['center'][1]:.2f}), "
                  f"半径={result['radius']:.2f}, 角度={result['start_angle']:.1f}°-{result['end_angle']:.1f}°")
            print(f"镜像检测: {matrix.is_mirrored()}")
            
        except Exception as e:
            print(f"变换失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("简单圆弧变换测试")
    print("=" * 30)
    
    try:
        # 基本测试
        test_basic_transformation()
        
        # 圆弧变换测试
        test_arc_transformation()
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
