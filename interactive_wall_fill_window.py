#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交互式墙体填充窗口 - 改进版
支持：
1. 自动填充所有墙体组
2. 墙体组列表显示状态（已填充、未填充、填充中、空腔）
3. 手动修改填充结果
4. 识别为空腔功能
5. 显示填充失败原因
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.font_manager as fm
import numpy as np
from shapely.geometry import LineString, Point
import math

class InteractiveWallFillWindow:
    """交互式墙体填充窗口 - 改进版"""

    def __init__(self, parent, entities, wall_fill_processor, unified_fill_method=None):
        self.parent = parent
        self.entities = entities
        self.wall_fill_processor = wall_fill_processor
        self.unified_fill_method = unified_fill_method  # 统一的填充方法

        # 配置中文字体
        self._setup_chinese_font()

        # 墙体组状态
        self.wall_groups = []
        self.group_status = {}  # 状态：'未填充', '已填充', '填充中', '空腔', '填充失败'
        self.group_results = {}  # 存储每个组的填充结果
        self.group_reasons = {}  # 存储填充失败的原因
        
        # 当前处理状态
        self.current_group_index = 0
        self.current_fill_result = None
        
        # 创建窗口
        # 检查parent是否是Tkinter窗口，如果不是，尝试获取其根窗口
        if hasattr(parent, 'tk') and parent.tk:
            # parent是Tkinter窗口
            self.window = tk.Toplevel(parent)
        elif hasattr(parent, 'root'):
            # parent是应用实例，使用其root属性
            self.window = tk.Toplevel(parent.root)
        else:
            # 尝试其他方式获取根窗口
            try:
                # 如果parent有master属性
                if hasattr(parent, 'master'):
                    self.window = tk.Toplevel(parent.master)
                else:
                    # 最后尝试直接创建Toplevel
                    self.window = tk.Toplevel()
            except Exception as e:
                print(f"创建Toplevel窗口失败: {e}")
                # 如果都失败了，创建一个独立的Tk窗口
                self.window = tk.Tk()
        self.window.title("交互式墙体填充 - 改进版")
        self.window.geometry("1400x900")
        
        # 创建界面
        self._create_interface()

        # 初始化墙体组并自动填充（使用统一逻辑）
        self._identify_and_auto_fill_groups_unified()

    def _setup_chinese_font(self):
        """配置中文字体支持"""
        try:
            # 尝试多个中文字体
            chinese_fonts = [
                'SimHei',           # 黑体
                'Microsoft YaHei',  # 微软雅黑
                'SimSun',           # 宋体
                'KaiTi',            # 楷体
                'FangSong'          # 仿宋
            ]

            # 获取系统可用字体
            available_fonts = [f.name for f in fm.fontManager.ttflist]

            # 找到第一个可用的中文字体
            self.chinese_font = None
            for font in chinese_fonts:
                if font in available_fonts:
                    self.chinese_font = font
                    break

            # 如果没有找到中文字体，使用默认字体
            if not self.chinese_font:
                self.chinese_font = 'DejaVu Sans'

            # 设置matplotlib的默认字体
            plt.rcParams['font.sans-serif'] = [self.chinese_font]
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        except Exception as e:
            # 如果配置失败，使用默认字体
            self.chinese_font = 'DejaVu Sans'
        
    def _create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=350)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 控制面板内容
        self._create_control_panel(control_frame)
        
        # 显示区域内容
        self._create_display_panel(display_frame)
        
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 标题
        title_label = ttk.Label(parent, text="墙体填充控制", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 墙体组列表
        group_frame = ttk.LabelFrame(parent, text="墙体组列表")
        group_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建Treeview显示墙体组
        columns = ('序号', '状态', '实体数', '识别状态', '操作')
        self.group_tree = ttk.Treeview(group_frame, columns=columns, show='headings', height=12)

        # 设置列标题
        self.group_tree.heading('序号', text='序号')
        self.group_tree.heading('状态', text='填充状态')
        self.group_tree.heading('实体数', text='实体数')
        self.group_tree.heading('识别状态', text='轮廓识别')
        self.group_tree.heading('操作', text='操作')

        # 设置列宽
        self.group_tree.column('序号', width=50)
        self.group_tree.column('状态', width=80)
        self.group_tree.column('实体数', width=60)
        self.group_tree.column('识别状态', width=80)
        self.group_tree.column('操作', width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(group_frame, orient=tk.VERTICAL, command=self.group_tree.yview)
        self.group_tree.configure(yscrollcommand=scrollbar.set)
        
        self.group_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.group_tree.bind('<<TreeviewSelect>>', self._on_group_selected)
        
        # 操作按钮框架
        action_frame = ttk.LabelFrame(parent, text="操作控制")
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 当前墙体组信息
        self.current_group_label = ttk.Label(action_frame, text="当前：未选择", font=("Arial", 10))
        self.current_group_label.pack(fill=tk.X, pady=2)
        
        # 按钮框架
        button_frame = ttk.Frame(action_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        # 第一行按钮
        row1_frame = ttk.Frame(button_frame)
        row1_frame.pack(fill=tk.X, pady=2)
        
        self.mark_cavity_btn = ttk.Button(row1_frame, text="识别为空腔", 
                                         command=self._mark_as_cavity)
        self.mark_cavity_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        
        self.retry_fill_btn = ttk.Button(row1_frame, text="重新填充", 
                                        command=self._retry_fill)
        self.retry_fill_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))
        
        # 保存按钮
        save_frame = ttk.Frame(button_frame)
        save_frame.pack(fill=tk.X, pady=2)
        
        self.save_all_btn = ttk.Button(save_frame, text="保存所有填充", 
                                      command=self._save_all_fills)
        self.save_all_btn.pack(fill=tk.X)
        
        # 退出按钮
        self.exit_btn = ttk.Button(button_frame, text="退出交互式填充", 
                                  command=self._finish_all)
        self.exit_btn.pack(fill=tk.X, pady=2)
        
        # 状态显示
        status_frame = ttk.LabelFrame(parent, text="处理状态")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_text = tk.Text(status_frame, height=8, width=40)
        self.status_text.pack(fill=tk.BOTH, padx=10, pady=5)
        
    def _create_display_panel(self, parent):
        """创建显示面板"""
        # 创建matplotlib图形
        self.fig, self.ax = plt.subplots(figsize=(12, 9))
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 初始显示
        self._draw_entities()
        
    def _identify_and_auto_fill_groups_unified(self):
        """识别所有墙体组并尝试填充（显示所有组，无论是否能识别轮廓）"""
        try:
            self._log_status("开始墙体组识别和填充...")

            # 首先识别所有墙体组（无论是否能填充）
            self._identify_all_wall_groups()

            if not self.wall_groups:
                self._log_status("未找到墙体组")
                messagebox.showwarning("警告", "未找到墙体组")
                return

            self._log_status(f"识别到 {len(self.wall_groups)} 个墙体组，开始尝试填充...")

            # 对每个组尝试填充，使用高级轮廓识别逻辑
            for i, group in enumerate(self.wall_groups):
                self._log_status(f"正在处理墙体组 {i+1}/{len(self.wall_groups)}...")
                self.group_status[i] = '填充中'
                self._update_group_list()
                self.window.update()

                try:
                    # 使用高级轮廓识别逻辑（包含空腔识别）
                    result = self._try_advanced_fill_group(i, group)

                    if result and result.get('fill_polygons'):
                        self.group_status[i] = '已填充'
                        self.group_results[i] = result

                        # 检查是否有空腔
                        cavities = result.get('cavities', [])
                        if cavities:
                            self.group_reasons[i] = f'高级轮廓识别成功（含{len(cavities)}个空腔）'
                            self._log_status(f"  墙体组 {i+1} 填充成功，识别到 {len(cavities)} 个空腔")
                        else:
                            self.group_reasons[i] = '高级轮廓识别成功'
                            self._log_status(f"  墙体组 {i+1} 填充成功")
                    else:
                        self.group_status[i] = '填充失败'
                        self.group_results[i] = None
                        self.group_reasons[i] = '无法识别有效轮廓'
                        self._log_status(f"  墙体组 {i+1} 填充失败")

                except Exception as e:
                    self.group_status[i] = '填充失败'
                    self.group_results[i] = None
                    self.group_reasons[i] = f'填充异常: {str(e)}'
                    self._log_status(f"  墙体组 {i+1} 填充异常: {e}")

            # 更新墙体组列表
            self._update_group_list()

            # 统计结果
            filled_count = sum(1 for status in self.group_status.values() if status == '已填充')
            failed_count = sum(1 for status in self.group_status.values() if status == '填充失败')

            self._log_status(f"填充完成：成功 {filled_count} 个，失败 {failed_count} 个")

        except Exception as e:
            self._log_status(f"墙体组处理失败: {e}")
            import traceback
            traceback.print_exc()

    def _identify_all_wall_groups(self):
        """识别所有墙体组（无论是否能填充）"""
        try:
            self._log_status("识别所有墙体组...")

            # 使用墙体填充处理器识别墙体实体
            wall_entities = self.wall_fill_processor.identify_wall_entities(self.entities)

            if not wall_entities:
                self._log_status("未找到墙体实体")
                return

            self._log_status(f"识别到 {len(wall_entities)} 个墙体实体")

            # 分组墙体实体
            self.wall_groups = self.wall_fill_processor.group_wall_entities(wall_entities)

            if not self.wall_groups:
                self._log_status("未找到有效的墙体组")
                return

            self._log_status(f"分组得到 {len(self.wall_groups)} 个墙体组")

            # 初始化所有组的状态
            for i in range(len(self.wall_groups)):
                self.group_status[i] = '未填充'
                self.group_results[i] = None
                self.group_reasons[i] = '等待处理'

        except Exception as e:
            self._log_status(f"识别墙体组失败: {e}")
            import traceback
            traceback.print_exc()

    def _try_advanced_fill_group(self, group_index, group):
        """使用高级轮廓识别逻辑尝试填充单个墙体组（包含空腔识别）"""
        try:
            # 使用墙体填充处理器的完整填充处理方法（包含空腔识别）
            if hasattr(self.wall_fill_processor, 'create_fill_polygons_enhanced'):
                # 使用完整的填充处理，包含空腔识别
                result = self.wall_fill_processor.create_fill_polygons_enhanced(group, self.entities)
                if result and result.get('fill_polygons'):
                    # 返回完整的结果，包含填充多边形和空腔
                    return result

            # 如果没有完整方法，使用改进的外轮廓识别方法
            if hasattr(self.wall_fill_processor, '_identify_outer_contour_improved'):
                # 使用改进的外轮廓识别方法
                outer_contour = self.wall_fill_processor._identify_outer_contour_improved(group)
                if outer_contour:
                    # 手动识别空腔
                    cavities = []
                    if hasattr(self.wall_fill_processor, '_identify_cavities_from_all_entities'):
                        cavities = self.wall_fill_processor._identify_cavities_from_all_entities(
                            group, outer_contour, self.entities)

                    # 返回包含外轮廓和空腔的结果
                    return {
                        'fill_polygons': [outer_contour],
                        'cavities': cavities
                    }

            # 最后尝试简化的填充逻辑
            simple_result = self._simple_fill_logic_for_group(group)
            if simple_result:
                # 将简单结果包装成标准格式
                return {
                    'fill_polygons': [simple_result],
                    'cavities': []
                }

            return None

        except Exception as e:
            self._log_status(f"墙体组 {group_index+1} 高级填充失败: {e}")
            return None

    def _simple_fill_logic_for_group(self, group):
        """为单个组使用简化的填充逻辑"""
        try:
            # 提取线段
            segments = []
            for entity in group:
                if 'points' in entity and len(entity['points']) >= 2:
                    segments.append(entity['points'])

            if not segments:
                return None

            # 创建LineString对象
            lines = []
            for seg in segments:
                if len(seg) >= 2:
                    line = LineString(seg)
                    lines.append(line)

            if not lines:
                return None

            # 尝试构建多边形
            from shapely.ops import polygonize
            polygons = list(polygonize(lines))

            if polygons:
                # 选择最大的多边形
                polygons.sort(key=lambda p: p.area, reverse=True)
                return polygons[0]

            return None

        except Exception as e:
            return None

    def _identify_and_auto_fill_groups_fallback(self):
        """原有的识别墙体组并自动填充逻辑（作为备用方案）"""
        try:
            self._log_status("使用备用逻辑识别墙体组...")

            # 使用墙体填充处理器识别墙体组
            wall_entities = self.wall_fill_processor.identify_wall_entities(self.entities)

            if not wall_entities:
                self._log_status("未找到墙体实体")
                messagebox.showwarning("警告", "未找到墙体实体")
                return

            # 分组墙体实体
            self.wall_groups = self.wall_fill_processor.group_wall_entities(wall_entities)

            if not self.wall_groups:
                self._log_status("未找到有效的墙体组")
                messagebox.showwarning("警告", "未找到有效的墙体组")
                return

            self._log_status(f"识别到 {len(self.wall_groups)} 个墙体组")

            # 初始化状态
            for i in range(len(self.wall_groups)):
                self.group_status[i] = '未填充'
                self.group_results[i] = None
                self.group_reasons[i] = ''

            # 自动填充所有墙体组
            self._auto_fill_all_groups()

        except Exception as e:
            self._log_status(f"备用逻辑识别墙体组失败: {e}")
            import traceback
            traceback.print_exc()
            # 回退到原有逻辑
            self._identify_and_auto_fill_groups_fallback()
    
    def _update_group_list(self):
        """更新墙体组列表"""
        # 清空现有项目
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)
        
        # 添加墙体组
        for i, group in enumerate(self.wall_groups):
            status = self.group_status.get(i, '未填充')
            entity_count = len(group)

            # 根据状态设置填充状态标签
            status_text = status
            if status == '已填充':
                status_text = '✅ 已填充'
            elif status == '填充中':
                status_text = '🔄 填充中'
            elif status == '空腔':
                status_text = '🕳️ 空腔'
            elif status == '填充失败':
                status_text = '❌ 填充失败'
            else:
                status_text = '⏳ 未填充'

            # 确定轮廓识别状态
            recognition_status = self._get_recognition_status(i, group, status)

            # 插入到树形控件
            item = self.group_tree.insert('', 'end', values=(
                f"{i+1}", status_text, entity_count, recognition_status, "选择"
            ))

            # 根据状态设置操作标签
            if status == '已填充':
                self.group_tree.set(item, '操作', '查看')
            elif status == '填充失败':
                self.group_tree.set(item, '操作', '重试')
            elif status == '空腔':
                self.group_tree.set(item, '操作', '查看')
            else:
                self.group_tree.set(item, '操作', '处理')

    def _get_recognition_status(self, group_index, group, fill_status):
        """获取轮廓识别状态"""
        try:
            if fill_status == '已填充':
                return '✅ 可识别'
            elif fill_status == '空腔':
                return '🕳️ 空腔'
            elif fill_status == '填充失败':
                # 尝试快速检测是否能识别轮廓（不实际填充）
                if self._can_recognize_contour(group):
                    return '⚠️ 部分识别'
                else:
                    return '❌ 无法识别'
            elif fill_status == '填充中':
                return '🔄 识别中'
            else:
                # 未填充状态，尝试快速检测
                if self._can_recognize_contour(group):
                    return '✅ 可识别'
                else:
                    return '❓ 待检测'
        except Exception as e:
            return '❓ 未知'

    def _can_recognize_contour(self, group):
        """快速检测是否能识别轮廓（不实际填充）"""
        try:
            # 提取线段
            segments = []
            for entity in group:
                if 'points' in entity and len(entity['points']) >= 2:
                    segments.append(entity['points'])

            if len(segments) < 3:  # 至少需要3个线段才能形成封闭区域
                return False

            # 检查线段是否能形成基本的连接关系
            from shapely.geometry import LineString
            lines = []
            for seg in segments:
                if len(seg) >= 2:
                    try:
                        line = LineString(seg)
                        lines.append(line)
                    except:
                        continue

            if len(lines) < 3:
                return False

            # 简单检查：是否有足够的连接点
            endpoints = []
            for line in lines:
                endpoints.extend([line.coords[0], line.coords[-1]])

            # 如果端点数量合理，认为可能可以识别
            return len(endpoints) >= 6

        except Exception as e:
            return False

    def _auto_fill_all_groups(self):
        """自动填充所有墙体组"""
        self._log_status("开始自动填充所有墙体组...")
        
        for i, group in enumerate(self.wall_groups):
            self._log_status(f"正在处理墙体组 {i+1}...")
            self.group_status[i] = '填充中'
            self._update_group_list()
            
            try:
                # 尝试自动填充
                result = self._try_auto_fill_group(i, group)
                
                if result:
                    self.group_status[i] = '已填充'
                    self.group_results[i] = result
                    self.group_reasons[i] = '自动填充成功'
                else:
                    self.group_status[i] = '填充失败'
                    self.group_reasons[i] = '无法形成封闭区域'
                    
            except Exception as e:
                self.group_status[i] = '填充失败'
                self.group_reasons[i] = f'填充异常: {str(e)}'
            
            # 更新显示
            self._update_group_list()
            self.window.update()
        
        self._log_status("自动填充完成")
        
        # 统计结果
        filled_count = sum(1 for status in self.group_status.values() if status == '已填充')
        failed_count = sum(1 for status in self.group_status.values() if status == '填充失败')
        
        self._log_status(f"填充结果统计：成功 {filled_count} 个，失败 {failed_count} 个")
    
    def _try_auto_fill_group(self, group_index, group):
        """尝试自动填充单个墙体组"""
        try:
            # 提取线段
            segments = []
            for entity in group:
                if 'points' in entity and len(entity['points']) >= 2:
                    segments.append(entity['points'])
            
            if not segments:
                return None
            
            # 使用墙体填充处理器的方法
            # 这里需要根据实际的wall_fill_processor方法进行调整
            if hasattr(self.wall_fill_processor, 'process_single_wall_group'):
                result = self.wall_fill_processor.process_single_wall_group(segments)
                return result
            else:
                # 使用简化的填充逻辑
                return self._simple_fill_logic(segments)
                
        except Exception as e:
            self._log_status(f"墙体组 {group_index+1} 填充失败: {e}")
            return None
    
    def _simple_fill_logic(self, segments):
        """简化的填充逻辑"""
        try:
            # 创建LineString对象
            lines = []
            for seg in segments:
                if len(seg) >= 2:
                    line = LineString(seg)
                    lines.append(line)
            
            if not lines:
                return None
            
            # 尝试构建多边形
            from shapely.ops import polygonize
            polygons = list(polygonize(lines))
            
            if polygons:
                # 选择最大的多边形
                polygons.sort(key=lambda p: p.area, reverse=True)
                return polygons[0]
            
            return None
            
        except Exception as e:
            return None
    
    def _on_group_selected(self, event):
        """墙体组选择事件"""
        selection = self.group_tree.selection()
        if not selection:
            return
        
        # 获取选中的项目
        item = selection[0]
        values = self.group_tree.item(item, 'values')
        group_index = int(values[0]) - 1  # 转换为0基索引
        
        self.current_group_index = group_index
        self.current_group_label.config(text=f"当前：墙体组 {group_index + 1} ({len(self.wall_groups[group_index])}个实体)")
        
        # 显示当前组的详细信息
        self._show_group_details(group_index)
        
        # 绘制当前组
        self._draw_current_group(group_index)
    
    def _show_group_details(self, group_index):
        """显示墙体组详细信息"""
        status = self.group_status.get(group_index, '未填充')
        reason = self.group_reasons.get(group_index, '')
        
        self._log_status(f"墙体组 {group_index + 1} 详情:")
        self._log_status(f"  状态: {status}")
        if reason:
            self._log_status(f"  原因: {reason}")
        
        if status == '已填充':
            result = self.group_results.get(group_index)
            if result and hasattr(result, 'area'):
                self._log_status(f"  面积: {result.area:.2f}")
    
    def _draw_current_group(self, group_index):
        """绘制当前墙体组"""
        self.ax.clear()
        
        # 绘制所有实体（灰色）
        for entity in self.entities:
            if 'points' in entity and len(entity['points']) >= 2:
                points = np.array(entity['points'])
                self.ax.plot(points[:, 0], points[:, 1], 'gray', linewidth=1, alpha=0.3)
        
        # 显示所有已填充的部分
        for i, group in enumerate(self.wall_groups):
            status = self.group_status.get(i, '未填充')
            if status == '已填充':
                result = self.group_results.get(i)
                if result:
                    # 处理新的结果格式（包含fill_polygons和cavities）
                    if isinstance(result, dict) and 'fill_polygons' in result:
                        # 绘制填充多边形
                        fill_polygons = result.get('fill_polygons', [])
                        for polygon in fill_polygons:
                            if hasattr(polygon, 'exterior'):
                                x, y = polygon.exterior.xy
                                # 当前选中的组用红色填充，其他用绿色填充
                                if i == group_index:
                                    self.ax.fill(x, y, alpha=0.5, color='red')
                                    # 绘制红色轮廓
                                    self.ax.plot(x, y, 'red', linewidth=3)
                                else:
                                    self.ax.fill(x, y, alpha=0.3, color='green')

                        # 绘制空腔（白色填充）
                        cavities = result.get('cavities', [])
                        for cavity in cavities:
                            if hasattr(cavity, 'exterior'):
                                x, y = cavity.exterior.xy
                                # 空腔用白色填充
                                self.ax.fill(x, y, alpha=0.8, color='white')
                                if i == group_index:
                                    # 当前选中的空腔用红色轮廓
                                    self.ax.plot(x, y, 'red', linewidth=2)
                                else:
                                    # 其他空腔用蓝色轮廓
                                    self.ax.plot(x, y, 'blue', linewidth=1)

                    # 兼容旧的结果格式（直接是多边形）
                    elif hasattr(result, 'exterior'):
                        x, y = result.exterior.xy
                        # 当前选中的组用红色填充，其他用绿色填充
                        if i == group_index:
                            self.ax.fill(x, y, alpha=0.5, color='red')
                            # 绘制红色轮廓
                            self.ax.plot(x, y, 'red', linewidth=3)
                        else:
                            self.ax.fill(x, y, alpha=0.3, color='green')
            elif status == '空腔':
                # 空腔用白色填充
                # 尝试创建空腔多边形并填充
                cavity_polygon = self._create_cavity_polygon(group)
                if cavity_polygon and hasattr(cavity_polygon, 'exterior'):
                    x, y = cavity_polygon.exterior.xy
                    if i == group_index:
                        # 当前选中的空腔用红色轮廓
                        self.ax.fill(x, y, alpha=0.8, color='white')
                        self.ax.plot(x, y, 'red', linewidth=3)
                    else:
                        # 其他空腔用白色填充，蓝色轮廓
                        self.ax.fill(x, y, alpha=0.8, color='white')
                        self.ax.plot(x, y, 'blue', linewidth=2)
                else:
                    # 如果无法创建多边形，用线条显示
                    for entity in group:
                        if 'points' in entity and len(entity['points']) >= 2:
                            points = np.array(entity['points'])
                            if i == group_index:
                                self.ax.plot(points[:, 0], points[:, 1], 'red', linewidth=3)
                            else:
                                self.ax.plot(points[:, 0], points[:, 1], 'blue', linewidth=2)
        
        # 高亮显示当前选中的墙体组轮廓
        current_group = self.wall_groups[group_index]
        for entity in current_group:
            if 'points' in entity and len(entity['points']) >= 2:
                points = np.array(entity['points'])
                self.ax.plot(points[:, 0], points[:, 1], 'red', linewidth=3)
        
        status = self.group_status.get(group_index, '未填充')
        self.ax.set_title(f"墙体组 {group_index + 1} - {status} (红色为当前选中)", fontsize=12, fontproperties=self.chinese_font)
        self.ax.set_aspect('equal')
        self.canvas.draw()
    
    def _mark_as_cavity(self):
        """将当前墙体组标记为空腔"""
        if self.current_group_index is None:
            messagebox.showwarning("警告", "请先选择墙体组")
            return
        
        # 直接标记为空腔，不需要确认对话框
        self.group_status[self.current_group_index] = '空腔'
        self.group_results[self.current_group_index] = None
        self.group_reasons[self.current_group_index] = '手动标记为空腔'
        
        # 扣除其他填充区域中与此空腔重叠的部分
        self._subtract_cavity_from_fills(self.current_group_index)
        
        self._update_group_list()
        self._draw_current_group(self.current_group_index)
        self._log_status(f"墙体组 {self.current_group_index + 1} 已标记为空腔")
    
    def _subtract_cavity_from_fills(self, cavity_group_index):
        """从其他填充区域中扣除空腔区域"""
        try:
            # 获取空腔区域
            cavity_group = self.wall_groups[cavity_group_index]
            
            # 创建空腔的多边形
            cavity_polygon = self._create_cavity_polygon(cavity_group)
            if not cavity_polygon:
                return
            
            # 检查所有已填充的区域，扣除与空腔重叠的部分
            for i, group in enumerate(self.wall_groups):
                if i == cavity_group_index:
                    continue  # 跳过空腔本身
                
                status = self.group_status.get(i, '未填充')
                if status == '已填充':
                    result = self.group_results.get(i)
                    if result and hasattr(result, 'intersection'):
                        # 扣除空腔区域
                        try:
                            subtracted_result = result.difference(cavity_polygon)
                            if subtracted_result.is_empty:
                                # 如果扣除后为空，标记为填充失败
                                self.group_status[i] = '填充失败'
                                self.group_results[i] = None
                                self.group_reasons[i] = f'被空腔 {cavity_group_index + 1} 完全扣除'
                            elif subtracted_result.geom_type == 'Polygon':
                                # 如果扣除后仍为单个多边形，更新结果
                                self.group_results[i] = subtracted_result
                                self.group_reasons[i] = f'已扣除空腔 {cavity_group_index + 1} 重叠部分'
                            elif subtracted_result.geom_type == 'MultiPolygon':
                                # 如果扣除后为多个多边形，选择面积最大的
                                largest_poly = max(subtracted_result.geoms, key=lambda p: p.area)
                                self.group_results[i] = largest_poly
                                self.group_reasons[i] = f'已扣除空腔 {cavity_group_index + 1} 重叠部分，保留最大区域'
                            else:
                                # 其他情况，保持原结果
                                self.group_reasons[i] = f'扣除空腔 {cavity_group_index + 1} 时出现异常'
                        except Exception as e:
                            self._log_status(f"扣除空腔时出错: {e}")
            
            self._log_status(f"已从其他填充区域中扣除空腔 {cavity_group_index + 1}")
            
        except Exception as e:
            self._log_status(f"扣除空腔功能失败: {e}")
    
    def _create_cavity_polygon(self, cavity_group):
        """创建空腔的多边形"""
        try:
            # 收集空腔组的所有线段
            segments = []
            for entity in cavity_group:
                if 'points' in entity and len(entity['points']) >= 2:
                    segments.append(entity['points'])
            
            if not segments:
                return None
            
            # 尝试创建空腔多边形
            from shapely.geometry import LineString
            from shapely.ops import polygonize
            
            lines = []
            for seg in segments:
                if len(seg) >= 2:
                    try:
                        line = LineString(seg)
                        lines.append(line)
                    except Exception as e:
                        continue
            
            if not lines:
                return None
            
            # 尝试构建多边形
            polygons = list(polygonize(lines))
            if polygons:
                # 选择最大的多边形作为空腔
                largest_poly = max(polygons, key=lambda p: p.area)
                return largest_poly
            
            # 如果无法构建多边形，不使用凸包算法
            # 严格按照要求：使用_identify_outer_contour_improved的方式
            # 不使用凸包算法
            self._log_status("无法构建空腔多边形，跳过凸包算法")
            return None
            
        except Exception as e:
            self._log_status(f"创建空腔多边形失败: {e}")
            return None
    
    def _retry_fill(self):
        """重新填充当前墙体组"""
        if self.current_group_index is None:
            messagebox.showwarning("警告", "请先选择墙体组")
            return
        
        group_index = self.current_group_index
        group = self.wall_groups[group_index]
        
        self._log_status(f"重新尝试填充墙体组 {group_index + 1}...")
        self.group_status[group_index] = '填充中'
        self._update_group_list()
        
        try:
            # 使用高级轮廓识别逻辑重新填充
            result = self._try_advanced_fill_group(group_index, group)

            if result and result.get('fill_polygons'):
                self.group_status[group_index] = '已填充'
                self.group_results[group_index] = result

                # 检查是否有空腔
                cavities = result.get('cavities', [])
                if cavities:
                    self.group_reasons[group_index] = f'重新填充成功（含{len(cavities)}个空腔）'
                    self._log_status(f"墙体组 {group_index + 1} 重新填充成功，识别到 {len(cavities)} 个空腔")
                else:
                    self.group_reasons[group_index] = '重新填充成功（高级轮廓识别）'
                    self._log_status(f"墙体组 {group_index + 1} 重新填充成功")
            else:
                self.group_status[group_index] = '填充失败'
                self.group_reasons[group_index] = '重新填充失败：无法识别有效轮廓'
                self._log_status(f"墙体组 {group_index + 1} 重新填充失败")

        except Exception as e:
            self.group_status[group_index] = '填充失败'
            self.group_reasons[group_index] = f'重新填充异常: {str(e)}'
            self._log_status(f"墙体组 {group_index + 1} 重新填充异常: {e}")
        
        self._update_group_list()
        self._draw_current_group(group_index)
    

    
    def _save_all_fills(self):
        """保存所有填充到主程序预览窗口"""
        try:
            # 收集所有填充结果
            filled_groups = []
            for i, group in enumerate(self.wall_groups):
                status = self.group_status.get(i, '未填充')
                if status == '已填充':
                    result = self.group_results.get(i)
                    if result:
                        filled_groups.append({
                            'wall_group': group,
                            'fill_polygons': [result],
                            'cavities': []
                        })
                elif status == '空腔':
                    filled_groups.append({
                        'wall_group': group,
                        'fill_polygons': [],
                        'cavities': [group]  # 标记为空腔
                    })
            
            # 统计信息
            filled_count = sum(1 for status in self.group_status.values() if status == '已填充')
            cavity_count = sum(1 for status in self.group_status.values() if status == '空腔')
            failed_count = sum(1 for status in self.group_status.values() if status == '填充失败')
            
            # 尝试将结果传递给主程序
            try:
                # 检查parent是否是主程序对象
                if hasattr(self.parent, 'current_wall_fills'):
                    # 保存到主程序
                    self.parent.current_wall_fills = filled_groups
                    self.parent.current_wall_fill_processor = self.wall_fill_processor
                    
                    # 更新主程序的预览窗口
                    if hasattr(self.parent, 'visualizer') and self.parent.visualizer:
                        try:
                            self.parent._update_visualization_with_fills_v2(filled_groups)
                            messagebox.showinfo("保存成功", 
                                f"已保存 {len(filled_groups)} 个墙体组的填充结果到主程序预览窗口\n\n"
                                f"填充统计：\n"
                                f"成功填充: {filled_count} 个\n"
                                f"标记空腔: {cavity_count} 个\n"
                                f"填充失败: {failed_count} 个")
                            
                            self._log_status(f"已保存 {len(filled_groups)} 个墙体组的填充结果到主程序")
                        except Exception as viz_error:
                            # 如果可视化更新失败，但数据已保存
                            messagebox.showinfo("保存完成", 
                                f"填充数据已保存到主程序\n\n"
                                f"填充统计：\n"
                                f"成功填充: {filled_count} 个\n"
                                f"标记空腔: {cavity_count} 个\n"
                                f"填充失败: {failed_count} 个\n\n"
                                f"注意：预览窗口更新失败，但数据已保存")
                            
                            self._log_status(f"数据已保存，但预览窗口更新失败: {viz_error}")
                    else:
                        # 没有可视化器，但数据已保存
                        messagebox.showinfo("保存完成", 
                            f"填充数据已保存到主程序\n\n"
                            f"填充统计：\n"
                            f"成功填充: {filled_count} 个\n"
                            f"标记空腔: {cavity_count} 个\n"
                            f"填充失败: {failed_count} 个\n\n"
                            f"注意：主程序没有可视化器，无法更新预览窗口")
                        
                        self._log_status(f"数据已保存到主程序，但无可视化器")
                else:
                    # 如果无法访问主程序，至少显示统计信息
                    messagebox.showinfo("保存完成", 
                        f"填充统计：\n"
                        f"成功填充: {filled_count} 个\n"
                        f"标记空腔: {cavity_count} 个\n"
                        f"填充失败: {failed_count} 个\n\n"
                        f"注意：无法访问主程序，填充结果未保存到预览窗口")
                    
                    self._log_status(f"填充完成，但无法保存到主程序")
                    
            except Exception as e:
                # 如果更新预览窗口失败，至少显示统计信息
                messagebox.showinfo("保存完成", 
                    f"填充统计：\n"
                    f"成功填充: {filled_count} 个\n"
                    f"标记空腔: {cavity_count} 个\n"
                    f"填充失败: {failed_count} 个\n\n"
                    f"注意：更新预览窗口失败，但填充结果已计算完成")
                
                self._log_status(f"填充完成，但更新预览窗口失败: {e}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存填充失败: {e}")
            self._log_status(f"保存填充失败: {e}")
    

    
    def _draw_entities(self):
        """绘制原始实体"""
        self.ax.clear()
        
        # 绘制所有实体
        for entity in self.entities:
            if 'points' in entity and len(entity['points']) >= 2:
                points = np.array(entity['points'])
                self.ax.plot(points[:, 0], points[:, 1], 'b-', linewidth=1, alpha=0.3)
        
        self.ax.set_title("原始CAD实体", fontsize=12, fontproperties=self.chinese_font)
        self.ax.set_aspect('equal')
        self.canvas.draw()
    
    def _log_status(self, message):
        """记录状态信息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.window.update()
    
    def _finish_all(self):
        """退出交互式填充"""
        # 首先询问是否要退出
        exit_result = messagebox.askyesno("确认退出", "确定要退出交互式填充吗？")
        if not exit_result:
            # 取消退出确认对话框后，直接跳转到保存填充确认对话框
            self._show_save_confirmation_dialog()
            return
        
        # 统计填充结果
        filled_count = sum(1 for status in self.group_status.values() if status == '已填充')
        cavity_count = sum(1 for status in self.group_status.values() if status == '空腔')
        failed_count = sum(1 for status in self.group_status.values() if status == '填充失败')
        
        # 如果有填充结果，询问是否保存
        if filled_count > 0 or cavity_count > 0:
            save_result = messagebox.askyesno("保存填充", 
                f"是否保存填充结果？\n\n"
                f"填充统计：\n"
                f"成功填充: {filled_count} 个\n"
                f"标记空腔: {cavity_count} 个\n"
                f"填充失败: {failed_count} 个\n\n"
                f"选择\"是\"：保存填充并更新左侧预览窗口\n"
                f"选择\"否\"：不保存填充，返回实体组类型标注界面")
            
            if save_result:
                # 保存填充结果
                self._save_all_fills()
            else:
                # 不保存，直接退出
                messagebox.showinfo("退出", "已退出交互式填充，未保存填充结果")
        else:
            # 没有填充结果，直接退出
            messagebox.showinfo("退出", "没有填充结果，已退出交互式填充")
        
        # 关闭窗口
        self.window.destroy()
    
    def _show_save_confirmation_dialog(self):
        """显示保存填充确认对话框（包含取消退出选项）"""
        # 统计填充结果
        filled_count = sum(1 for status in self.group_status.values() if status == '已填充')
        cavity_count = sum(1 for status in self.group_status.values() if status == '空腔')
        failed_count = sum(1 for status in self.group_status.values() if status == '填充失败')
        
        # 创建自定义对话框
        dialog = tk.Toplevel(self.window)
        dialog.title("保存填充确认")
        dialog.geometry("500x400")
        dialog.transient(self.window)  # 设置为主窗口的临时窗口
        dialog.grab_set()  # 模态对话框
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"500x400+{x}+{y}")
        
        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="保存填充结果", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 统计信息
        stats_frame = ttk.LabelFrame(main_frame, text="填充统计")
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        stats_text = f"""填充统计：
成功填充: {filled_count} 个
标记空腔: {cavity_count} 个
填充失败: {failed_count} 个"""
        
        stats_label = ttk.Label(stats_frame, text=stats_text, justify=tk.LEFT)
        stats_label.pack(padx=10, pady=10)
        
        # 说明文本
        if filled_count > 0 or cavity_count > 0:
            desc_text = """请选择操作：
• 保存填充：保存填充结果并更新主程序预览窗口
• 不保存退出：不保存填充结果，直接退出交互式填充
• 取消退出：返回交互式填充界面继续操作"""
        else:
            desc_text = """没有填充结果，请选择操作：
• 退出：直接退出交互式填充
• 取消退出：返回交互式填充界面继续操作"""
        
        desc_label = ttk.Label(main_frame, text=desc_text, justify=tk.LEFT)
        desc_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮变量
        result_var = tk.StringVar()
        
        def on_button_click(action):
            result_var.set(action)
            dialog.destroy()
        
        # 按钮
        if filled_count > 0 or cavity_count > 0:
            # 有填充结果时的按钮
            save_btn = ttk.Button(button_frame, text="保存填充", 
                                 command=lambda: on_button_click("save"))
            save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
            
            no_save_btn = ttk.Button(button_frame, text="不保存退出", 
                                    command=lambda: on_button_click("no_save"))
            no_save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            
            cancel_btn = ttk.Button(button_frame, text="取消退出", 
                                   command=lambda: on_button_click("cancel"))
            cancel_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        else:
            # 没有填充结果时的按钮
            exit_btn = ttk.Button(button_frame, text="退出", 
                                 command=lambda: on_button_click("exit"))
            exit_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
            
            cancel_btn = ttk.Button(button_frame, text="取消退出", 
                                   command=lambda: on_button_click("cancel"))
            cancel_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 等待对话框关闭
        dialog.wait_window()
        
        # 处理结果
        action = result_var.get()
        
        if action == "save":
            # 保存填充结果
            self._save_all_fills()
            messagebox.showinfo("退出", "已保存填充结果并退出交互式填充")
            self.window.destroy()
        elif action == "no_save":
            # 不保存，直接退出
            messagebox.showinfo("退出", "已退出交互式填充，未保存填充结果")
            self.window.destroy()
        elif action == "exit":
            # 直接退出（没有填充结果的情况）
            messagebox.showinfo("退出", "已退出交互式填充")
            self.window.destroy()
        elif action == "cancel":
            # 取消退出，返回交互式填充界面
            self._log_status("用户取消退出，返回交互式填充界面")
            return
        else:
            # 对话框被关闭，相当于取消退出
            self._log_status("用户取消退出，返回交互式填充界面")
            return
    
 