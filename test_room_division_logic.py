#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试初步房间划分逻辑
验证门窗与墙体交点检测、临时线条生成、区域分类等功能
"""

import tkinter as tk
import numpy as np
from shapely.geometry import LineString, Point

def test_room_division_logic():
    """测试房间划分逻辑"""
    print("🚀 开始测试初步房间划分逻辑...")
    print("="*80)
    
    try:
        # 导入房间识别处理器
        from room_recognition_processor import RoomRecognitionProcessor
        
        # 创建处理器实例
        processor = RoomRecognitionProcessor()
        print("✅ 房间识别处理器创建成功")
        
        # 创建模拟墙体组（CAD实体格式）
        print("\n🧱 创建模拟墙体组...")
        wall_entities = [
            # 外墙 - 形成一个矩形房间
            {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 3000, 'y': 0}},      # 底边
            {'type': 'LINE', 'start': {'x': 3000, 'y': 0}, 'end': {'x': 3000, 'y': 2000}}, # 右边
            {'type': 'LINE', 'start': {'x': 3000, 'y': 2000}, 'end': {'x': 0, 'y': 2000}}, # 顶边
            {'type': 'LINE', 'start': {'x': 0, 'y': 2000}, 'end': {'x': 0, 'y': 0}},       # 左边

            # 内墙 - 分隔房间
            {'type': 'LINE', 'start': {'x': 1000, 'y': 0}, 'end': {'x': 1000, 'y': 1200}}, # 竖墙1
            {'type': 'LINE', 'start': {'x': 1000, 'y': 1400}, 'end': {'x': 1000, 'y': 2000}}, # 竖墙2
            {'type': 'LINE', 'start': {'x': 2000, 'y': 0}, 'end': {'x': 2000, 'y': 800}},  # 竖墙3
            {'type': 'LINE', 'start': {'x': 2000, 'y': 1000}, 'end': {'x': 2000, 'y': 2000}}, # 竖墙4
            {'type': 'LINE', 'start': {'x': 0, 'y': 1000}, 'end': {'x': 800, 'y': 1000}},  # 横墙1
            {'type': 'LINE', 'start': {'x': 1200, 'y': 1000}, 'end': {'x': 3000, 'y': 1000}}, # 横墙2
        ]
        
        processor.wall_groups = [wall_entities]
        print(f"✅ 创建墙体组: {len(wall_entities)} 个墙体实体")
        
        # 创建模拟门窗组（CAD实体格式）
        print("\n🚪 创建模拟门窗组...")
        door_window_entities = [
            # 门窗1 - 在竖墙1上
            {'type': 'LINE', 'start': {'x': 1000, 'y': 1200}, 'end': {'x': 1000, 'y': 1400}},

            # 门窗2 - 在竖墙3上
            {'type': 'LINE', 'start': {'x': 2000, 'y': 800}, 'end': {'x': 2000, 'y': 1000}},

            # 门窗3 - 在横墙1上
            {'type': 'LINE', 'start': {'x': 800, 'y': 1000}, 'end': {'x': 1200, 'y': 1000}},
        ]
        
        processor.door_window_groups = [door_window_entities]
        print(f"✅ 创建门窗组: {len(door_window_entities)} 个门窗实体")
        
        # 测试步骤1: 门窗与墙体交点检测
        print("\n📊 测试步骤1: 门窗与墙体交点检测...")
        temp_lines = processor._detect_door_window_wall_intersections()
        
        if temp_lines:
            print(f"✅ 生成临时线条: {len(temp_lines)} 条")
            for i, line in enumerate(temp_lines):
                coords = list(line.coords)
                print(f"   临时线条 {i+1}: {coords[0]} -> {coords[1]}")
        else:
            print("⚠️ 未生成临时线条")
        
        # 测试步骤2: 收集所有线段
        print("\n📊 测试步骤2: 收集所有线段...")
        all_line_geometries = processor._collect_all_line_geometries(temp_lines)
        
        if all_line_geometries:
            print(f"✅ 收集线段: {len(all_line_geometries)} 条")
        else:
            print("❌ 未收集到线段")
            return False
        
        # 测试步骤3: 生成围合区域
        print("\n📊 测试步骤3: 生成围合区域...")
        from shapely.ops import polygonize
        all_polygons = list(polygonize(all_line_geometries))
        
        if all_polygons:
            print(f"✅ 生成围合区域: {len(all_polygons)} 个")
            for i, polygon in enumerate(all_polygons):
                print(f"   区域 {i+1}: 面积 {polygon.area:.1f}")
        else:
            print("❌ 未生成围合区域")
            return False
        
        # 测试步骤4: 区域分类
        print("\n📊 测试步骤4: 区域分类...")
        rooms = processor._classify_regions(all_polygons)
        
        if rooms:
            print(f"✅ 房间分类完成: {len(rooms)} 个房间")
            
            print("\n📋 房间分类详情:")
            for i, room in enumerate(rooms):
                room_type = room['type']
                area = room['area']
                width = room.get('width', 0)
                print(f"   房间 {i+1}: {room_type}")
                print(f"      面积: {area:.1f}")
                print(f"      宽度: {width:.1f}")
                print()
        else:
            print("❌ 房间分类失败")
            return False
        
        # 测试完整的房间识别流程
        print("\n📊 测试完整房间识别流程...")
        processor.rooms = []  # 清空之前的结果
        
        # 设置数据并运行识别
        identified_rooms = processor._identify_rooms()
        
        if identified_rooms:
            print(f"✅ 完整流程识别房间: {len(identified_rooms)} 个")
            
            # 统计房间类型
            type_counts = {}
            for room in identified_rooms:
                room_type = room['type']
                type_counts[room_type] = type_counts.get(room_type, 0) + 1
            
            print("\n📊 房间类型统计:")
            for room_type, count in type_counts.items():
                print(f"   {room_type}: {count} 个")
            
            # 验证分类规则
            print("\n🔍 验证分类规则:")
            
            # 检查是否有客厅（面积最大）
            largest_room = max(identified_rooms, key=lambda r: r['area'])
            if largest_room['type'] == '客厅':
                print("   ✅ 面积最大的房间已标记为客厅")
            else:
                print(f"   ⚠️ 面积最大的房间类型: {largest_room['type']}")
            
            # 检查宽度分类
            for room in identified_rooms:
                width = room.get('width', 0)
                room_type = room['type']
                
                if room_type == '设备平台':
                    if 400 <= width < 1100:
                        print(f"   ✅ 设备平台宽度正确: {width:.0f}")
                    else:
                        print(f"   ❌ 设备平台宽度错误: {width:.0f}")
                
                elif room_type == '阳台':
                    if 1100 <= width < 1700:
                        print(f"   ✅ 阳台宽度正确: {width:.0f}")
                    else:
                        print(f"   ❌ 阳台宽度错误: {width:.0f}")
                
                elif room_type == '卫生间':
                    if 1700 <= width < 2500:
                        print(f"   ✅ 卫生间宽度正确: {width:.0f}")
                    else:
                        print(f"   ❌ 卫生间宽度错误: {width:.0f}")
        
        # 测试结果总结
        print("\n" + "="*80)
        print("📊 房间划分逻辑测试结果:")
        
        success_count = 0
        total_tests = 4
        
        if temp_lines is not None:
            print("   ✅ 步骤1 - 门窗与墙体交点检测: 通过")
            success_count += 1
        else:
            print("   ❌ 步骤1 - 门窗与墙体交点检测: 失败")
        
        if all_line_geometries:
            print("   ✅ 步骤2 - 线段收集: 通过")
            success_count += 1
        else:
            print("   ❌ 步骤2 - 线段收集: 失败")
        
        if all_polygons:
            print("   ✅ 步骤3 - 围合区域生成: 通过")
            success_count += 1
        else:
            print("   ❌ 步骤3 - 围合区域生成: 失败")
        
        if identified_rooms:
            print("   ✅ 步骤4 - 区域分类: 通过")
            success_count += 1
        else:
            print("   ❌ 步骤4 - 区域分类: 失败")
        
        print(f"\n🎯 测试通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
        
        if success_count == total_tests:
            print("🎉 房间划分逻辑测试全部通过！")
            return True
        else:
            print("⚠️ 部分测试未通过，需要进一步调试")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 初步房间划分逻辑测试")
    print("="*80)
    
    success = test_room_division_logic()
    
    print("\n" + "="*80)
    print("📊 测试总结:")
    
    if success:
        print("🎉 房间划分逻辑测试成功！")
        print("\n💡 实现的功能:")
        print("   1. ✅ 门窗与墙体交点检测（容差20）")
        print("   2. ✅ 水平/垂直线条连接（距离>300，容差10）")
        print("   3. ✅ 大距离门窗组线条复制（距离>=400）")
        print("   4. ✅ 墙体实体和临时线条围合区域检测")
        print("   5. ✅ 宽度<400区域合并为墙体空腔")
        print("   6. ✅ 其他区域按宽度分类（设备平台、阳台、卫生间、卧室）")
        print("   7. ✅ 面积最大区域标记为客厅")
        
        print("\n🎯 分类规则:")
        print("   - 宽度 400-1100: 设备平台")
        print("   - 宽度 1100-1700: 阳台")
        print("   - 宽度 1700-2500: 卫生间")
        print("   - 其他宽度: 卧室")
        print("   - 面积最大: 客厅")
        print("   - 宽度<400: 墙体空腔")
    else:
        print("❌ 房间划分逻辑测试失败")
    
    return success

if __name__ == "__main__":
    main()
