#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别模块集成验证脚本
验证房间识别模块是否已成功集成到主应用中
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_imports():
    """验证模块导入"""
    print("🔍 验证模块导入...")
    
    try:
        # 验证房间识别处理器
        from room_recognition_processor import RoomRecognitionProcessor
        print("✅ RoomRecognitionProcessor 导入成功")
        
        # 验证房间识别UI
        from room_recognition_ui import RoomRecognitionUI
        print("✅ RoomRecognitionUI 导入成功")
        
        # 验证主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("✅ EnhancedCADAppV2 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def verify_processor_functionality():
    """验证处理器功能"""
    print("\n🏠 验证房间识别处理器功能...")
    
    try:
        from room_recognition_processor import RoomRecognitionProcessor
        
        processor = RoomRecognitionProcessor()
        
        # 验证房间类型
        expected_types = ['客厅', '卧室', '阳台', '厨房', '卫生间', '杂物间', '其他房间', '设备平台']
        if processor.room_types == expected_types:
            print("✅ 房间类型配置正确")
        else:
            print(f"❌ 房间类型配置错误: {processor.room_types}")
            return False
        
        # 验证颜色配置
        if len(processor.room_colors) == len(expected_types):
            print("✅ 房间颜色配置正确")
        else:
            print(f"❌ 房间颜色配置错误: {len(processor.room_colors)} vs {len(expected_types)}")
            return False
        
        # 验证基本方法
        methods = [
            'process_room_recognition',
            'get_room_statistics', 
            'update_room_type',
            'export_room_data'
        ]
        
        for method in methods:
            if hasattr(processor, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器功能验证失败: {e}")
        return False

def verify_ui_functionality():
    """验证UI功能"""
    print("\n🖼️ 验证房间识别UI功能...")
    
    try:
        from room_recognition_processor import RoomRecognitionProcessor
        from room_recognition_ui import RoomRecognitionUI
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        processor = RoomRecognitionProcessor()
        
        # 创建测试框架
        test_frame = tk.Frame(root)
        
        # 创建UI
        ui = RoomRecognitionUI(test_frame, processor)
        
        # 验证UI组件
        if hasattr(ui, 'main_frame'):
            print("✅ UI主框架创建成功")
        else:
            print("❌ UI主框架创建失败")
            return False
        
        # 验证方法
        ui_methods = [
            'update_with_new_data',
            'set_data_callback'
        ]
        
        for method in ui_methods:
            if hasattr(ui, method):
                print(f"✅ UI {method} 方法存在")
            else:
                print(f"❌ UI {method} 方法缺失")
                return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI功能验证失败: {e}")
        return False

def verify_main_app_integration():
    """验证主应用集成"""
    print("\n🔗 验证主应用集成...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 验证房间识别相关属性和方法
        integration_items = [
            # 初始化方法
            '_init_room_recognition',
            # UI创建方法
            '_create_room_recognition_panel',
            # 数据获取方法
            '_get_wall_door_data',
            # 实体分组方法
            '_group_entities_by_connectivity',
            '_find_connected_entities_simple',
            '_get_entity_coordinates',
            '_are_entities_connected',
            # 回调方法
            '_on_room_update'
        ]
        
        for item in integration_items:
            if hasattr(EnhancedCADAppV2, item):
                print(f"✅ {item} 已集成")
            else:
                print(f"❌ {item} 未集成")
                return False
        
        # 验证导入语句
        import inspect
        source = inspect.getsource(EnhancedCADAppV2)
        
        if 'RoomRecognitionProcessor' in source:
            print("✅ RoomRecognitionProcessor 已导入主应用")
        else:
            print("❌ RoomRecognitionProcessor 未导入主应用")
            return False
        
        if 'RoomRecognitionUI' in source:
            print("✅ RoomRecognitionUI 已导入主应用")
        else:
            print("❌ RoomRecognitionUI 未导入主应用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用集成验证失败: {e}")
        return False

def verify_layout_integration():
    """验证布局集成"""
    print("\n📐 验证布局集成...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import inspect
        
        # 检查概览视图创建方法
        source = inspect.getsource(EnhancedCADAppV2._create_overview_view)
        
        if '_create_room_recognition_panel' in source:
            print("✅ 房间识别面板已添加到布局")
        else:
            print("❌ 房间识别面板未添加到布局")
            return False
        
        if 'bottom_container' in source:
            print("✅ 底部容器布局正确")
        else:
            print("❌ 底部容器布局错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 布局集成验证失败: {e}")
        return False

def run_functional_test():
    """运行功能测试"""
    print("\n🧪 运行功能测试...")
    
    try:
        from room_recognition_processor import RoomRecognitionProcessor
        
        processor = RoomRecognitionProcessor()
        
        # 创建测试数据
        test_wall_groups = [
            [
                {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 100, 'y': 0}},
                {'type': 'LINE', 'start': {'x': 100, 'y': 0}, 'end': {'x': 100, 'y': 100}},
                {'type': 'LINE', 'start': {'x': 100, 'y': 100}, 'end': {'x': 0, 'y': 100}},
                {'type': 'LINE', 'start': {'x': 0, 'y': 100}, 'end': {'x': 0, 'y': 0}}
            ]
        ]
        
        test_door_groups = []
        
        # 执行房间识别
        result = processor.process_room_recognition(test_wall_groups, test_door_groups)
        
        if result:
            print("✅ 房间识别处理成功")
            print(f"   建筑外轮廓: {'已识别' if result['building_outline'] else '未识别'}")
            print(f"   房间数量: {len(result['rooms'])}")
        else:
            print("⚠️ 房间识别返回空结果")
        
        # 测试统计功能
        stats = processor.get_room_statistics()
        print(f"✅ 统计功能正常: {len(stats)} 项统计")
        
        # 测试导出功能
        export_data = processor.export_room_data()
        if export_data:
            print("✅ 导出功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始房间识别模块集成验证...")
    print("=" * 80)
    
    # 执行所有验证
    verifications = [
        ("模块导入", verify_imports),
        ("处理器功能", verify_processor_functionality),
        ("UI功能", verify_ui_functionality),
        ("主应用集成", verify_main_app_integration),
        ("布局集成", verify_layout_integration),
        ("功能测试", run_functional_test)
    ]
    
    all_passed = True
    results = []
    
    for name, verify_func in verifications:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            result = verify_func()
            results.append((name, result))
            if result:
                print(f"✅ {name} 验证通过")
            else:
                print(f"❌ {name} 验证失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {name} 验证异常: {e}")
            results.append((name, False))
            all_passed = False
    
    # 输出最终结果
    print("\n" + "=" * 80)
    print("📊 验证结果汇总:")
    print("=" * 80)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:20} : {status}")
    
    print("=" * 80)
    
    if all_passed:
        print("🎉 房间识别模块集成验证全部通过！")
        print("\n📋 集成完成的功能:")
        print("1. ✅ 房间识别处理器 - 完整的房间识别逻辑")
        print("2. ✅ 房间识别UI - 完整的用户界面")
        print("3. ✅ 主应用集成 - 已集成到主CAD应用")
        print("4. ✅ 布局集成 - 在索引图上方添加房间识别模块")
        print("5. ✅ 数据流集成 - 与现有墙体和门窗识别系统连接")
        print("6. ✅ 功能验证 - 所有核心功能正常工作")
        
        print("\n🎯 使用方法:")
        print("- 启动主应用后，房间识别模块将显示在索引图上方")
        print("- 依据已识别的墙体组和门窗栏杆组进行房间识别")
        print("- 支持建筑外轮廓识别和房间切分功能")
        print("- 可对每个房间的类型进行修改")
        
    else:
        print("❌ 部分验证失败，请检查实现")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
