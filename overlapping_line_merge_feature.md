# 门窗图层重叠线条合并功能

## 🎯 功能概述

在DXF处理过程中，增加了一个重叠线条合并步骤。如果门窗图层上有完全重叠（位置、长度一致）的线条，系统会自动对这些重叠线条进行合并，然后再进行后续的图层识别和自动分组等过程。

## 🔧 技术实现

### 核心组件

#### 1. OverlappingLineMerger 类
- **文件**: `overlapping_line_merger.py`
- **功能**: 专门处理门窗图层重叠线条的合并
- **特点**: 
  - 支持多种门窗图层模式识别
  - 精确的重叠检测算法
  - 智能的代表线条选择策略

#### 2. CADDataProcessor 集成
- **文件**: `cad_data_processor.py`
- **集成位置**: DXF文件加载流程中
- **执行时机**: 在图层识别和自动分组之前

### 处理流程

```
DXF文件加载
    ↓
线型-图层映射建立
    ↓
实体数据提取
    ↓
🆕 重叠线条合并 ← 新增步骤
    ↓
图层识别
    ↓
自动分组
    ↓
后续处理...
```

## 📋 功能特性

### 1. 门窗图层识别
支持多种门窗图层命名模式：

**中文模式**:
- 门、窗、门窗、门洞、窗洞、开口、洞口
- 门套、窗套、入口、出口、通道口
- 门扇、窗扇

**英文模式**:
- door、doors、window、windows
- opening、openings、entrance、exit
- portal、aperture、casement、sash、frame、jamb

**CAD标准命名**:
- A-DOOR、ADOOR、A_DOOR、DOOR_、_DOOR
- A-WINDOW、AWINDOW、A_WINDOW、WINDOW_、_WINDOW
- A-OPEN、AOPEN、OPENING、OPENINGS

**图层编号模式**:
- 01-门、02-窗、L-门、L-窗
- A-门、A-窗、AR-门、AR-窗

### 2. 重叠检测算法
- **位置检测**: 起点和终点坐标完全一致（在容差范围内）
- **方向兼容**: 支持正向和反向重叠线条的检测
- **容差设置**: 默认0.1单位的位置容差，可配置

### 3. 代表线条选择策略
当发现多条重叠线条时，系统会选择最优的代表线条：

**选择优先级**:
1. 有非默认颜色信息的线条 (+2分)
2. 有非默认线型信息的线条 (+2分)
3. 有虚线标记的线条 (+1分)
4. 有更多属性信息的线条 (+1-5分)
5. 基础存在分数 (+1分)

**示例**:
```python
线条A: 颜色=256(BYLAYER), 线型=BYLAYER → 得分=1
线条B: 颜色=2, 线型=DASHED, is_dashed=True → 得分=6
结果: 选择线条B作为代表线条
```

## 🧪 测试验证

### 测试用例
1. **基本重叠检测**: 完全相同的线条合并
2. **方向兼容性**: 正向和反向线条合并
3. **图层隔离**: 只处理门窗图层，不影响其他图层
4. **属性保留**: 选择最优代表线条保留重要属性
5. **集成测试**: 与完整DXF处理流程的兼容性

### 测试结果
```
🧪 测试重叠线条合并器...
原始实体数量: 5
🔧 开始重叠线条合并处理...
   门窗图层实体: 4 个
   其他图层实体: 1 个
   处理 4 个LINE实体...
   处理图层 'A-DOOR': 3 个线条
     图层 'A-DOOR' 合并了 2 个重叠线条
✅ 重叠线条合并完成: 5 → 3 个实体 (合并了 2 个重叠线条)
```

## 📊 性能优化

### 算法复杂度
- **时间复杂度**: O(n²) 其中n为单个图层的线条数量
- **空间复杂度**: O(n) 用于存储处理结果
- **优化策略**: 按图层分组处理，减少不必要的比较

### 处理效率
- **图层过滤**: 只处理门窗相关图层
- **类型过滤**: 只处理LINE类型实体
- **早期退出**: 单个图层线条数≤1时跳过处理

## 🎯 使用效果

### 处理前
```
门窗图层包含重叠线条:
- LINE1: (0,0) → (100,0), 颜色=1, 线型=CONTINUOUS
- LINE2: (0,0) → (100,0), 颜色=256, 线型=BYLAYER  ← 重叠
- LINE3: (100,0) → (0,0), 颜色=2, 线型=DASHED    ← 重叠(反向)
```

### 处理后
```
门窗图层合并后:
- LINE_MERGED: (0,0) → (100,0), 颜色=2, 线型=DASHED
  (选择了属性最丰富的线条作为代表)
```

### 实际效果
- ✅ **减少冗余**: 消除重复的线条数据
- ✅ **提高精度**: 避免重叠线条影响分组算法
- ✅ **保留信息**: 选择最优线条保留重要属性
- ✅ **性能提升**: 减少后续处理的数据量

## 🔄 集成流程

### 启用条件
```python
# 自动检测并启用
if OVERLAPPING_MERGER_AVAILABLE:
    self.overlapping_merger = OverlappingLineMerger()
    print("✅ 重叠线条合并器已启用")
```

### 调用时机
```python
# 在DXF文件加载过程中
def load_dxf_file(self, file_path):
    # ... 实体提取 ...
    
    # 🔧 步骤3：重叠线条合并处理
    if self.overlapping_merger:
        all_entities = self.overlapping_merger.process_entities(all_entities)
    
    return all_entities
```

## 📝 配置选项

### 容差设置
```python
# 创建合并器时可配置位置容差
merger = OverlappingLineMerger(tolerance=0.1)  # 默认0.1单位
```

### 图层模式扩展
```python
# 可以扩展门窗图层识别模式
merger.door_window_layer_patterns.extend([
    'custom_door_layer',
    'special_window_pattern'
])
```

## 🎉 总结

重叠线条合并功能成功集成到DXF处理流程中，实现了：

1. **自动化处理**: 无需手动干预，自动检测和合并重叠线条
2. **智能选择**: 基于多维度评分选择最优代表线条
3. **精确识别**: 支持多种门窗图层命名模式
4. **性能优化**: 高效的算法和处理策略
5. **完整集成**: 与现有DXF处理流程无缝集成

这个功能将显著提高门窗图层数据的质量，为后续的图层识别和自动分组提供更准确的基础数据。
