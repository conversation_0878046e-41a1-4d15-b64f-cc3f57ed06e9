#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试圆弧分组修复
"""

import sys
import os
import math

def test_arc_grouping_without_center():
    """测试圆弧分组不使用圆心"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        print("测试圆弧分组不使用圆心:")
        
        # 创建测试场景：圆弧和直线应该分组，但圆心距离很远
        test_entities = [
            # 圆弧（圆心在(100, 100)，但端点接触直线）
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 50,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'test'
            },
            # 直线（从圆弧端点延伸，但距离圆心很远）
            {
                'type': 'LINE',
                'points': [(150, 100), (300, 100)],  # 从圆弧右端点延伸
                'layer': 'test'
            },
            # 另一个远离的直线组
            {
                'type': 'LINE',
                'points': [(500, 500), (600, 500)],  # 距离圆心和圆弧都很远
                'layer': 'test'
            },
            {
                'type': 'LINE',
                'points': [(600, 500), (600, 600)],
                'layer': 'test'
            }
        ]
        
        print(f"测试实体数量: {len(test_entities)}")
        
        # 计算圆弧端点
        arc = test_entities[0]
        arc_end_x = arc['center'][0] + arc['radius'] * math.cos(math.radians(0))  # 150
        arc_end_y = arc['center'][1] + arc['radius'] * math.sin(math.radians(0))  # 100
        
        print(f"圆弧端点: ({arc_end_x}, {arc_end_y})")
        print(f"直线起点: {test_entities[1]['points'][0]}")
        print(f"圆心到远离直线组的距离: {math.sqrt((100-500)**2 + (100-500)**2):.1f}")
        
        # 运行分组算法
        groups = processor.group_other_entities(test_entities)
        
        print(f"\n分组结果: {len(groups)} 个组")
        
        # 分析分组结果
        arc_group = None
        far_line_group = None
        
        for i, group in enumerate(groups):
            print(f"组 {i+1}: {len(group)} 个实体")
            has_arc = any(e['type'] == 'ARC' for e in group)
            has_near_line = any(e['type'] == 'LINE' and e['points'][0][0] < 400 for e in group)
            has_far_line = any(e['type'] == 'LINE' and e['points'][0][0] >= 400 for e in group)
            
            print(f"  包含圆弧: {has_arc}")
            print(f"  包含近距离直线: {has_near_line}")
            print(f"  包含远距离直线: {has_far_line}")
            
            if has_arc:
                arc_group = i
            if has_far_line and not has_arc:
                far_line_group = i
        
        # 验证分组结果
        if arc_group is not None and far_line_group is not None:
            print("✓ 圆弧和远离直线被正确分为不同组")
            return True
        elif len(groups) == 1:
            print("✗ 所有实体被分为一组，可能仍在使用圆心距离")
            return False
        else:
            print("✓ 分组结果合理")
            return True
        
    except Exception as e:
        print(f"✗ 圆弧分组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arc_distance_calculation():
    """测试圆弧距离计算"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        print("测试圆弧距离计算:")
        
        # 创建圆弧和直线
        arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90
        }
        
        # 接触圆弧端点的直线
        near_line = {
            'type': 'LINE',
            'points': [(150, 100), (200, 100)]  # 从圆弧右端点延伸
        }
        
        # 远离圆弧的直线
        far_line = {
            'type': 'LINE',
            'points': [(500, 500), (600, 500)]
        }
        
        print(f"圆弧: 中心={arc['center']}, 半径={arc['radius']}")
        print(f"近距离直线: {near_line['points']}")
        print(f"远距离直线: {far_line['points']}")
        
        # 计算距离
        near_distance = processor._calculate_entity_distance(arc, near_line)
        far_distance = processor._calculate_entity_distance(arc, far_line)
        
        print(f"\n距离计算结果:")
        print(f"圆弧到近距离直线: {near_distance:.2f}")
        print(f"圆弧到远距离直线: {far_distance:.2f}")
        
        # 验证距离计算
        if near_distance < 10 and far_distance > 400:
            print("✓ 圆弧距离计算正确：近距离小，远距离大")
            return True
        else:
            print("✗ 圆弧距离计算可能有问题")
            return False
        
    except Exception as e:
        print(f"✗ 圆弧距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_connection_detection():
    """测试实体连接检测"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        print("测试实体连接检测:")
        
        # 创建连接的圆弧和直线
        arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90
        }
        
        connected_line = {
            'type': 'LINE',
            'points': [(150, 100), (200, 100)]  # 从圆弧端点开始
        }
        
        disconnected_line = {
            'type': 'LINE',
            'points': [(300, 300), (400, 300)]  # 完全分离
        }
        
        print(f"圆弧端点: (150, 100)")
        print(f"连接直线起点: {connected_line['points'][0]}")
        print(f"分离直线起点: {disconnected_line['points'][0]}")
        
        # 测试连接检测
        connected = processor._are_entities_connected(arc, connected_line, tolerance=50)
        disconnected = processor._are_entities_connected(arc, disconnected_line, tolerance=50)
        
        print(f"\n连接检测结果:")
        print(f"圆弧与连接直线: {'连接' if connected else '不连接'}")
        print(f"圆弧与分离直线: {'连接' if disconnected else '不连接'}")
        
        if connected and not disconnected:
            print("✓ 实体连接检测正确")
            return True
        else:
            print("✗ 实体连接检测有问题")
            return False
        
    except Exception as e:
        print(f"✗ 实体连接检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nearest_group_finding():
    """测试最近组查找"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        print("测试最近组查找:")
        
        # 创建两个组
        group1 = [
            {
                'type': 'LINE',
                'points': [(0, 0), (100, 0)]
            }
        ]
        
        group2 = [
            {
                'type': 'LINE',
                'points': [(500, 500), (600, 500)]
            }
        ]
        
        groups = [group1, group2]
        
        # 创建一个圆弧，其端点接近group1
        test_arc = {
            'type': 'ARC',
            'center': (200, 200),  # 圆心距离group1较远
            'radius': 150,
            'start_angle': 180,
            'end_angle': 270
        }
        
        # 圆弧端点应该是 (50, 200) 和 (200, 50)，都比较接近group1
        
        print(f"圆弧: 中心={test_arc['center']}, 半径={test_arc['radius']}")
        print(f"组1直线: {group1[0]['points']}")
        print(f"组2直线: {group2[0]['points']}")
        
        # 查找最近组
        nearest_group = processor._find_nearest_group_for_entity(test_arc, groups)
        
        if nearest_group == group1:
            print("✓ 找到正确的最近组（基于几何距离，不是圆心距离）")
            return True
        elif nearest_group == group2:
            print("✗ 找到错误的最近组（可能仍在使用圆心距离）")
            return False
        else:
            print("✗ 未找到最近组")
            return False
        
    except Exception as e:
        print(f"✗ 最近组查找测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试圆弧分组修复...")
    print("=" * 60)
    
    tests = [
        ("圆弧距离计算", test_arc_distance_calculation),
        ("实体连接检测", test_entity_connection_detection),
        ("最近组查找", test_nearest_group_finding),
        ("圆弧分组不使用圆心", test_arc_grouping_without_center)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧分组修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
