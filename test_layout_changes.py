#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面布局修改
验证左侧窗口排列顺序和配色系统/图像控制位置交换
"""

import os
import sys

def test_left_panel_order():
    """测试左侧面板排列顺序"""
    print("🔍 测试左侧面板排列顺序...")
    
    try:
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找create_widgets方法中的左侧面板创建顺序
        start_marker = "# 左侧控制面板"
        end_marker = "# 右侧可视化区域"
        
        start_pos = content.find(start_marker)
        end_pos = content.find(end_marker)
        
        if start_pos == -1 or end_pos == -1:
            print("❌ 未找到左侧控制面板代码段")
            return False
        
        panel_section = content[start_pos:end_pos]
        
        # 检查期望的顺序
        expected_order = [
            "_create_file_selection",      # 数据文件夹
            "_create_process_control",     # 处理控制
            "_create_status_display",      # 处理状态
            "_create_group_list",          # 实体组列表
            "_create_category_buttons",    # 选择类别
            "_create_action_buttons",      # 操作控制
            "_create_save_options"         # 保存选项
        ]
        
        print("📋 检查左侧面板顺序:")
        for i, method in enumerate(expected_order, 1):
            if method in panel_section:
                print(f"  ✅ {i}. {method}")
            else:
                print(f"  ❌ {i}. {method} - 未找到")
                return False
        
        # 检查顺序是否正确
        positions = []
        for method in expected_order:
            pos = panel_section.find(method)
            if pos != -1:
                positions.append(pos)
        
        if positions == sorted(positions):
            print("✅ 左侧面板排列顺序正确")
            return True
        else:
            print("❌ 左侧面板排列顺序不正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_right_panel_swap():
    """测试右侧面板配色系统和图像控制位置交换"""
    print("\n🔍 测试右侧面板位置交换...")
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找四区域布局代码
        layout_start = content.find("# 区域3：")
        layout_end = content.find("print(\"✅ 四区域可视化布局创建完成")
        
        if layout_start == -1 or layout_end == -1:
            print("❌ 未找到四区域布局代码")
            return False
        
        layout_section = content[layout_start:layout_end]
        
        # 检查区域3是图像控制（左下）
        if "区域3：图像控制" in layout_section and "row=1, column=0" in layout_section:
            print("✅ 区域3：图像控制位于左下角")
        else:
            print("❌ 区域3：图像控制位置不正确")
            return False
        
        # 检查区域4是配色系统（右下）
        if "区域4：配色系统" in layout_section and "row=1, column=1" in layout_section:
            print("✅ 区域4：配色系统位于右下角")
        else:
            print("❌ 区域4：配色系统位置不正确")
            return False
        
        # 检查标题文本
        zoom_title_pos = content.find('text="3. 图像控制"')
        color_title_pos = content.find('text="4. 配色系统"')
        
        if zoom_title_pos != -1:
            print("✅ 图像控制标题正确")
        else:
            print("❌ 图像控制标题不正确")
            return False
        
        if color_title_pos != -1:
            print("✅ 配色系统标题正确")
        else:
            print("❌ 配色系统标题不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_documentation_update():
    """测试文档更新"""
    print("\n🔍 测试文档更新...")
    
    try:
        doc_path = "修改总结文档/四区域布局设计总结.md"
        if not os.path.exists(doc_path):
            print("❌ 文档文件不存在")
            return False
        
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局图更新
        if "3. 图像控制" in content and "4. 配色系统" in content:
            print("✅ 文档布局图已更新")
        else:
            print("❌ 文档布局图未正确更新")
            return False
        
        # 检查区域描述更新
        if "图像控制** - 左下区域" in content and "配色系统** - 右下区域" in content:
            print("✅ 文档区域描述已更新")
        else:
            print("❌ 文档区域描述未正确更新")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🧪 界面布局修改验证")
    print("🎯 验证左侧面板顺序调整和右侧面板位置交换")
    print("=" * 70)
    
    # 测试左侧面板顺序
    left_panel_ok = test_left_panel_order()
    
    # 测试右侧面板交换
    right_panel_ok = test_right_panel_swap()
    
    # 测试文档更新
    doc_ok = test_documentation_update()
    
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"  左侧面板顺序: {'✅ 通过' if left_panel_ok else '❌ 失败'}")
    print(f"  右侧面板交换: {'✅ 通过' if right_panel_ok else '❌ 失败'}")
    print(f"  文档更新: {'✅ 通过' if doc_ok else '❌ 失败'}")
    
    if all([left_panel_ok, right_panel_ok, doc_ok]):
        print("\n🎉 所有修改验证通过！")
        print("\n✨ 修改内容:")
        print("  1. 左侧窗口排列顺序：数据文件夹→处理控制→处理状态→实体组列表→选择类别→操作控制→保存选项")
        print("  2. 配色系统和图像控制位置互换：图像控制在左下，配色系统在右下")
        print("  3. 缩放按钮改名为图像控制")
        print("  4. 相关文档已同步更新")
    else:
        print("\n❌ 部分修改验证失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
