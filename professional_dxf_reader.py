#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业DXF读取器
基于坐标系手性检测的圆弧和椭圆精确读取方案
替代之前的处理方式，确保弧形线和椭圆线在分组过程中和显示中正确无误
"""

import math
import numpy as np
from typing import Dict, Any, Tuple, List
from enum import Enum

class CoordinateSystemType(Enum):
    """坐标系类型"""
    RIGHT_HANDED = "右手坐标系"  # RHS - 逆时针为正
    LEFT_HANDED = "左手坐标系"   # LHS - 顺时针为正

class ProfessionalDXFReader:
    """
    专业DXF读取器
    
    基于您提供的解决方案：
    1. 检测坐标系手性的变化
    2. 在解析弧线数据时进行相应的校正
    3. 确保弧形线和椭圆线在分组过程中和显示中正确无误
    """
    
    def __init__(self):
        """初始化专业DXF读取器"""
        self.default_extrusion = (0, 0, 1)  # 默认拉伸方向 (RHS)
        
    def extract_entity_data_with_handedness_correction(self, entity, doc=None):
        """
        提取实体数据并进行坐标系手性校正
        
        这是核心方法，替代原有的 _extract_entity_data 方法
        """
        entity_data = {
            'type': entity.dxftype(),
            'layer': entity.dxf.layer,
            'entity': entity
        }
        
        # 处理不同类型的实体
        if entity.dxftype() == 'ARC':
            entity_data.update(self._extract_arc_with_handedness_correction(entity))
        elif entity.dxftype() == 'ELLIPSE':
            entity_data.update(self._extract_ellipse_with_handedness_correction(entity))
        elif entity.dxftype() == 'CIRCLE':
            entity_data.update(self._extract_circle_data(entity))
        elif entity.dxftype() == 'LINE':
            entity_data.update(self._extract_line_data(entity))
        elif entity.dxftype() == 'LWPOLYLINE':
            entity_data.update(self._extract_lwpolyline_data(entity))
        elif entity.dxftype() == 'POLYLINE':
            entity_data.update(self._extract_polyline_data(entity))
        elif entity.dxftype() in ['TEXT', 'MTEXT']:
            entity_data.update(self._extract_text_data(entity))
        elif entity.dxftype() == 'INSERT':
            entity_data.update(self._extract_insert_data(entity))
        
        return entity_data
    
    def _extract_arc_with_handedness_correction(self, entity) -> Dict[str, Any]:
        """
        提取圆弧数据并进行坐标系手性校正
        
        核心实现您提供的解决方案：
        1. 检查拉伸方向 (210, 220, 230)
        2. 检测坐标系手性
        3. 进行LHS下的角度校正
        4. 处理角度范围
        """
        # 步骤1: 提取基本数据
        center = entity.dxf.center
        radius = entity.dxf.radius
        start_angle = entity.dxf.start_angle
        end_angle = entity.dxf.end_angle
        
        # 步骤2: 检查拉伸方向 (210, 220, 230) - 关键！
        extrusion_dir = getattr(entity.dxf, 'extrusion', self.default_extrusion)
        z_component = extrusion_dir.z if hasattr(extrusion_dir, 'z') else extrusion_dir[2]
        
        # 步骤3: 检测坐标系手性
        if z_component >= 0:
            # 右手坐标系 (RHS) - 角度正方向逆时针 (CCW) - 无需校正
            coord_system = CoordinateSystemType.RIGHT_HANDED
            corrected_start_angle = start_angle
            corrected_end_angle = end_angle
            correction_applied = False
        else:
            # 左手坐标系 (LHS) - 角度正方向顺时针 (CW) - 需要校正！
            coord_system = CoordinateSystemType.LEFT_HANDED
            
            # 步骤4: LHS下的角度校正 (关键步骤)
            # 校正公式：
            # start_angle_corrected = 360.0 - original_end_angle
            # end_angle_corrected = 360.0 - original_start_angle
            corrected_start_angle = 360.0 - end_angle
            corrected_end_angle = 360.0 - start_angle
            correction_applied = True
        
        # 步骤5: 处理角度范围 (归一化)
        corrected_start_angle = corrected_start_angle % 360
        corrected_end_angle = corrected_end_angle % 360
        
        # 构建返回数据
        arc_data = {
            'center': (center.x, center.y),
            'radius': radius,
            'start_angle': corrected_start_angle,
            'end_angle': corrected_end_angle,
            # 保存原始数据用于调试和验证
            'original_start_angle': start_angle,
            'original_end_angle': end_angle,
            'extrusion_direction': (extrusion_dir.x, extrusion_dir.y, extrusion_dir.z),
            'coordinate_system': coord_system.value,
            'handedness_correction_applied': correction_applied
        }
        
        # 添加调试信息
        if correction_applied:
            print(f"🔧 圆弧手性校正: Z={z_component:.3f} (LHS)")
            print(f"  原始角度: {start_angle:.1f}° - {end_angle:.1f}°")
            print(f"  校正角度: {corrected_start_angle:.1f}° - {corrected_end_angle:.1f}°")
        
        return arc_data
    
    def _extract_ellipse_with_handedness_correction(self, entity) -> Dict[str, Any]:
        """
        提取椭圆数据并进行坐标系手性校正
        
        椭圆的校正原理与圆弧类似，但使用参数而不是角度
        """
        # 步骤1: 提取基本数据
        center = entity.dxf.center
        major_axis = entity.dxf.major_axis
        ratio = entity.dxf.ratio
        start_param = getattr(entity.dxf, 'start_param', 0)
        end_param = getattr(entity.dxf, 'end_param', 2 * math.pi)
        
        # 步骤2: 检查拉伸方向 (210, 220, 230)
        extrusion_dir = getattr(entity.dxf, 'extrusion', self.default_extrusion)
        z_component = extrusion_dir.z if hasattr(extrusion_dir, 'z') else extrusion_dir[2]
        
        # 步骤3: 检测坐标系手性
        if z_component >= 0:
            # 右手坐标系 (RHS) - 无需校正
            coord_system = CoordinateSystemType.RIGHT_HANDED
            corrected_start_param = start_param
            corrected_end_param = end_param
            correction_applied = False
        else:
            # 左手坐标系 (LHS) - 需要校正
            coord_system = CoordinateSystemType.LEFT_HANDED
            
            # 椭圆参数校正公式（类似圆弧角度校正）
            corrected_start_param = 2 * math.pi - end_param
            corrected_end_param = 2 * math.pi - start_param
            correction_applied = True
        
        # 步骤4: 处理参数范围 (归一化)
        corrected_start_param = corrected_start_param % (2 * math.pi)
        corrected_end_param = corrected_end_param % (2 * math.pi)
        
        # 构建返回数据
        ellipse_data = {
            'center': (center.x, center.y),
            'major_axis': (major_axis.x, major_axis.y),
            'ratio': ratio,
            'start_param': corrected_start_param,
            'end_param': corrected_end_param,
            # 保存原始数据
            'original_start_param': start_param,
            'original_end_param': end_param,
            'extrusion_direction': (extrusion_dir.x, extrusion_dir.y, extrusion_dir.z),
            'coordinate_system': coord_system.value,
            'handedness_correction_applied': correction_applied
        }
        
        # 添加调试信息
        if correction_applied:
            print(f"🔧 椭圆手性校正: Z={z_component:.3f} (LHS)")
            print(f"  原始参数: {start_param:.3f} - {end_param:.3f}")
            print(f"  校正参数: {corrected_start_param:.3f} - {corrected_end_param:.3f}")
        
        return ellipse_data
    
    def _extract_circle_data(self, entity) -> Dict[str, Any]:
        """提取圆形数据"""
        center = entity.dxf.center
        return {
            'center': (center.x, center.y),
            'radius': entity.dxf.radius
        }
    
    def _extract_line_data(self, entity) -> Dict[str, Any]:
        """提取直线数据"""
        start = entity.dxf.start
        end = entity.dxf.end
        return {
            'points': [(start.x, start.y), (end.x, end.y)]
        }
    
    def _extract_lwpolyline_data(self, entity) -> Dict[str, Any]:
        """提取轻量多段线数据"""
        points = []
        for point in entity.get_points():
            if hasattr(point, 'x') and hasattr(point, 'y'):
                points.append((float(point.x), float(point.y)))
            elif isinstance(point, (list, tuple)) and len(point) >= 2:
                points.append((float(point[0]), float(point[1])))
        
        return {
            'points': points,
            'closed': getattr(entity.dxf, 'flags', 0) & 1 == 1
        }
    
    def _extract_polyline_data(self, entity) -> Dict[str, Any]:
        """提取多段线数据"""
        points = []
        for vertex in entity.vertices:
            location = vertex.dxf.location
            points.append((location.x, location.y))
        
        return {
            'points': points,
            'closed': entity.is_closed
        }
    
    def _extract_text_data(self, entity) -> Dict[str, Any]:
        """提取文本数据"""
        insert = entity.dxf.insert
        return {
            'text': entity.dxf.text,
            'insert': (insert.x, insert.y),
            'height': entity.dxf.height,
            'rotation': getattr(entity.dxf, 'rotation', 0)
        }
    
    def _extract_insert_data(self, entity) -> Dict[str, Any]:
        """提取插入块数据"""
        insert = entity.dxf.insert
        return {
            'name': entity.dxf.name,
            'insert': (insert.x, insert.y),
            'xscale': getattr(entity.dxf, 'xscale', 1.0),
            'yscale': getattr(entity.dxf, 'yscale', 1.0),
            'rotation': getattr(entity.dxf, 'rotation', 0)
        }
    
    def validate_handedness_correction(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证手性校正结果
        
        返回校正统计信息
        """
        stats = {
            'total_entities': len(entities),
            'arc_entities': 0,
            'ellipse_entities': 0,
            'corrected_arcs': 0,
            'corrected_ellipses': 0,
            'rhs_entities': 0,
            'lhs_entities': 0,
            'correction_details': []
        }
        
        for entity in entities:
            entity_type = entity.get('type', '')
            
            if entity_type == 'ARC':
                stats['arc_entities'] += 1
                if entity.get('handedness_correction_applied', False):
                    stats['corrected_arcs'] += 1
                    stats['correction_details'].append({
                        'type': 'ARC',
                        'original_angles': (entity.get('original_start_angle'), entity.get('original_end_angle')),
                        'corrected_angles': (entity.get('start_angle'), entity.get('end_angle')),
                        'coordinate_system': entity.get('coordinate_system')
                    })
                
                # 统计坐标系类型
                if entity.get('coordinate_system') == CoordinateSystemType.RIGHT_HANDED.value:
                    stats['rhs_entities'] += 1
                else:
                    stats['lhs_entities'] += 1
            
            elif entity_type == 'ELLIPSE':
                stats['ellipse_entities'] += 1
                if entity.get('handedness_correction_applied', False):
                    stats['corrected_ellipses'] += 1
                    stats['correction_details'].append({
                        'type': 'ELLIPSE',
                        'original_params': (entity.get('original_start_param'), entity.get('original_end_param')),
                        'corrected_params': (entity.get('start_param'), entity.get('end_param')),
                        'coordinate_system': entity.get('coordinate_system')
                    })
                
                # 统计坐标系类型
                if entity.get('coordinate_system') == CoordinateSystemType.RIGHT_HANDED.value:
                    stats['rhs_entities'] += 1
                else:
                    stats['lhs_entities'] += 1
        
        return stats
    
    def print_correction_summary(self, stats: Dict[str, Any]):
        """打印校正摘要"""
        print("\n" + "="*60)
        print("🔍 专业DXF读取器 - 坐标系手性校正摘要")
        print("="*60)
        print(f"总实体数: {stats['total_entities']}")
        print(f"圆弧实体: {stats['arc_entities']} (校正: {stats['corrected_arcs']})")
        print(f"椭圆实体: {stats['ellipse_entities']} (校正: {stats['corrected_ellipses']})")
        print(f"右手坐标系 (RHS): {stats['rhs_entities']}")
        print(f"左手坐标系 (LHS): {stats['lhs_entities']}")
        
        if stats['correction_details']:
            print("\n📐 校正详情:")
            for detail in stats['correction_details']:
                print(f"  {detail['type']} ({detail['coordinate_system']}):")
                if detail['type'] == 'ARC':
                    print(f"    原始角度: {detail['original_angles']}")
                    print(f"    校正角度: {detail['corrected_angles']}")
                else:
                    print(f"    原始参数: {detail['original_params']}")
                    print(f"    校正参数: {detail['corrected_params']}")
        
        print("="*60)
