#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试自动房间识别按钮功能
验证UI中的自动识别按钮是否正常工作
"""

import tkinter as tk
import time

def test_auto_room_recognition():
    """测试自动房间识别功能"""
    print("🚀 开始测试自动房间识别功能...")
    print("="*80)
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建根窗口
        root = tk.Tk()
        root.title("自动房间识别测试")
        root.geometry("1400x900")
        
        print("✅ 创建根窗口成功")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        print("✅ 创建应用实例成功")
        
        # 更新界面确保初始化完成
        root.update()
        time.sleep(1)
        
        # 检查房间识别UI是否存在
        print("\n🔍 检查房间识别UI...")
        
        if hasattr(app, 'room_ui') and app.room_ui:
            print("✅ 房间识别UI存在")
            
            # 检查自动识别按钮是否存在
            if hasattr(app.room_ui, '_auto_room_recognition'):
                print("✅ 自动识别方法存在")
            else:
                print("❌ 自动识别方法不存在")
                return False
            
            # 检查UI组件
            if hasattr(app.room_ui, 'room_tree'):
                print("✅ 房间列表组件存在")
            else:
                print("❌ 房间列表组件不存在")
                return False
                
        else:
            print("❌ 房间识别UI不存在")
            return False
        
        # 创建模拟数据
        print("\n📊 创建模拟数据...")
        
        # 模拟墙体组
        mock_wall_entities = [
            # 外墙 - 形成一个大房间
            {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 4000, 'y': 0}, 'label': 'wall'},
            {'type': 'LINE', 'start': {'x': 4000, 'y': 0}, 'end': {'x': 4000, 'y': 3000}, 'label': 'wall'},
            {'type': 'LINE', 'start': {'x': 4000, 'y': 3000}, 'end': {'x': 0, 'y': 3000}, 'label': 'wall'},
            {'type': 'LINE', 'start': {'x': 0, 'y': 3000}, 'end': {'x': 0, 'y': 0}, 'label': 'wall'},
            
            # 内墙 - 分隔房间
            {'type': 'LINE', 'start': {'x': 2000, 'y': 0}, 'end': {'x': 2000, 'y': 1200}, 'label': 'wall'},
            {'type': 'LINE', 'start': {'x': 2000, 'y': 1800}, 'end': {'x': 2000, 'y': 3000}, 'label': 'wall'},
            {'type': 'LINE', 'start': {'x': 0, 'y': 1500}, 'end': {'x': 1500, 'y': 1500}, 'label': 'wall'},
            {'type': 'LINE', 'start': {'x': 2500, 'y': 1500}, 'end': {'x': 4000, 'y': 1500}, 'label': 'wall'},
        ]
        
        # 模拟门窗组
        mock_door_window_entities = [
            # 门窗1 - 在内墙上
            {'type': 'LINE', 'start': {'x': 2000, 'y': 1200}, 'end': {'x': 2000, 'y': 1800}, 'label': 'door'},
            
            # 门窗2 - 在横墙上
            {'type': 'LINE', 'start': {'x': 1500, 'y': 1500}, 'end': {'x': 2500, 'y': 1500}, 'label': 'door'},
        ]
        
        # 创建模拟的实体组
        mock_groups = [
            mock_wall_entities,  # 墙体组
            mock_door_window_entities  # 门窗组
        ]
        
        # 设置到处理器
        if hasattr(app, 'processor'):
            app.processor.all_groups = mock_groups
            print("✅ 模拟数据设置到处理器")
        
        # 测试数据获取功能
        print("\n📊 测试数据获取功能...")
        
        try:
            data = app._get_wall_door_data()
            
            if data:
                wall_groups = data.get('wall_groups', [])
                door_window_groups = data.get('door_window_groups', [])
                
                print(f"✅ 获取到 {len(wall_groups)} 个墙体组")
                print(f"✅ 获取到 {len(door_window_groups)} 个门窗组")
                
                if wall_groups:
                    print("✅ 数据获取功能正常")
                else:
                    print("⚠️ 未获取到墙体数据")
            else:
                print("❌ 数据获取失败")
                return False
                
        except Exception as e:
            print(f"❌ 数据获取测试失败: {e}")
            return False
        
        # 测试自动识别功能
        print("\n📊 测试自动识别功能...")
        
        try:
            # 模拟点击自动识别按钮
            print("🤖 模拟点击自动识别按钮...")
            
            # 直接调用自动识别方法
            app.room_ui._auto_room_recognition()
            
            # 等待处理完成
            time.sleep(2)
            root.update()
            
            # 检查识别结果
            if hasattr(app.room_ui.room_processor, 'rooms') and app.room_ui.room_processor.rooms:
                rooms = app.room_ui.room_processor.rooms
                print(f"✅ 识别成功: {len(rooms)} 个房间")
                
                # 显示识别结果
                for i, room in enumerate(rooms):
                    room_type = room['type']
                    area = room['area']
                    width = room.get('width', 0)
                    print(f"   房间 {i+1}: {room_type}, 面积: {area:.1f}, 宽度: {width:.1f}")
                
                # 检查房间列表是否更新
                if hasattr(app.room_ui, 'room_tree'):
                    tree_items = app.room_ui.room_tree.get_children()
                    if tree_items:
                        print(f"✅ 房间列表已更新: {len(tree_items)} 项")
                        
                        # 显示列表内容
                        for item in tree_items:
                            values = app.room_ui.room_tree.item(item)['values']
                            if len(values) >= 3 and values[1] != "正在识别...":
                                print(f"   列表项: {values[0]} - {values[1]} - {values[2]}")
                    else:
                        print("⚠️ 房间列表为空")
                
                return True
                
            else:
                print("❌ 未获取到识别结果")
                return False
                
        except Exception as e:
            print(f"❌ 自动识别测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 保持窗口显示一段时间
        print("\n💡 窗口将保持显示5秒以便观察效果...")
        root.after(5000, root.destroy)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 自动房间识别功能测试")
    print("="*80)
    
    try:
        success = test_auto_room_recognition()
        
        print("\n" + "="*80)
        print("📊 测试总结:")
        
        if success:
            print("🎉 自动房间识别功能测试成功！")
            print("\n💡 验证的功能:")
            print("   1. ✅ 自动识别按钮正确添加")
            print("   2. ✅ 数据获取功能正常")
            print("   3. ✅ 房间识别流程正常")
            print("   4. ✅ 结果显示功能正常")
            print("   5. ✅ UI更新功能正常")
            
            print("\n🎯 新增功能:")
            print("   - 🤖 自动房间识别按钮")
            print("   - 📊 完整的房间切分流程")
            print("   - 📋 实时结果显示")
            print("   - 🔄 进度提示功能")
            print("   - 📈 识别结果统计")
        else:
            print("❌ 自动房间识别功能测试失败")
            print("💡 请检查UI组件和识别逻辑")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
