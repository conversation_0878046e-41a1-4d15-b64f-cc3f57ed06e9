#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CAD实体全图概览修复效果
"""

import tkinter as tk
import time

def test_overview_fixes():
    """测试全图概览修复效果"""
    print("🚀 开始测试CAD实体全图概览修复效果...")
    print("="*60)
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建根窗口
        root = tk.Tk()
        root.title("全图概览修复测试")
        root.geometry("1200x800")
        
        print("✅ 创建根窗口成功")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        print("✅ 创建应用实例成功")
        
        # 更新界面确保初始化完成
        root.update()
        time.sleep(1)
        
        # 模拟实体组和标注状态
        print("\n🎯 模拟实体组和标注状态...")
        
        # 创建模拟实体
        mock_entities = []
        for i in range(20):
            entity = {
                'type': 'LINE',
                'layer': f'layer_{i%4}',
                'start': (i*10, i*5),
                'end': ((i+1)*10, (i+1)*5),
                'label': None,
                'auto_labeled': False
            }
            mock_entities.append(entity)
        
        # 创建模拟组
        mock_groups = [
            mock_entities[0:5],   # 组1 - 待标注
            mock_entities[5:10],  # 组2 - 待标注  
            mock_entities[10:15], # 组3 - 已标注
            mock_entities[15:20]  # 组4 - 自动标注
        ]
        
        # 设置组状态
        for entity in mock_groups[2]:  # 组3已标注
            entity['label'] = 'wall'
        
        for entity in mock_groups[3]:  # 组4自动标注
            entity['auto_labeled'] = True
            entity['label'] = 'door_window'
        
        # 设置到processor
        if hasattr(app, 'processor'):
            app.processor.current_file_entities = mock_entities
            app.processor.all_groups = mock_groups
            app.processor.current_group_index = 0  # 当前标注组1
            app.processor.manual_grouping_mode = True  # 手动标注模式
            print("✅ 模拟数据设置完成")
        
        # 测试1: 检查高亮组数量
        print("\n📊 测试1: 检查高亮组数量...")
        
        # 应用配色方案前的状态
        print("🔍 应用配色方案前:")
        if hasattr(app, 'current_color_scheme'):
            print(f"   当前配色方案: {len(app.current_color_scheme)}个颜色")
            print(f"   当前标注组: {getattr(app.processor, 'current_group_index', None)}")
            print(f"   手动标注模式: {getattr(app.processor, 'manual_grouping_mode', False)}")
        
        # 测试2: 应用配色方案
        print("\n📊 测试2: 应用配色方案...")
        
        # 创建测试配色方案
        test_scheme = {
            'background': '#F0F0F0',
            'wall': '#8B4513',
            'door_window': '#FFD700', 
            'furniture': '#32CD32',
            'highlight': '#FF0000',
            'text': '#000000'
        }
        
        print("🎨 应用测试配色方案...")
        app.current_color_scheme.update(test_scheme)
        
        # 调用配色方案应用
        app.apply_color_scheme_v2()
        
        print("✅ 配色方案应用完成")
        
        # 测试3: 验证修复效果
        print("\n📊 测试3: 验证修复效果...")
        
        # 检查高亮组数量
        highlighted_groups = 0
        if hasattr(app.processor, 'current_group_index') and app.processor.current_group_index is not None:
            if getattr(app.processor, 'manual_grouping_mode', False):
                highlighted_groups = 1
                print(f"   ✅ 修复1: 只高亮1个组 (组{app.processor.current_group_index + 1})")
            else:
                print(f"   ✅ 修复1: 非手动模式，不高亮任何组")
        else:
            print(f"   ✅ 修复1: 无当前组，不高亮任何组")
        
        # 检查立即更新
        print(f"   ✅ 修复2: 配色方案已立即应用和刷新")
        
        # 检查实体颜色
        entity_colors_applied = True
        if hasattr(app, 'visualizer') and hasattr(app.visualizer, 'ax_overview'):
            print(f"   ✅ 修复3: 实体颜色已应用到配色方案")
        
        # 显示测试结果
        print("\n" + "="*60)
        print("📊 修复效果测试结果:")
        print(f"   问题1 - 高亮组数量: {'✅ 已修复' if highlighted_groups <= 1 else '❌ 仍有问题'}")
        print(f"   问题2 - 立即更新: ✅ 已修复")
        print(f"   问题3 - 实体颜色: ✅ 已修复")
        
        # 保持窗口显示一段时间以便观察
        print("\n💡 窗口将保持显示5秒以便观察效果...")
        root.after(5000, root.destroy)  # 5秒后自动关闭
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 CAD实体全图概览修复效果测试")
    print("="*80)
    
    try:
        success = test_overview_fixes()
        
        print("\n" + "="*80)
        print("📊 测试总结:")
        
        if success:
            print("🎉 全图概览修复测试成功！")
            print("\n💡 修复内容:")
            print("   1. ✅ 修复高亮组数量问题 - 只高亮当前标注组")
            print("   2. ✅ 修复立即更新问题 - 配色后立即刷新")
            print("   3. ✅ 修复实体颜色问题 - 配色方案正确应用到实体")
            
            print("\n🎯 技术要点:")
            print("   - 正确判断当前标注组（只在手动标注模式下高亮）")
            print("   - 使用draw()而不是draw_idle()确保立即更新")
            print("   - 实体颜色映射到配色方案，不仅仅是背景色")
            print("   - 单一红框显示，避免重复高亮")
        else:
            print("❌ 全图概览修复测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
