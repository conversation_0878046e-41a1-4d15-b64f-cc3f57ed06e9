#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终改进
"""

import sys
import os

def test_fill_button_improvements():
    """测试填充按钮改进"""
    try:
        print("测试填充按钮改进:")
        
        # 检查V2版本是否包含了改进的填充按钮功能
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("自定义组列表", "_create_group_list"),
            ("创建填充按钮", "_create_fill_button"),
            ("填充按钮颜色", "button_color"),
            ("填充状态管理", "group_fill_status"),
            ("清除填充确认", "_clear_group_fill"),
            ("增强版填充", "_fill_group_enhanced"),
            ("更新按钮显示", "_update_fill_button"),
            ("滚动容器", "scrollable_frame"),
            ("Canvas容器", "group_canvas")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 填充按钮改进检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 填充按钮改进测试失败: {e}")
        return False

def test_coordinate_system_improvements():
    """测试坐标系改进"""
    try:
        print("测试坐标系改进:")
        
        # 检查V2版本是否包含了坐标系改进
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("增强坐标范围", "_enhance_coordinate_range"),
            ("提取实体坐标", "_extract_entity_coordinates"),
            ("坐标边距计算", "margin_x"),
            ("设置坐标范围", "set_xlim"),
            ("坐标范围日志", "坐标范围:"),
            ("圆形坐标处理", "CIRCLE"),
            ("圆弧坐标处理", "ARC"),
            ("椭圆坐标处理", "ELLIPSE")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 坐标系改进检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 坐标系改进测试失败: {e}")
        return False

def test_color_scheme_completeness():
    """测试配色方案完整性"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        print("测试配色方案完整性:")
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查基础颜色
        basic_colors = [
            'background', 'wall', 'door_window', 'railing', 'furniture',
            'bed', 'sofa', 'cabinet', 'dining_table', 'appliance',
            'stair', 'elevator', 'dimension', 'room_label', 'column',
            'other', 'fill', 'text', 'current_group', 'labeled_group'
        ]
        
        # 检查填充颜色
        fill_colors = [
            'wall_fill', 'door_window_fill', 'railing_fill', 'furniture_fill',
            'bed_fill', 'sofa_fill', 'cabinet_fill', 'dining_table_fill',
            'appliance_fill', 'stair_fill', 'elevator_fill', 'dimension_fill',
            'room_label_fill', 'column_fill', 'other_fill'
        ]
        
        all_colors = basic_colors + fill_colors
        default_scheme = app.default_color_scheme
        
        missing_colors = []
        for color in all_colors:
            if color in default_scheme:
                print(f"✓ 颜色 {color}: {default_scheme[color]}")
            else:
                print(f"✗ 颜色 {color}: 缺失")
                missing_colors.append(color)
        
        root.destroy()
        
        if missing_colors:
            print(f"✗ 缺失颜色: {missing_colors}")
            return False
        else:
            print(f"✓ 配色方案完整，包含 {len(all_colors)} 种颜色")
            return True
        
    except Exception as e:
        print(f"✗ 配色方案完整性测试失败: {e}")
        return False

def test_v2_app_functionality():
    """测试V2应用功能"""
    try:
        print("测试V2应用功能:")
        
        # 检查V2应用的基本结构
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和方法
        checks = [
            ("V2应用类", "class EnhancedCADAppV2"),
            ("V2填充处理器", "EnhancedWallFillProcessorV2"),
            ("自动填充墙体", "auto_fill_walls"),
            ("交互式填充", "_start_interactive_fill"),
            ("自动填充实现", "_auto_fill_walls_impl"),
            ("更新V2全图概览", "update_complete_v2_overview"),
            ("主函数V2", "def main_v2"),
            ("matplotlib设置", "matplotlib.use")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ V2应用功能检查通过")
        return True
        
    except Exception as e:
        print(f"✗ V2应用功能测试失败: {e}")
        return False

def test_ui_improvements():
    """测试UI改进"""
    try:
        print("测试UI改进:")
        
        # 检查UI改进
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查UI组件
        ui_checks = [
            ("表头框架", "header_frame"),
            ("滚动条", "Scrollbar"),
            ("Canvas滚动", "group_canvas"),
            ("行容器", "row_frame"),
            ("状态标签", "status_label"),
            ("类型标签", "type_label"),
            ("实体数标签", "count_label"),
            ("双击绑定", "bind('<Double-1>'"),
            ("标签显示名称", "_get_label_display_name"),
            ("组行创建", "_create_group_row")
        ]
        
        for check_name, pattern in ui_checks:
            if pattern in content:
                print(f"✓ {check_name}: 已实现")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ UI改进检查通过")
        return True
        
    except Exception as e:
        print(f"✗ UI改进测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    try:
        print("测试错误处理:")
        
        # 检查错误处理
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计异常处理
        exception_count = content.count("except Exception as e:")
        try_count = content.count("try:")
        print_error_count = content.count("print(f\"")
        
        print(f"✓ try块数量: {try_count}")
        print(f"✓ 异常处理块数量: {exception_count}")
        print(f"✓ 错误输出语句数量: {print_error_count}")
        
        # 检查关键方法的错误处理
        critical_methods = [
            "_create_fill_button",
            "_fill_group_enhanced",
            "_clear_group_fill",
            "_enhance_coordinate_range",
            "_extract_entity_coordinates"
        ]
        
        for method in critical_methods:
            method_start = content.find(f"def {method}")
            if method_start != -1:
                method_end = content.find("def ", method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                method_content = content[method_start:method_end]
                if "except Exception as e:" in method_content:
                    print(f"✓ 方法 {method} 有异常处理")
                else:
                    print(f"✗ 方法 {method} 缺少异常处理")
                    return False
        
        print("✓ 错误处理检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试最终改进...")
    print("=" * 60)
    
    tests = [
        ("填充按钮改进", test_fill_button_improvements),
        ("坐标系改进", test_coordinate_system_improvements),
        ("配色方案完整性", test_color_scheme_completeness),
        ("V2应用功能", test_v2_app_functionality),
        ("UI改进", test_ui_improvements),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 最终改进测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
