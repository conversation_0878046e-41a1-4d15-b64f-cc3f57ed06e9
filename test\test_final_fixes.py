#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终修复的所有功能
"""

import sys
import os

def test_imports():
    """测试导入是否正常"""
    try:
        from main_enhanced import EnhancedCADProcessor, EnhancedCADApp
        from cad_visualizer import CADVisualizer
        from cad_data_processor import CADDataProcessor
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_relabel_labeled_groups():
    """测试已标注组重新标注功能"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 创建已标注的组
        labeled_group = [
            {'type': 'LINE', 'layer': 'furniture', 'points': [(0, 0), (10, 10)], 'label': 'furniture'}
        ]
        
        processor.all_groups = [labeled_group]
        processor._update_groups_info()
        processor._update_pending_manual_groups()
        
        # 测试是否可以重新标注
        can_relabel = processor.can_relabel_group(1)
        
        if can_relabel:
            print("✓ 已标注组可以重新标注")
            return True
        else:
            print("✗ 已标注组无法重新标注")
            return False
        
    except Exception as e:
        print(f"✗ 已标注组重新标注测试失败: {e}")
        return False

def test_dash_line_detection():
    """测试虚线识别功能"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 检查代码中是否包含虚线识别逻辑
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'linetype' in content and 'is_dashed' in content and 'DASHED' in content:
            print("✓ 虚线识别功能已实现")
            return True
        else:
            print("✗ 虚线识别功能未实现")
            return False
        
    except Exception as e:
        print(f"✗ 虚线识别测试失败: {e}")
        return False

def test_color_system():
    """测试配色系统功能"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = EnhancedCADApp(root)
        
        # 检查配色系统是否初始化
        if hasattr(app, 'color_scheme') and hasattr(app, 'default_color_scheme'):
            print("✓ 配色系统已初始化")
        else:
            print("✗ 配色系统未初始化")
            root.destroy()
            return False
        
        # 检查配色系统方法是否存在
        methods = ['open_color_settings', 'save_color_scheme', 'load_color_scheme']
        for method in methods:
            if hasattr(app, method):
                print(f"✓ 配色系统方法 {method} 存在")
            else:
                print(f"✗ 配色系统方法 {method} 不存在")
                root.destroy()
                return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 配色系统测试失败: {e}")
        return False

def test_visualizer_color_support():
    """测试可视化器配色支持"""
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 检查配色方案是否存在
        if hasattr(visualizer, 'color_scheme'):
            print("✓ 可视化器配色方案已初始化")
        else:
            print("✗ 可视化器配色方案未初始化")
            return False
        
        # 检查更新配色方法是否存在
        if hasattr(visualizer, 'update_color_scheme'):
            print("✓ 可视化器配色更新方法存在")
        else:
            print("✗ 可视化器配色更新方法不存在")
            return False
        
        # 测试配色更新
        test_scheme = {'background': '#F0F0F0', 'wall': '#123456'}
        visualizer.update_color_scheme(test_scheme)
        
        if visualizer.color_scheme['background'] == '#F0F0F0':
            print("✓ 配色更新功能正常")
            return True
        else:
            print("✗ 配色更新功能异常")
            return False
        
    except Exception as e:
        print(f"✗ 可视化器配色支持测试失败: {e}")
        return False

def test_clear_fills_functionality():
    """测试清除填充功能"""
    try:
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 检查清除填充方法是否存在
        if hasattr(app, 'clear_wall_fills'):
            print("✓ 清除填充功能存在")
            root.destroy()
            return True
        else:
            print("✗ 清除填充功能不存在")
            root.destroy()
            return False
        
    except Exception as e:
        print(f"✗ 清除填充功能测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件是否正确添加"""
    try:
        # 检查代码中是否包含新的UI组件
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        ui_components = [
            '配色系统',
            '配色设置',
            '保存配色',
            '加载配色',
            '清除填充',
            '_create_color_system'
        ]
        
        missing_components = []
        for component in ui_components:
            if component not in content:
                missing_components.append(component)
        
        if not missing_components:
            print("✓ 所有UI组件都已添加")
            return True
        else:
            print(f"✗ 缺少UI组件: {missing_components}")
            return False
        
    except Exception as e:
        print(f"✗ UI组件测试失败: {e}")
        return False

def test_color_scheme_file_operations():
    """测试配色文件操作"""
    try:
        # 创建测试配色文件
        test_scheme_content = """background=#FFFFFF
wall=#000000
door_window=#FF0000
railing=#00FF00
furniture=#0000FF
bed=#FF00FF
sofa=#FFFF00
cabinet=#00FFFF
dining_table=#800080
appliance=#FFA500
stair=#808080
elevator=#800000
dimension=#008000
room_label=#000080
column=#808000
other=#C0C0C0
fill=#E0E0E0
text=#000000
current_group=#FF0000
labeled_group=#00FF00"""
        
        # 创建测试目录
        test_dir = "test_color_schemes"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        
        test_file = os.path.join(test_dir, "test_scheme.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_scheme_content)
        
        # 测试读取
        from main_enhanced import EnhancedCADApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        scheme = app.load_color_scheme_from_file(test_file)
        
        # 清理
        os.remove(test_file)
        os.rmdir(test_dir)
        root.destroy()
        
        if scheme and 'background' in scheme and scheme['background'] == '#FFFFFF':
            print("✓ 配色文件操作正常")
            return True
        else:
            print("✗ 配色文件操作异常")
            return False
        
    except Exception as e:
        print(f"✗ 配色文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试最终修复功能...")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("已标注组重新标注", test_relabel_labeled_groups),
        ("虚线识别", test_dash_line_detection),
        ("配色系统", test_color_system),
        ("可视化器配色支持", test_visualizer_color_support),
        ("清除填充功能", test_clear_fills_functionality),
        ("UI组件", test_ui_components),
        ("配色文件操作", test_color_scheme_file_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有最终修复功能测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
