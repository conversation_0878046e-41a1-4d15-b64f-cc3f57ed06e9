#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的几何重建测试
"""

import sys
import os
import math

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    print("=== 测试导入 ===")
    
    try:
        from cad_data_processor import GeometricReconstructor, TransformationMatrix
        print("✅ 导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        from cad_data_processor import GeometricReconstructor
        
        # 创建重建器
        reconstructor = GeometricReconstructor()
        print("✅ 重建器创建成功")
        
        # 测试圆弧采样
        sample_points = reconstructor.sample_arc_points(
            center=(0, 0),
            radius=1,
            start_angle=0,
            end_angle=90,
            num_samples=5
        )
        
        print(f"✅ 圆弧采样成功，采样了 {len(sample_points)} 个点")
        
        # 测试圆拟合
        points_2d = [(p[0], p[1]) for p in sample_points]
        center, radius = reconstructor._fit_circle_least_squares(points_2d)
        
        if center and radius:
            print(f"✅ 圆拟合成功: 中心({center[0]:.3f}, {center[1]:.3f}), 半径={radius:.3f}")
        else:
            print("❌ 圆拟合失败")
            return False
        
        # 测试圆弧重建
        result = reconstructor.reconstruct_arc_from_points(sample_points)
        
        if result:
            print(f"✅ 圆弧重建成功: {result['type']}")
            print(f"   中心: ({result['center'][0]:.3f}, {result['center'][1]:.3f})")
            print(f"   半径: {result['radius']:.3f}")
            print(f"   角度: {result['start_angle']:.1f}° - {result['end_angle']:.1f}°")
        else:
            print("❌ 圆弧重建失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transformation_matrix():
    """测试变换矩阵"""
    print("\n=== 测试变换矩阵 ===")
    
    try:
        from cad_data_processor import TransformationMatrix
        
        # 创建测试点
        class MockPoint:
            def __init__(self, x, y):
                self.x = x
                self.y = y
        
        base_point = MockPoint(0, 0)
        
        # 测试基本变换
        matrix = TransformationMatrix(base_point, 0, (1.0, 1.0))
        test_point = (1.0, 0.0)
        result = matrix.apply(test_point)
        print(f"✅ 无变换: {test_point} → {result}")
        
        # 测试镜像检测
        mirror_matrix = TransformationMatrix(base_point, 0, (1.0, -1.0))
        is_mirrored = mirror_matrix.is_mirrored()
        print(f"✅ 镜像检测: {is_mirrored}")
        
        return True
        
    except Exception as e:
        print(f"❌ 变换矩阵测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arc_transformation():
    """测试圆弧变换"""
    print("\n=== 测试圆弧变换 ===")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        # 创建模拟圆弧
        class MockPoint:
            def __init__(self, x, y):
                self.x = x
                self.y = y
        
        class MockDxf:
            def __init__(self):
                self.center = MockPoint(0, 0)
                self.radius = 1.0
                self.start_angle = 0
                self.end_angle = 90
        
        class MockArc:
            def __init__(self):
                self.dxf = MockDxf()
            
            def dxftype(self):
                return 'ARC'
        
        # 创建处理器和圆弧
        processor = CADDataProcessor()
        arc = MockArc()
        
        # 创建变换矩阵
        from cad_data_processor import TransformationMatrix
        base_point = MockPoint(0, 0)
        matrix = TransformationMatrix(base_point, 0, (1.0, -1.0))  # X轴镜像
        
        # 应用变换
        result = processor._transform_arc(arc, matrix)
        
        if result:
            print(f"✅ 圆弧变换成功: {result['type']}")
            print(f"   中心: ({result['center'][0]:.3f}, {result['center'][1]:.3f})")
            if result['type'] == 'ARC':
                print(f"   半径: {result['radius']:.3f}")
                print(f"   角度: {result['start_angle']:.1f}° - {result['end_angle']:.1f}°")
        else:
            print("❌ 圆弧变换失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 圆弧变换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("简单几何重建测试")
    print("=" * 40)
    
    tests = [
        ("导入测试", test_import),
        ("基本功能测试", test_basic_functionality),
        ("变换矩阵测试", test_transformation_matrix),
        ("圆弧变换测试", test_arc_transformation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！几何重建法实现正确。")
        print("\n📋 功能验证:")
        print("✅ 几何重建器创建正常")
        print("✅ 圆弧采样功能正常")
        print("✅ 圆拟合算法正常")
        print("✅ 圆弧重建功能正常")
        print("✅ 变换矩阵功能正常")
        print("✅ 镜像检测功能正常")
        print("✅ 圆弧变换功能正常")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
