#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试圆弧和椭圆重构方案
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_arc_reconstruction():
    """测试圆弧重构"""
    print("=== 测试圆弧重构 ===")
    
    try:
        from arc_ellipse_reconstructor import ArcEllipseReconstructor
        
        # 创建重构器
        reconstructor = ArcEllipseReconstructor(sample_points_count=30)
        
        # 测试用例：各种复杂的圆弧
        test_arcs = [
            {
                'name': '正常圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (0, 0),
                    'radius': 10,
                    'start_angle': 0,
                    'end_angle': 90,
                    'layer': 'test'
                }
            },
            {
                'name': '跨越0度圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (0, 0),
                    'radius': 10,
                    'start_angle': 270,
                    'end_angle': 45,
                    'layer': 'test'
                }
            },
            {
                'name': '镜像圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (0, 0),
                    'radius': 10,
                    'start_angle': 0,
                    'end_angle': 90,
                    'scale_x': -1.0,
                    'scale_y': 1.0,
                    'layer': 'test'
                }
            },
            {
                'name': '缩放圆弧',
                'entity': {
                    'type': 'ARC',
                    'center': (0, 0),
                    'radius': 10,
                    'start_angle': 180,
                    'end_angle': 270,
                    'scale_x': 1.5,
                    'scale_y': 0.8,
                    'layer': 'test'
                }
            }
        ]
        
        success_count = 0
        
        for test_case in test_arcs:
            print(f"\n🔄 测试: {test_case['name']}")
            
            try:
                # 重构圆弧
                reconstructed = reconstructor.reconstruct_arc(test_case['entity'])
                
                # 验证结果
                if reconstructed['type'] == 'POLYLINE':
                    points = np.array(reconstructed['points'])
                    info = reconstructed['reconstruction_info']
                    
                    print(f"  ✅ 重构成功")
                    print(f"    原始类型: {reconstructed['original_type']}")
                    print(f"    新类型: {reconstructed['type']}")
                    print(f"    采样点数: {len(points)}")
                    print(f"    中心点: {info['center']}")
                    print(f"    半径: {info['radius']:.2f}")
                    print(f"    角度范围: {info['start_angle']:.1f}° - {info['end_angle']:.1f}°")
                    
                    # 验证点的合理性
                    if len(points) >= 3:
                        # 计算点到中心的距离
                        center = np.array(info['center'])
                        distances = np.linalg.norm(points - center, axis=1)
                        avg_distance = np.mean(distances)
                        distance_std = np.std(distances)
                        
                        print(f"    平均距离: {avg_distance:.2f}")
                        print(f"    距离标准差: {distance_std:.3f}")
                        
                        # 如果标准差很小，说明点都在圆弧上
                        if distance_std < 0.1:
                            print(f"    ✅ 点分布验证通过")
                            success_count += 1
                        else:
                            print(f"    ⚠️ 点分布验证失败")
                    else:
                        print(f"    ⚠️ 采样点数量不足")
                else:
                    print(f"  ❌ 重构失败: 类型不正确")
                    
            except Exception as e:
                print(f"  ❌ 重构异常: {e}")
        
        print(f"\n圆弧重构测试结果: {success_count}/{len(test_arcs)} 通过")
        return success_count == len(test_arcs)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ellipse_reconstruction():
    """测试椭圆重构"""
    print("\n=== 测试椭圆重构 ===")
    
    try:
        from arc_ellipse_reconstructor import ArcEllipseReconstructor
        
        # 创建重构器
        reconstructor = ArcEllipseReconstructor(sample_points_count=40)
        
        # 测试用例：各种复杂的椭圆
        test_ellipses = [
            {
                'name': '正常椭圆',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (0, 0),
                    'major_axis': (15, 0),
                    'ratio': 0.6,
                    'start_param': 0,
                    'end_param': 2 * np.pi,
                    'layer': 'test'
                }
            },
            {
                'name': '椭圆弧',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (0, 0),
                    'major_axis': (12, 8),
                    'ratio': 0.7,
                    'start_param': 0,
                    'end_param': np.pi,
                    'layer': 'test'
                }
            },
            {
                'name': '镜像椭圆',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (0, 0),
                    'major_axis': (10, 5),
                    'ratio': 0.8,
                    'start_param': 0,
                    'end_param': 1.5 * np.pi,
                    'scale_x': 1.0,
                    'scale_y': -1.0,
                    'layer': 'test'
                }
            },
            {
                'name': '旋转椭圆',
                'entity': {
                    'type': 'ELLIPSE',
                    'center': (0, 0),
                    'major_axis': (8, 12),  # 45度旋转
                    'ratio': 0.5,
                    'start_param': 0,
                    'end_param': 2 * np.pi,
                    'layer': 'test'
                }
            }
        ]
        
        success_count = 0
        
        for test_case in test_ellipses:
            print(f"\n🔄 测试: {test_case['name']}")
            
            try:
                # 重构椭圆
                reconstructed = reconstructor.reconstruct_ellipse(test_case['entity'])
                
                # 验证结果
                if reconstructed['type'] == 'POLYLINE':
                    points = np.array(reconstructed['points'])
                    info = reconstructed['reconstruction_info']
                    
                    print(f"  ✅ 重构成功")
                    print(f"    原始类型: {reconstructed['original_type']}")
                    print(f"    新类型: {reconstructed['type']}")
                    print(f"    采样点数: {len(points)}")
                    print(f"    中心点: {info['center']}")
                    print(f"    长轴: {info['a']:.2f}")
                    print(f"    短轴: {info['b']:.2f}")
                    print(f"    旋转角度: {np.degrees(info['angle']):.1f}°")
                    print(f"    参数范围: {info['start_param']:.2f} - {info['end_param']:.2f}")
                    
                    # 验证点的合理性
                    if len(points) >= 3:
                        # 计算椭圆的边界框
                        min_x, max_x = np.min(points[:, 0]), np.max(points[:, 0])
                        min_y, max_y = np.min(points[:, 1]), np.max(points[:, 1])
                        
                        print(f"    X范围: [{min_x:.2f}, {max_x:.2f}]")
                        print(f"    Y范围: [{min_y:.2f}, {max_y:.2f}]")
                        
                        # 简单验证：点应该在合理范围内
                        center = np.array(info['center'])
                        max_distance = np.max(np.linalg.norm(points - center, axis=1))
                        expected_max = max(info['a'], info['b']) * 1.1  # 允许10%误差
                        
                        if max_distance <= expected_max:
                            print(f"    ✅ 点分布验证通过")
                            success_count += 1
                        else:
                            print(f"    ⚠️ 点分布验证失败: 最大距离{max_distance:.2f} > 预期{expected_max:.2f}")
                    else:
                        print(f"    ⚠️ 采样点数量不足")
                else:
                    print(f"  ❌ 重构失败: 类型不正确")
                    
            except Exception as e:
                print(f"  ❌ 重构异常: {e}")
        
        print(f"\n椭圆重构测试结果: {success_count}/{len(test_ellipses)} 通过")
        return success_count == len(test_ellipses)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_workflow():
    """测试集成工作流程"""
    print("\n=== 测试集成工作流程 ===")
    
    try:
        from arc_ellipse_reconstructor import ArcEllipseReconstructor
        
        # 创建重构器
        reconstructor = ArcEllipseReconstructor(sample_points_count=25)
        
        # 模拟混合实体列表
        mixed_entities = [
            {
                'type': 'LINE',
                'points': [(0, 0), (10, 10)],
                'layer': 'lines'
            },
            {
                'type': 'ARC',
                'center': (20, 20),
                'radius': 5,
                'start_angle': 45,
                'end_angle': 135,
                'scale_x': -1.0,
                'scale_y': 1.0,
                'layer': 'arcs'
            },
            {
                'type': 'CIRCLE',
                'center': (30, 30),
                'radius': 8,
                'layer': 'circles'
            },
            {
                'type': 'ELLIPSE',
                'center': (40, 40),
                'major_axis': (6, 4),
                'ratio': 0.75,
                'start_param': 0,
                'end_param': 1.5 * np.pi,
                'layer': 'ellipses'
            }
        ]
        
        print(f"原始实体列表:")
        for i, entity in enumerate(mixed_entities):
            print(f"  {i+1}. {entity['type']}")
        
        # 执行重构
        reconstructed_entities = reconstructor.reconstruct_entities(mixed_entities)
        
        print(f"\n重构后实体列表:")
        arc_count = 0
        ellipse_count = 0
        unchanged_count = 0
        
        for i, entity in enumerate(reconstructed_entities):
            print(f"  {i+1}. {entity['type']}", end="")
            if 'original_type' in entity:
                print(f" (原始: {entity['original_type']})")
                if entity['original_type'] == 'ARC':
                    arc_count += 1
                elif entity['original_type'] == 'ELLIPSE':
                    ellipse_count += 1
            else:
                print(f" (未改变)")
                unchanged_count += 1
        
        print(f"\n重构统计:")
        print(f"  圆弧重构: {arc_count}")
        print(f"  椭圆重构: {ellipse_count}")
        print(f"  保持不变: {unchanged_count}")
        print(f"  总计: {len(reconstructed_entities)}")
        
        # 验证结果
        success = (
            len(reconstructed_entities) == len(mixed_entities) and
            arc_count == 1 and
            ellipse_count == 1 and
            unchanged_count == 2
        )
        
        print(f"✅ 集成工作流程测试: {'通过' if success else '失败'}")
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("圆弧和椭圆重构方案测试")
    print("=" * 50)
    print("目标: 验证基于完整图形重构的可行性")
    print()
    
    tests = [
        ("圆弧重构", test_arc_reconstruction),
        ("椭圆重构", test_ellipse_reconstruction),
        ("集成工作流程", test_integration_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！圆弧椭圆重构方案完全可行。")
        print("\n📋 方案优势:")
        print("✅ 基于数学重构，精度高")
        print("✅ 转换为多段线，兼容性好")
        print("✅ 保留原始数据，可追溯")
        print("✅ 集成简单，易于实现")
        
        print("\n🎯 实现建议:")
        print("• 在数据加载阶段进行重构")
        print("• 采样点数量可根据精度需求调整")
        print("• 可以添加缓存机制提高性能")
        print("• 建议保留原始数据用于调试")
    else:
        print("⚠️ 部分测试失败，需要进一步优化。")

if __name__ == "__main__":
    main()
