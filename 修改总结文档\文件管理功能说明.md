# 文件管理功能说明

## 功能概述

在CAD分类标注工具中新增了文件管理功能，支持多文件处理、状态跟踪、数据保存和读取等功能。

## 新增功能

### 1. 文件选择下拉菜单

**位置：** 处理状态区域下方

**功能：**
- 显示文件夹中的所有CAD文件
- 实时显示每个文件的处理状态和标注状态
- 当前处理的文件名标红显示
- 支持选择已完成处理的文件进行切换

**显示格式：**
- 当前文件：`[文件名] (处理状态, 标注状态)`
- 其他文件：`文件名 (处理状态, 标注状态)`

**状态说明：**
- 处理状态：未处理、处理中、已完成
- 标注状态：未标注、标注未完成、标注完成

### 2. 保存和读取按钮

**位置：** 文件选择下拉菜单右侧

**保存功能：**
- 检查所有文件是否已处理完成
- 保存所有文件的处理数据和标注数据
- 保存文件状态信息
- 生成JSON格式的数据文件

**读取功能：**
- 读取之前保存的数据文件
- 恢复所有文件的处理状态
- 恢复当前文件的数据和界面状态

### 3. 后台处理功能

**工作原理：**
- 第一个文件在前台处理，用户可以进行交互操作
- 其他文件在后台自动处理分组检测
- 后台处理不影响界面操作
- 处理完成后自动更新文件状态

**状态管理：**
- 实时更新文件处理进度
- 自动检测标注完成度
- 支持中途停止后台处理

### 4. 数据暂存功能

**自动暂存：**
- 切换文件时自动保存当前文件数据
- 处理完成时自动暂存结果
- 标注操作实时更新状态

**数据恢复：**
- 切换回已处理文件时自动恢复数据
- 支持继续未完成的标注工作
- 保持界面状态一致性

## 使用流程

### 1. 基本使用流程

1. **选择文件夹**
   - 点击"选择文件夹"按钮
   - 系统自动扫描CAD文件
   - 初始化文件状态管理

2. **开始处理**
   - 点击"开始处理"按钮
   - 第一个文件开始前台处理
   - 其他文件自动后台处理

3. **文件切换**
   - 第一个文件分组完成后可进行其他操作
   - 通过下拉菜单选择其他已完成的文件
   - 系统自动切换并恢复数据

4. **数据保存**
   - 所有文件处理完成后点击"保存"
   - 系统保存所有数据到JSON文件
   - 下次可通过"读取"恢复数据

### 2. 高级使用场景

**场景1：中途暂停**
- 可以随时停止处理
- 已完成的文件数据会被保存
- 下次启动时可继续处理

**场景2：分批标注**
- 可以先处理所有文件的分组
- 然后分批进行标注工作
- 每个文件的标注进度独立跟踪

**场景3：数据备份**
- 定期保存数据避免丢失
- 支持在不同时间点恢复数据
- 便于团队协作和数据共享

## 技术实现

### 1. 数据结构

**文件状态结构：**
```python
file_status = {
    'filename.dxf': {
        'processing_status': 'completed',  # unprocessed/processing/completed
        'annotation_status': 'incomplete'  # unannotated/incomplete/completed
    }
}
```

**文件数据结构：**
```python
file_data = {
    'filename.dxf': {
        'entities': [],           # 实体数据
        'groups': [],            # 分组数据
        'labeled_entities': [],  # 已标注实体
        'dataset': [],           # 数据集
        'groups_info': [],       # 组状态信息
        'group_fill_status': {}, # 填充状态
        'timestamp': '2024-01-01T12:00:00'
    }
}
```

**保存数据结构：**
```python
save_data = {
    'folder': '/path/to/folder',
    'files': ['file1.dxf', 'file2.dxf'],
    'file_status': file_status,
    'file_data': file_data,
    'save_time': '2024-01-01T12:00:00',
    'version': '1.0'
}
```

### 2. 核心方法

**文件管理方法：**
- `_scan_folder_files()`: 扫描文件夹中的CAD文件
- `update_file_combo()`: 更新文件选择下拉菜单
- `switch_to_file()`: 切换到指定文件
- `_save_current_file_data()`: 保存当前文件数据
- `_load_file_data()`: 加载文件数据

**后台处理方法：**
- `_start_background_processing()`: 启动后台处理
- `_start_file_processing()`: 开始处理指定文件

**数据保存方法：**
- `save_all_data()`: 保存所有数据
- `load_all_data()`: 读取所有数据

### 3. 界面集成

**重写的方法：**
- `_create_status_display()`: 创建状态显示区域
- `start_processing()`: 开始处理方法
- `on_status_update()`: 状态更新回调
- `stop_processing()`: 停止处理方法
- `update_stats()`: 更新统计信息

## 注意事项

### 1. 性能考虑

- 后台处理使用独立线程，不阻塞界面
- 大文件处理时会有适当的延迟
- 内存使用会随文件数量增加

### 2. 数据安全

- 自动保存机制防止数据丢失
- JSON格式便于数据恢复和迁移
- 支持版本控制和兼容性检查

### 3. 使用限制

- 只能切换到已完成处理的文件
- 保存功能要求所有文件都已处理完成
- 后台处理期间建议不要关闭程序

## 测试验证

**测试文件：** `test_file_management.py`

**测试内容：**
- 文件管理功能初始化
- 文件状态显示格式
- 数据结构验证
- 界面交互测试

**运行测试：**
```bash
python test_file_management.py
```

## 总结

新增的文件管理功能大大提升了工具的实用性和效率：

✅ **多文件支持** - 一次处理整个文件夹的CAD文件
✅ **后台处理** - 不影响用户操作的同时处理其他文件
✅ **状态跟踪** - 实时显示每个文件的处理和标注状态
✅ **数据暂存** - 自动保存和恢复文件数据
✅ **灵活切换** - 支持在已完成的文件间自由切换
✅ **数据备份** - 完整的保存和读取功能

这些功能使得CAD分类标注工具更适合批量处理和团队协作场景。
