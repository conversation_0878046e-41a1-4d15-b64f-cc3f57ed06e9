#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重新分类后跳转修复
"""

import sys
import os

def test_relabel_jump_logic():
    """测试重新分类后的跳转逻辑"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 创建模拟数据：自动标注组 + 待处理组
        auto_group1 = [
            {'type': 'LINE', 'layer': 'wall', 'points': [(0, 0), (10, 10)], 'auto_labeled': True, 'label': 'wall'}
        ]
        auto_group2 = [
            {'type': 'LINE', 'layer': 'door', 'points': [(20, 20), (30, 30)], 'auto_labeled': True, 'label': 'door_window'}
        ]
        
        # 待处理的手动组（未标注）
        manual_group1 = [
            {'type': 'LINE', 'layer': 'furniture', 'points': [(40, 40), (50, 50)]}
        ]
        manual_group2 = [
            {'type': 'LINE', 'layer': 'other', 'points': [(60, 60), (70, 70)]}
        ]
        manual_group3 = [
            {'type': 'LINE', 'layer': 'appliance', 'points': [(80, 80), (90, 90)]}
        ]
        
        # 设置处理器状态
        processor.all_groups = [auto_group1, auto_group2, manual_group1, manual_group2, manual_group3]
        processor.current_file = "test.dxf"
        processor.dataset = []
        processor.labeled_entities = []
        processor.auto_labeled_entities = auto_group1 + auto_group2
        
        # 初始化组状态
        processor._update_groups_info()
        processor._update_pending_manual_groups()
        
        print(f"初始状态:")
        print(f"  总组数: {len(processor.all_groups)}")
        print(f"  自动标注组: {len([g for g in processor.all_groups if any(e.get('auto_labeled', False) for e in g)])}")
        print(f"  待处理手动组: {len(processor.pending_manual_groups)}")
        
        # 模拟重新分类第一个自动标注组
        print(f"\n重新分类第1组（自动标注组）...")
        
        # 设置状态回调来捕获跳转信息
        jump_info = []
        def status_callback(status_type, data):
            if status_type == "auto_jump":
                jump_info.append(data)
            print(f"状态回调: {status_type} - {data}")
        
        processor.set_callbacks(status_callback, None)
        
        # 执行重新分类
        result = processor.relabel_group(1, 'furniture')
        
        if result:
            print(f"✓ 重新分类成功")
            
            # 检查跳转结果
            if jump_info:
                jump_message = jump_info[0]
                print(f"✓ 跳转信息: {jump_message}")
                
                # 验证是否跳转到第一个待处理组（应该是组3）
                if "第一个待处理组3" in jump_message or "待处理组3" in jump_message:
                    print(f"✅ 跳转正确：跳转到第一个待处理组")
                    return True
                else:
                    print(f"❌ 跳转错误：{jump_message}")
                    return False
            else:
                print(f"❌ 没有捕获到跳转信息")
                return False
        else:
            print(f"❌ 重新分类失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_next_unlabeled_group():
    """测试获取下一个未标注组的逻辑"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 创建模拟数据
        auto_group = [
            {'type': 'LINE', 'layer': 'wall', 'points': [(0, 0), (10, 10)], 'auto_labeled': True, 'label': 'wall'}
        ]
        manual_group1 = [
            {'type': 'LINE', 'layer': 'furniture', 'points': [(20, 20), (30, 30)]}
        ]
        manual_group2 = [
            {'type': 'LINE', 'layer': 'other', 'points': [(40, 40), (50, 50)]}
        ]
        
        processor.all_groups = [auto_group, manual_group1, manual_group2]
        processor._update_groups_info()
        processor._update_pending_manual_groups()
        
        print(f"\n测试获取下一个未标注组:")
        print(f"  待处理组数: {len(processor.pending_manual_groups)}")
        
        next_group = processor.get_next_unlabeled_group()
        print(f"  下一个未标注组索引: {next_group}")
        
        # 应该返回2（第一个待处理组）
        if next_group == 2:
            print(f"✅ 获取下一个未标注组正确")
            return True
        else:
            print(f"❌ 获取下一个未标注组错误，期望2，实际{next_group}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pending_manual_groups_update():
    """测试待处理手动组更新逻辑"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 创建不同状态的组
        auto_group = [
            {'type': 'LINE', 'layer': 'wall', 'points': [(0, 0), (10, 10)], 'auto_labeled': True, 'label': 'wall'}
        ]
        labeled_group = [
            {'type': 'LINE', 'layer': 'door', 'points': [(20, 20), (30, 30)], 'label': 'door_window'}
        ]
        unlabeled_group1 = [
            {'type': 'LINE', 'layer': 'furniture', 'points': [(40, 40), (50, 50)]}
        ]
        unlabeled_group2 = [
            {'type': 'LINE', 'layer': 'other', 'points': [(60, 60), (70, 70)]}
        ]
        
        processor.all_groups = [auto_group, labeled_group, unlabeled_group1, unlabeled_group2]
        processor._update_pending_manual_groups()
        
        print(f"\n测试待处理手动组更新:")
        print(f"  总组数: {len(processor.all_groups)}")
        print(f"  待处理组数: {len(processor.pending_manual_groups)}")
        
        # 应该有2个待处理组（unlabeled_group1和unlabeled_group2）
        if len(processor.pending_manual_groups) == 2:
            print(f"✅ 待处理组数量正确")
            
            # 检查第一个待处理组是否是unlabeled_group1
            if processor.pending_manual_groups[0] == unlabeled_group1:
                print(f"✅ 第一个待处理组正确")
                return True
            else:
                print(f"❌ 第一个待处理组错误")
                return False
        else:
            print(f"❌ 待处理组数量错误，期望2，实际{len(processor.pending_manual_groups)}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试重新分类后跳转修复...")
    print("=" * 60)
    
    tests = [
        test_pending_manual_groups_update,
        test_get_next_unlabeled_group,
        test_relabel_jump_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重新分类跳转修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
