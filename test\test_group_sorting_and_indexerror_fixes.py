#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试组排序和IndexError修复效果
验证：
1. IndexError修复（_update_groups_info方法）
2. 组列表排序显示（未标注组优先）
3. 完成状态的正确处理
"""

import os
import sys
import tkinter as tk
from unittest.mock import Mock, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_indexerror_fixes():
    """测试IndexError修复"""
    print("=" * 60)
    print("测试IndexError修复")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置测试数据
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],  # 已标注
            [{'type': 'CIRCLE'}],  # 未标注
            [{'type': 'ARC'}],  # 未标注
        ]
        
        processor.groups_info = []
        processor.manual_grouping_mode = True
        processor.pending_manual_groups = [[{'type': 'CIRCLE'}], [{'type': 'ARC'}]]
        processor.current_manual_group_index = 0
        
        print("\n🔍 测试1: 正常情况下的_update_groups_info")
        try:
            processor._update_groups_info()
            print("✅ 正常情况处理成功")
            print(f"  生成的groups_info数量: {len(processor.groups_info)}")
        except Exception as e:
            print(f"❌ 正常情况处理失败: {e}")
        
        print("\n🔍 测试2: 索引超出范围的情况")
        processor.current_manual_group_index = 10  # 超出范围
        
        try:
            processor._update_groups_info()
            print("✅ 索引超出范围处理成功，没有IndexError")
            print(f"  当前索引已重置为: {processor.current_manual_group_index}")
        except IndexError as e:
            print(f"❌ 仍然出现IndexError: {e}")
        except Exception as e:
            print(f"✅ 其他异常（可能正常）: {e}")
        
        print("\n🔍 测试3: pending_manual_groups为空的情况")
        processor.pending_manual_groups = []
        processor.current_manual_group_index = 0
        
        try:
            processor._update_groups_info()
            print("✅ 空pending_manual_groups处理成功")
        except Exception as e:
            print(f"❌ 空pending_manual_groups处理失败: {e}")
        
        print("\n🔍 测试4: label_current_group方法的安全调用")
        processor.pending_manual_groups = [[{'type': 'CIRCLE'}]]
        processor.current_manual_group_index = 0
        
        try:
            # 模拟标注操作
            success = processor.label_current_group('wall')
            print(f"✅ label_current_group执行成功: {success}")
        except Exception as e:
            print(f"❌ label_current_group执行失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_sorting():
    """测试组排序显示"""
    print("\n" + "=" * 60)
    print("测试组排序显示")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 创建测试数据：混合状态的组
        test_groups_info = [
            {'status': 'labeled', 'type': 'wall', 'entity_count': 5},      # 组1：已标注
            {'status': 'unlabeled', 'type': '', 'entity_count': 3},        # 组2：未标注
            {'status': 'auto_labeled', 'type': 'door', 'entity_count': 2}, # 组3：自动标注
            {'status': 'labeling', 'type': '', 'entity_count': 4},         # 组4：标注中
            {'status': 'unlabeled', 'type': '', 'entity_count': 6},        # 组5：未标注
        ]
        
        print("\n🔍 测试组排序逻辑")
        sorted_groups = app._sort_groups_for_display(test_groups_info)
        
        print("排序结果:")
        for i, (original_index, group_info) in enumerate(sorted_groups):
            status = group_info['status']
            print(f"  显示位置{i+1}: 组{original_index+1} ({status})")
        
        # 验证排序结果
        expected_order = [
            (3, 'labeling'),    # 组4：标注中（最高优先级）
            (1, 'unlabeled'),   # 组2：未标注
            (4, 'unlabeled'),   # 组5：未标注
            (0, 'labeled'),     # 组1：已标注
            (2, 'auto_labeled') # 组3：自动标注
        ]
        
        actual_order = [(original_index, group_info['status']) for original_index, group_info in sorted_groups]
        
        if actual_order == expected_order:
            print("✅ 组排序结果正确")
        else:
            print("❌ 组排序结果不正确")
            print(f"  期望: {expected_order}")
            print(f"  实际: {actual_order}")
        
        print("\n🔍 测试所有组都已标注的情况")
        all_labeled_groups = [
            {'status': 'labeled', 'type': 'wall', 'entity_count': 5},      # 组1
            {'status': 'auto_labeled', 'type': 'door', 'entity_count': 3}, # 组2
            {'status': 'labeled', 'type': 'window', 'entity_count': 2},    # 组3
        ]
        
        sorted_all_labeled = app._sort_groups_for_display(all_labeled_groups)
        first_group_index = sorted_all_labeled[0][0]
        
        if first_group_index == 0:  # 组1应该在第一位
            print("✅ 所有组已标注时，组1正确显示在第一行")
        else:
            print(f"❌ 所有组已标注时，组1没有在第一行，实际第一行是组{first_group_index+1}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_completion_state_handling():
    """测试完成状态处理"""
    print("\n" + "=" * 60)
    print("测试完成状态处理")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置完成状态的数据
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],
            [{'type': 'CIRCLE', 'label': 'door'}],
            [{'type': 'ARC', 'label': 'window'}],
        ]
        
        processor.groups_info = [
            {'status': 'labeled'},
            {'status': 'labeled'},
            {'status': 'labeled'},
        ]
        
        processor.pending_manual_groups = []
        processor.manual_grouping_mode = False
        processor.current_manual_group_index = 0
        
        print("\n🔍 测试完成状态检查")
        
        # 测试has_unlabeled_groups
        has_unlabeled = processor.has_unlabeled_groups()
        if not has_unlabeled:
            print("✅ 正确识别所有组都已标注")
        else:
            print(f"❌ 应该识别为已完成，但返回: {has_unlabeled}")
        
        # 测试get_next_unlabeled_group
        next_group = processor.get_next_unlabeled_group()
        if next_group is None:
            print("✅ 正确返回None（无未标注组）")
        else:
            print(f"❌ 应该返回None，但返回: {next_group}")
        
        print("\n🔍 测试异常数据的完成状态处理")
        
        # 设置异常数据
        processor.groups_info = None
        processor.all_groups = None
        
        try:
            has_unlabeled = processor.has_unlabeled_groups()
            next_group = processor.get_next_unlabeled_group()
            print(f"✅ 异常数据处理成功: has_unlabeled={has_unlabeled}, next_group={next_group}")
        except Exception as e:
            print(f"❌ 异常数据处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_scenario():
    """测试集成场景：从标注到完成的完整流程"""
    print("\n" + "=" * 60)
    print("测试集成场景")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置初始状态：有一些已标注，有一些未标注
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],  # 已标注
            [{'type': 'CIRCLE'}],  # 未标注
            [{'type': 'ARC'}],     # 未标注
        ]
        
        processor.pending_manual_groups = [
            [{'type': 'CIRCLE'}],
            [{'type': 'ARC'}]
        ]
        processor.manual_grouping_mode = True
        processor.current_manual_group_index = 0
        
        print("\n🔍 步骤1: 初始状态更新")
        processor._update_groups_info()
        print(f"  初始groups_info数量: {len(processor.groups_info)}")
        
        print("\n🔍 步骤2: 标注第一个未标注组")
        success = processor.label_current_group('door')
        print(f"  标注结果: {success}")
        
        print("\n🔍 步骤3: 标注最后一个未标注组")
        if processor.pending_manual_groups and processor.current_manual_group_index < len(processor.pending_manual_groups):
            success = processor.label_current_group('window')
            print(f"  最后标注结果: {success}")
        
        print("\n🔍 步骤4: 检查完成状态")
        has_unlabeled = processor.has_unlabeled_groups()
        print(f"  是否还有未标注组: {has_unlabeled}")
        
        if not has_unlabeled:
            print("✅ 集成测试成功：完整流程正常工作")
        else:
            print("⚠️ 集成测试部分成功：流程工作但可能有遗留问题")
        
        return True
        
    except Exception as e:
        print(f"集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试组排序和IndexError修复效果...")
    
    test1 = test_indexerror_fixes()
    test2 = test_group_sorting()
    test3 = test_completion_state_handling()
    test4 = test_integration_scenario()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = {
        "IndexError修复": test1,
        "组排序显示": test2,
        "完成状态处理": test3,
        "集成场景测试": test4,
    }
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！组排序和IndexError修复成功。")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码。")
