#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据类型修复功能
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_type_issues():
    """测试数据类型问题和修复"""
    print("=== 测试数据类型问题和修复 ===")
    
    # 模拟序列化后的问题数据
    print("\n1. 模拟序列化后的问题数据:")
    
    # 这些是从错误日志中看到的问题数据类型
    problem_data = {
        'entities': [
            {
                'type': 'ELLIPSE',
                'center': "np.str_('(-620.4862004912079, 620.4862004912079)')",  # 字符串化的numpy数据
                'major_axis': ['(-620.4862004912079)', '(620.4862004912079)'],  # 字符串化的数值
                'ratio': '0.5',  # 字符串化的数值
                'points': [['100.0', '200.0'], ['300.0', '400.0']]  # 字符串化的坐标
            },
            {
                'type': 'LINE',
                'points': [['0', '0'], ['100', '100']],  # 字符串化的坐标
                'def_points': [['0.0', '0.0'], ['100.0', '100.0']]
            }
        ]
    }
    
    for i, entity in enumerate(problem_data['entities']):
        print(f"  实体{i+1}: {entity['type']}")
        for key, value in entity.items():
            if key != 'type':
                print(f"    {key}: {value} (类型: {type(value).__name__})")
    
    # 测试numpy计算失败的情况
    print("\n2. 测试numpy计算失败的情况:")
    
    try:
        # 模拟原始错误
        bad_points = [['100.0', '200.0'], ['300.0', '400.0']]  # 字符串类型
        points_array = np.array(bad_points)
        print(f"  字符串数组: {points_array}")
        print(f"  数组dtype: {points_array.dtype}")
        
        # 这会失败
        centroid = np.mean(points_array, axis=0)
        print(f"  质心计算结果: {centroid}")
    except Exception as e:
        print(f"  ❌ numpy计算失败: {e}")
    
    # 测试修复方法
    print("\n3. 测试数据类型修复:")
    
    def fix_entity_data_types(entity):
        """修复实体数据中的数值类型问题"""
        if not isinstance(entity, dict):
            return entity
        
        # 需要确保为数值类型的字段
        numeric_fields = ['center', 'position', 'points', 'def_points', 'major_axis', 'ratio', 'radius']
        
        for field in numeric_fields:
            if field in entity and entity[field] is not None:
                try:
                    if field in ['center', 'position']:
                        # 坐标点：确保为数值元组
                        if isinstance(entity[field], (list, tuple)) and len(entity[field]) >= 2:
                            entity[field] = [float(x) for x in entity[field][:2]]
                        elif isinstance(entity[field], str):
                            # 处理字符串化的numpy数据
                            import re
                            numbers = re.findall(r'-?\d+\.?\d*', entity[field])
                            if len(numbers) >= 2:
                                entity[field] = [float(numbers[0]), float(numbers[1])]
                    elif field in ['points', 'def_points']:
                        # 点列表：确保每个点都是数值
                        if isinstance(entity[field], list):
                            fixed_points = []
                            for point in entity[field]:
                                if isinstance(point, (list, tuple)) and len(point) >= 2:
                                    fixed_points.append([float(x) for x in point[:2]])
                                elif point is not None:
                                    fixed_points.append(point)
                            entity[field] = fixed_points
                    elif field == 'major_axis':
                        # 主轴：确保为数值列表
                        if isinstance(entity[field], (list, tuple)) and len(entity[field]) >= 2:
                            entity[field] = [float(x) for x in entity[field][:2]]
                    elif field in ['ratio', 'radius']:
                        # 单个数值：确保为浮点数
                        entity[field] = float(entity[field])
                except (ValueError, TypeError) as e:
                    print(f"⚠️ 修复实体字段 {field} 失败: {e}")
                    # 保持原值，避免程序崩溃
                    pass
        
        return entity
    
    # 修复问题数据
    fixed_entities = []
    for entity in problem_data['entities']:
        fixed_entity = fix_entity_data_types(entity.copy())
        fixed_entities.append(fixed_entity)
    
    print("  修复后的数据:")
    for i, entity in enumerate(fixed_entities):
        print(f"  实体{i+1}: {entity['type']}")
        for key, value in entity.items():
            if key != 'type':
                print(f"    {key}: {value} (类型: {type(value).__name__})")
    
    # 测试修复后的numpy计算
    print("\n4. 测试修复后的numpy计算:")
    
    for i, entity in enumerate(fixed_entities):
        if 'points' in entity and entity['points']:
            try:
                points = entity['points']
                print(f"  实体{i+1}的points: {points}")
                
                # 确保points是数值类型的列表
                if isinstance(points, list) and len(points) > 0:
                    numeric_points = []
                    for point in points:
                        if isinstance(point, (list, tuple)) and len(point) >= 2:
                            numeric_point = [float(point[0]), float(point[1])]
                            numeric_points.append(numeric_point)
                    
                    if numeric_points:
                        points_array = np.array(numeric_points, dtype=float)
                        print(f"    数组: {points_array}")
                        print(f"    数组dtype: {points_array.dtype}")
                        
                        centroid = np.mean(points_array, axis=0)
                        print(f"    ✅ 质心计算成功: {centroid}")
                        
            except Exception as e:
                print(f"    ❌ 质心计算失败: {e}")
    
    print("\n=== 测试结果 ===")
    print("✅ 数据类型修复方法可以正确处理序列化后的数据")
    print("✅ 修复后的数据可以正常进行numpy计算")
    print("✅ 错误处理机制可以防止程序崩溃")
    
    print("\n🎯 修复要点:")
    print("1. 序列化时保持数值类型，避免转换为字符串")
    print("2. 反序列化时验证和修复数据类型")
    print("3. 可视化器中添加数据类型检查")
    print("4. 界面更新时添加错误处理，避免中断流程")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_data_type_issues()
