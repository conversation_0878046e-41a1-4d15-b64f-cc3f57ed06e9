#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试圆弧镜像修复效果
"""

import sys
import os
import math

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_arc(center_x=0, center_y=0, radius=1, start_angle=0, end_angle=90):
    """创建测试圆弧"""
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    class MockDxf:
        def __init__(self):
            self.center = MockPoint(center_x, center_y)
            self.radius = radius
            self.start_angle = start_angle
            self.end_angle = end_angle
    
    class MockArc:
        def __init__(self):
            self.dxf = MockDxf()
        
        def dxftype(self):
            return 'ARC'
    
    return MockArc()

def test_mirror_transformations():
    """测试镜像变换的准确性"""
    print("=== 测试镜像变换准确性 ===")
    
    from cad_data_processor import CADDataProcessor, TransformationMatrix
    
    processor = CADDataProcessor()
    
    # 测试用例：不同的圆弧配置
    test_arcs = [
        {
            'name': '第一象限圆弧 (0°-90°)',
            'arc': create_test_arc(0, 0, 1, 0, 90),
            'expected_angles': {
                'original': (0, 90),
                'x_mirror': (270, 360),  # X轴镜像后应该是270°-360°
                'y_mirror': (90, 180),   # Y轴镜像后应该是90°-180°
            }
        },
        {
            'name': '第二象限圆弧 (90°-180°)',
            'arc': create_test_arc(0, 0, 1, 90, 180),
            'expected_angles': {
                'original': (90, 180),
                'x_mirror': (180, 270),  # X轴镜像
                'y_mirror': (0, 90),     # Y轴镜像
            }
        },
        {
            'name': '跨象限圆弧 (45°-135°)',
            'arc': create_test_arc(0, 0, 1, 45, 135),
            'expected_angles': {
                'original': (45, 135),
                'x_mirror': (225, 315),  # X轴镜像
                'y_mirror': (45, 135),   # Y轴镜像（对称）
            }
        },
        {
            'name': '小角度圆弧 (0°-30°)',
            'arc': create_test_arc(0, 0, 1, 0, 30),
            'expected_angles': {
                'original': (0, 30),
                'x_mirror': (330, 360),  # X轴镜像
                'y_mirror': (150, 180),  # Y轴镜像
            }
        }
    ]
    
    # 测试变换类型
    transformations = [
        {
            'name': 'X轴镜像',
            'scale': (1.0, -1.0),
            'key': 'x_mirror'
        },
        {
            'name': 'Y轴镜像',
            'scale': (-1.0, 1.0),
            'key': 'y_mirror'
        }
    ]
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    success_count = 0
    total_count = 0
    
    for arc_info in test_arcs:
        print(f"\n🔵 {arc_info['name']}")
        arc = arc_info['arc']
        
        for transform in transformations:
            total_count += 1
            print(f"\n  📐 {transform['name']}")
            
            try:
                # 创建变换矩阵
                matrix = TransformationMatrix(base_point, 0, transform['scale'])
                
                # 应用变换
                result = processor._transform_arc(arc, matrix)
                
                if result and result['type'] == 'ARC':
                    actual_start = result['start_angle']
                    actual_end = result['end_angle']
                    
                    # 获取期望角度
                    expected_key = transform['key']
                    if expected_key in arc_info['expected_angles']:
                        expected_start, expected_end = arc_info['expected_angles'][expected_key]
                        
                        # 角度容差
                        tolerance = 5.0  # 5度容差
                        
                        # 检查角度是否在期望范围内
                        start_ok = abs(actual_start - expected_start) <= tolerance or abs(actual_start - expected_start - 360) <= tolerance
                        end_ok = abs(actual_end - expected_end) <= tolerance or abs(actual_end - expected_end - 360) <= tolerance
                        
                        if start_ok and end_ok:
                            success_count += 1
                            print(f"    ✅ 角度正确: {actual_start:.1f}° - {actual_end:.1f}°")
                            print(f"       期望: {expected_start:.1f}° - {expected_end:.1f}°")
                        else:
                            print(f"    ❌ 角度错误: {actual_start:.1f}° - {actual_end:.1f}°")
                            print(f"       期望: {expected_start:.1f}° - {expected_end:.1f}°")
                            print(f"       误差: 起始角度 {abs(actual_start - expected_start):.1f}°, 结束角度 {abs(actual_end - expected_end):.1f}°")
                    else:
                        print(f"    ⚠️ 没有期望角度数据")
                else:
                    print(f"    ❌ 变换失败或类型错误")
                    
            except Exception as e:
                print(f"    💥 变换异常: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"镜像变换测试结果: {success_count}/{total_count} 通过 ({success_count/total_count*100:.1f}%)")
    
    return success_count, total_count

def test_geometric_consistency():
    """测试几何一致性"""
    print("\n=== 测试几何一致性 ===")
    
    from cad_data_processor import CADDataProcessor, TransformationMatrix
    
    processor = CADDataProcessor()
    
    # 创建测试圆弧
    arc = create_test_arc(0, 0, 1, 0, 90)
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    # 测试双重镜像应该回到原始状态
    print("测试双重镜像一致性...")
    
    # 第一次X轴镜像
    matrix1 = TransformationMatrix(base_point, 0, (1.0, -1.0))
    result1 = processor._transform_arc(arc, matrix1)
    
    if result1:
        print(f"第一次X轴镜像: {result1['start_angle']:.1f}° - {result1['end_angle']:.1f}°")
        
        # 创建第二次镜像的圆弧
        arc2 = create_test_arc(
            result1['center'][0], result1['center'][1], result1['radius'],
            result1['start_angle'], result1['end_angle']
        )
        
        # 第二次X轴镜像
        matrix2 = TransformationMatrix(base_point, 0, (1.0, -1.0))
        result2 = processor._transform_arc(arc2, matrix2)
        
        if result2:
            print(f"第二次X轴镜像: {result2['start_angle']:.1f}° - {result2['end_angle']:.1f}°")
            
            # 检查是否回到原始状态
            original_start = 0
            original_end = 90
            
            start_diff = min(abs(result2['start_angle'] - original_start), 
                           abs(result2['start_angle'] - original_start - 360),
                           abs(result2['start_angle'] - original_start + 360))
            end_diff = min(abs(result2['end_angle'] - original_end),
                         abs(result2['end_angle'] - original_end - 360),
                         abs(result2['end_angle'] - original_end + 360))
            
            if start_diff <= 5 and end_diff <= 5:
                print("✅ 双重镜像一致性测试通过")
                return True
            else:
                print(f"❌ 双重镜像一致性测试失败，角度差: {start_diff:.1f}°, {end_diff:.1f}°")
                return False
        else:
            print("❌ 第二次镜像失败")
            return False
    else:
        print("❌ 第一次镜像失败")
        return False

def main():
    """主测试函数"""
    print("圆弧镜像修复测试")
    print("=" * 50)
    print("目标: 验证镜像后的圆弧显示是否准确")
    print("问题: 镜像后圆弧显示为旋转180度的弧线")
    print()
    
    try:
        # 测试镜像变换准确性
        success, total = test_mirror_transformations()
        
        # 测试几何一致性
        consistency_ok = test_geometric_consistency()
        
        print(f"\n" + "=" * 50)
        print("测试总结:")
        print(f"镜像变换准确性: {success}/{total} ({success/total*100:.1f}%)")
        print(f"几何一致性: {'通过' if consistency_ok else '失败'}")
        
        if success == total and consistency_ok:
            print("\n🎉 所有测试通过！圆弧镜像问题已修复。")
            print("\n📋 修复效果:")
            print("✅ 镜像变换角度计算正确")
            print("✅ 几何一致性保持良好")
            print("✅ 各种圆弧配置都能正确处理")
            print("✅ 双重变换能回到原始状态")
        else:
            print("\n⚠️ 部分测试失败，需要进一步优化。")
            
            if success < total:
                print(f"镜像变换准确性需要改进: {total-success} 个测试失败")
            
            if not consistency_ok:
                print("几何一致性需要改进")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
