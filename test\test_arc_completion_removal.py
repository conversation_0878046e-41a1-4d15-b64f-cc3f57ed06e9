#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试圆弧椭圆完整化移除
验证代码中不再将圆弧视为完整圆、椭圆弧视为完整椭圆
"""

import sys
import os
import math

def test_arc_not_as_full_circle():
    """测试圆弧不被视为完整圆"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试圆弧不被视为完整圆:")
        
        processor = CADDataProcessor()
        
        # 创建90度圆弧（不是完整圆）
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,  # 只有90度
            'layer': 'test'
        }
        
        # 创建完整圆形作为对比
        test_circle = {
            'type': 'CIRCLE',
            'center': (100, 100),
            'radius': 50,
            'layer': 'test'
        }
        
        # 测试1: 圆弧包围盒应该只包含实际圆弧部分，不是完整圆
        print("\n1. 测试圆弧包围盒:")
        arc_bbox = processor._get_entity_bbox(test_arc)
        circle_bbox = processor._get_entity_bbox(test_circle)
        
        print(f"圆弧包围盒: {arc_bbox}")
        print(f"圆形包围盒: {circle_bbox}")
        
        # 90度圆弧应该只覆盖第一象限，包围盒应该不同于完整圆
        if arc_bbox != circle_bbox:
            print("✓ 圆弧包围盒不同于完整圆（正确）")
        else:
            print("✗ 圆弧包围盒与完整圆相同（错误）")
            return False
        
        # 测试2: 圆弧到圆弧距离计算不应将圆弧视为完整圆
        print("\n2. 测试圆弧到圆弧距离:")
        
        # 创建两个不相交的90度圆弧
        arc1 = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,  # 第一象限
            'layer': 'test'
        }
        
        arc2 = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 180,
            'end_angle': 270,  # 第三象限
            'layer': 'test'
        }
        
        arc_to_arc_distance = processor._arc_to_arc_distance(arc1, arc2)
        print(f"圆弧到圆弧距离: {arc_to_arc_distance:.2f}")
        
        # 两个不相交的90度圆弧应该有一定距离，不应该是0
        if arc_to_arc_distance > 50:  # 应该大于半径
            print("✓ 圆弧到圆弧距离正确（使用采样点）")
        else:
            print(f"✗ 圆弧到圆弧距离可能将圆弧视为完整圆: {arc_to_arc_distance}")
            return False
        
        # 测试3: 圆形到圆弧距离计算不应将圆弧视为完整圆
        print("\n3. 测试圆形到圆弧距离:")
        
        # 创建远离圆弧的圆形
        distant_circle = {
            'type': 'CIRCLE',
            'center': (300, 300),
            'radius': 30,
            'layer': 'test'
        }
        
        circle_to_arc_distance = processor._circle_to_arc_distance(distant_circle, arc1)
        print(f"圆形到圆弧距离: {circle_to_arc_distance:.2f}")
        
        # 距离应该反映真实的几何关系
        if circle_to_arc_distance > 100:  # 应该是一个合理的距离
            print("✓ 圆形到圆弧距离正确（使用采样点）")
        else:
            print(f"✗ 圆形到圆弧距离可能有问题: {circle_to_arc_distance}")
            return False
        
        print("\n✓ 圆弧不被视为完整圆测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧完整化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ellipse_arc_not_as_full_ellipse():
    """测试椭圆弧不被视为完整椭圆"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试椭圆弧不被视为完整椭圆:")
        
        processor = CADDataProcessor()
        
        # 创建椭圆弧（不是完整椭圆）
        ellipse_arc = {
            'type': 'ELLIPSE',
            'center': (100, 100),
            'major_axis': (40, 0),
            'ratio': 0.5,
            'start_param': 0,
            'end_param': math.pi,  # 只有半个椭圆
            'layer': 'test'
        }
        
        # 创建完整椭圆作为对比
        full_ellipse = {
            'type': 'ELLIPSE',
            'center': (100, 100),
            'major_axis': (40, 0),
            'ratio': 0.5,
            'start_param': 0,
            'end_param': 2 * math.pi,  # 完整椭圆
            'layer': 'test'
        }
        
        # 测试1: 椭圆弧端点计算
        print("\n1. 测试椭圆弧端点:")
        arc_endpoints = processor._get_entity_endpoints(ellipse_arc)
        full_endpoints = processor._get_entity_endpoints(full_ellipse)
        
        print(f"椭圆弧端点数量: {len(arc_endpoints) if arc_endpoints else 0}")
        print(f"完整椭圆端点数量: {len(full_endpoints) if full_endpoints else 0}")
        
        # 椭圆弧应该有端点，完整椭圆应该没有端点
        if arc_endpoints and len(arc_endpoints) > 0 and (not full_endpoints or len(full_endpoints) == 0):
            print("✓ 椭圆弧端点计算正确（区分弧和完整椭圆）")
        else:
            print("✗ 椭圆弧端点计算可能有问题")
            return False
        
        # 测试2: 椭圆弧采样点
        print("\n2. 测试椭圆弧采样点:")
        arc_points = processor._sample_ellipse_points(ellipse_arc, num_points=16)
        full_points = processor._sample_ellipse_points(full_ellipse, num_points=16)
        
        print(f"椭圆弧采样点数量: {len(arc_points)}")
        print(f"完整椭圆采样点数量: {len(full_points)}")
        
        # 验证采样点分布是否正确
        if len(arc_points) == 16 and len(full_points) == 16:
            print("✓ 椭圆采样点数量正确")
        else:
            print(f"✗ 椭圆采样点数量异常")
            return False
        
        # 测试3: 椭圆弧包围盒
        print("\n3. 测试椭圆弧包围盒:")
        arc_bbox = processor._get_entity_bbox(ellipse_arc)
        full_bbox = processor._get_entity_bbox(full_ellipse)
        
        print(f"椭圆弧包围盒: {arc_bbox}")
        print(f"完整椭圆包围盒: {full_bbox}")
        
        # 半椭圆的包围盒应该不同于完整椭圆
        if arc_bbox and full_bbox and arc_bbox != full_bbox:
            print("✓ 椭圆弧包围盒不同于完整椭圆（正确）")
        else:
            print("✗ 椭圆弧包围盒与完整椭圆相同（可能有问题）")
            # 这个不一定是错误，因为半椭圆的包围盒可能与完整椭圆相同
            print("  注意：这可能是正常的，取决于椭圆弧的具体范围")
        
        print("\n✓ 椭圆弧不被视为完整椭圆测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 椭圆弧完整化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sampling_accuracy():
    """测试采样点方法的准确性"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试采样点方法准确性:")
        
        processor = CADDataProcessor()
        
        # 测试1: 圆弧采样点应该在圆弧上
        print("\n1. 验证圆弧采样点:")
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,
            'layer': 'test'
        }
        
        arc_points = processor._sample_arc_points(test_arc, num_points=8)
        print(f"圆弧采样点数量: {len(arc_points)}")
        
        # 验证所有采样点都在圆弧上
        center = test_arc['center']
        radius = test_arc['radius']
        all_on_arc = True
        
        for i, point in enumerate(arc_points[:3]):  # 检查前3个点
            distance_to_center = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
            angle = math.atan2(point[1] - center[1], point[0] - center[0])
            angle_deg = math.degrees(angle) % 360
            
            # 检查距离是否等于半径
            if abs(distance_to_center - radius) > 0.01:
                print(f"✗ 采样点{i+1}距离圆心{distance_to_center:.2f}，应该是{radius}")
                all_on_arc = False
            
            # 检查角度是否在圆弧范围内
            if not (0 <= angle_deg <= 90):
                print(f"✗ 采样点{i+1}角度{angle_deg:.1f}°，应该在0°-90°范围内")
                all_on_arc = False
            else:
                print(f"✓ 采样点{i+1}: {point}, 距离{distance_to_center:.2f}, 角度{angle_deg:.1f}°")
        
        if all_on_arc:
            print("✓ 圆弧采样点都在正确位置")
        else:
            print("✗ 圆弧采样点位置有误")
            return False
        
        # 测试2: 圆形采样点应该在圆周上
        print("\n2. 验证圆形采样点:")
        test_circle = {
            'type': 'CIRCLE',
            'center': (200, 200),
            'radius': 30,
            'layer': 'test'
        }
        
        circle_points = processor._sample_circle_points(test_circle, num_points=8)
        print(f"圆形采样点数量: {len(circle_points)}")
        
        # 验证所有采样点都在圆周上
        center = test_circle['center']
        radius = test_circle['radius']
        all_on_circle = True
        
        for i, point in enumerate(circle_points[:3]):  # 检查前3个点
            distance_to_center = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
            
            if abs(distance_to_center - radius) > 0.01:
                print(f"✗ 采样点{i+1}距离圆心{distance_to_center:.2f}，应该是{radius}")
                all_on_circle = False
            else:
                print(f"✓ 采样点{i+1}: {point}, 距离{distance_to_center:.2f}")
        
        if all_on_circle:
            print("✓ 圆形采样点都在正确位置")
        else:
            print("✗ 圆形采样点位置有误")
            return False
        
        print("\n✓ 采样点方法准确性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 采样点准确性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试圆弧椭圆完整化移除...")
    print("=" * 60)
    
    tests = [
        ("圆弧不被视为完整圆", test_arc_not_as_full_circle),
        ("椭圆弧不被视为完整椭圆", test_ellipse_arc_not_as_full_ellipse),
        ("采样点方法准确性", test_sampling_accuracy)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧椭圆完整化移除测试全部通过！")
        print("\n📋 修复总结:")
        print("✅ _circle_to_arc_distance: 不将圆弧视为完整圆")
        print("✅ _arc_to_arc_distance: 不将圆弧视为完整圆")
        print("✅ _get_entity_bbox: 圆弧使用采样点包围盒")
        print("✅ 椭圆端点计算: 正确区分椭圆弧和完整椭圆")
        print("✅ 椭圆采样点: 正确处理椭圆弧范围")
        print("✅ 采样点方法: 准确反映真实几何形状")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
