#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试停止处理功能
"""

import sys
import os
import time
import threading
import tempfile

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_dxf_files(folder_path, num_files=5):
    """创建测试DXF文件"""
    test_files = []
    
    for i in range(num_files):
        file_path = os.path.join(folder_path, f"test_{i+1}.dxf")
        
        # 创建简单的DXF内容
        dxf_content = f"""0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
LINE
8
0
10
{i * 10}
20
{i * 10}
30
0.0
11
{i * 10 + 100}
21
{i * 10 + 100}
31
0.0
0
ENDSEC
0
EOF
"""
        
        with open(file_path, 'w') as f:
            f.write(dxf_content)
        
        test_files.append(file_path)
    
    return test_files

def test_stop_during_processing():
    """测试处理过程中的停止功能"""
    print("=== 测试处理过程中的停止功能 ===")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建临时文件夹和测试文件
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"创建测试文件夹: {temp_dir}")
            
            # 创建多个测试DXF文件
            test_files = create_test_dxf_files(temp_dir, 10)
            print(f"创建了 {len(test_files)} 个测试DXF文件")
            
            # 创建处理器
            processor = CADProcessor()
            
            # 设置回调函数来监控处理状态
            processing_status = {'files_processed': 0, 'stopped': False}
            
            def status_callback(status_type, message):
                if status_type == "file_processed":
                    processing_status['files_processed'] += 1
                    print(f"已处理文件: {processing_status['files_processed']}")
                elif status_type == "stopped":
                    processing_status['stopped'] = True
                    print("收到停止信号")
                elif status_type == "completed":
                    print("处理完成")
            
            def progress_callback(current, total):
                print(f"进度: {current}/{total}")
            
            processor.status_callback = status_callback
            processor.progress_callback = progress_callback
            
            # 在后台线程中开始处理
            def start_processing():
                processor.process_folder(temp_dir)
            
            processing_thread = threading.Thread(target=start_processing, daemon=True)
            processing_thread.start()
            
            # 等待一段时间后停止处理
            time.sleep(2)  # 让处理开始
            print("🛑 发送停止信号...")
            processor.stop_processing()
            
            # 等待处理线程结束
            processing_thread.join(timeout=5)
            
            # 检查结果
            if processing_status['stopped'] or processing_status['files_processed'] < len(test_files):
                print("✅ 停止功能正常工作")
                print(f"   处理了 {processing_status['files_processed']}/{len(test_files)} 个文件")
                return True
            else:
                print("❌ 停止功能未生效")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stop_flags():
    """测试停止标志的设置和重置"""
    print("\n=== 测试停止标志 ===")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_data_processor import CADDataProcessor

        # 测试主处理器
        main_processor = EnhancedCADProcessor()
        
        # 初始状态
        assert not main_processor.should_stop, "初始停止标志应为False"
        assert not main_processor.is_running, "初始运行标志应为False"
        print("✅ 初始状态正确")
        
        # 停止处理
        main_processor.stop_processing()
        assert main_processor.should_stop, "停止后should_stop应为True"
        assert not main_processor.is_running, "停止后is_running应为False"
        print("✅ 停止状态正确")
        
        # 测试CAD数据处理器
        cad_processor = CADDataProcessor()
        
        # 初始状态
        assert not cad_processor.should_stop, "CAD处理器初始停止标志应为False"
        print("✅ CAD处理器初始状态正确")
        
        # 停止处理
        cad_processor.stop_processing()
        assert cad_processor.should_stop, "CAD处理器停止后should_stop应为True"
        print("✅ CAD处理器停止状态正确")
        
        # 重置标志
        cad_processor.reset_stop_flag()
        assert not cad_processor.should_stop, "重置后should_stop应为False"
        print("✅ CAD处理器重置状态正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 停止标志测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stop_in_long_operations():
    """测试长时间操作中的停止检查"""
    print("\n=== 测试长时间操作中的停止检查 ===")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建大量模拟实体
        mock_entities = []
        for i in range(100):
            mock_entities.append({
                'type': 'LINE',
                'layer': f'layer_{i % 10}',
                'points': [(i, i), (i+10, i+10)],
                'length': 10,
                'area': 0
            })
        
        print(f"创建了 {len(mock_entities)} 个模拟实体")
        
        # 在后台线程中开始分组
        def start_grouping():
            return processor.group_entities(mock_entities, debug=False)
        
        grouping_thread = threading.Thread(target=start_grouping, daemon=True)
        grouping_thread.start()
        
        # 立即停止
        time.sleep(0.1)  # 让分组开始
        print("🛑 发送停止信号...")
        processor.stop_processing()
        
        # 等待线程结束
        grouping_thread.join(timeout=3)
        
        if processor.should_stop:
            print("✅ 长时间操作中的停止检查正常")
            return True
        else:
            print("❌ 长时间操作中的停止检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 长时间操作停止测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_stop_button():
    """测试UI停止按钮功能"""
    print("\n=== 测试UI停止按钮功能 ===")
    
    try:
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = EnhancedCADAppV2(root)
        
        # 测试初始状态
        assert hasattr(app, 'stop_processing'), "应该有stop_processing方法"
        print("✅ UI停止方法存在")
        
        # 模拟点击停止按钮
        app.stop_processing()
        
        # 检查状态
        if hasattr(app, 'should_stop_background'):
            assert app.should_stop_background, "后台停止标志应为True"
            print("✅ 后台停止标志设置正确")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI停止按钮测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("停止处理功能测试")
    print("=" * 50)
    print("目标: 验证点击停止按钮后能真正停止处理")
    print()
    
    tests = [
        ("停止标志测试", test_stop_flags),
        ("长时间操作停止测试", test_stop_in_long_operations),
        ("UI停止按钮测试", test_ui_stop_button),
        ("处理过程停止测试", test_stop_during_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！停止功能修复完成。")
        print("\n📋 修复总结:")
        print("✅ 添加了全面的停止检查机制")
        print("✅ 在所有长时间运行的方法中添加停止检查")
        print("✅ CAD数据处理器支持停止控制")
        print("✅ UI停止按钮功能正常")
        print("✅ 停止标志的设置和重置正常")
        
        print("\n🔧 修复内容:")
        print("1. 在process_single_file中添加多个停止检查点")
        print("2. 在_auto_process_special_entities中添加停止检查")
        print("3. 在CADDataProcessor中添加停止标志和方法")
        print("4. 在group_entities等长时间运行方法中添加停止检查")
        print("5. 更新UI停止方法，改善用户反馈")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
