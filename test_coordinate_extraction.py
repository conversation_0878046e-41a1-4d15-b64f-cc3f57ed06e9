#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试坐标提取修复
验证改进的坐标提取方法是否正确
"""

def test_coordinate_extraction():
    """测试坐标提取方法"""
    print("🚀 测试坐标提取修复...")
    print("="*80)
    
    # 模拟各种可能的实体结构
    test_entities = [
        # LINE实体 - 标准结构
        {
            'type': 'LINE',
            'start': {'x': 100, 'y': 200},
            'end': {'x': 300, 'y': 200},
            'label': '墙'
        },
        
        # LINE实体 - 扁平化坐标
        {
            'type': 'LINE',
            'start_x': 100,
            'start_y': 200,
            'end_x': 300,
            'end_y': 200,
            'label': '墙'
        },
        
        # LINE实体 - 坐标数组
        {
            'type': 'LINE',
            'coordinates': [(100, 200), (300, 200)],
            'label': '墙'
        },
        
        # LINE实体 - 几何对象
        {
            'type': 'LINE',
            'geometry': {
                'type': 'LineString',
                'coordinates': [[100, 200], [300, 200]]
            },
            'label': '墙'
        },
        
        # POLYLINE实体 - 字典点
        {
            'type': 'LWPOLYLINE',
            'points': [
                {'x': 100, 'y': 200},
                {'x': 300, 'y': 200},
                {'x': 300, 'y': 400}
            ],
            'label': '墙'
        },
        
        # POLYLINE实体 - 元组点
        {
            'type': 'POLYLINE',
            'points': [(100, 200), (300, 200), (300, 400)],
            'label': '墙'
        },
        
        # 未知类型 - 通用坐标
        {
            'type': 'UNKNOWN',
            'coordinates': [(100, 200), (300, 200)],
            'label': '墙'
        },
    ]
    
    def extract_line_coordinates_test(entity):
        """测试版本的坐标提取方法"""
        try:
            entity_type = entity.get('type', '')
            print(f"   测试实体类型: {entity_type}")
            
            if entity_type == 'LINE':
                # 方法1: 标准结构
                start = entity.get('start', {})
                end = entity.get('end', {})
                if start and end and isinstance(start, dict) and isinstance(end, dict):
                    coords = [(start.get('x', 0), start.get('y', 0)), 
                             (end.get('x', 0), end.get('y', 0))]
                    print(f"      方法1成功: {coords}")
                    return [coords]
                
                # 方法2: 扁平化坐标
                if all(key in entity for key in ['start_x', 'start_y', 'end_x', 'end_y']):
                    coords = [(entity['start_x'], entity['start_y']), 
                             (entity['end_x'], entity['end_y'])]
                    print(f"      方法2成功: {coords}")
                    return [coords]
                
                # 方法3: 坐标数组
                if 'coordinates' in entity:
                    coords = entity['coordinates']
                    if len(coords) >= 2:
                        print(f"      方法3成功: {coords}")
                        return [coords]
                
                # 方法4: 几何对象
                if 'geometry' in entity:
                    geom = entity['geometry']
                    if isinstance(geom, dict) and 'coordinates' in geom:
                        coords = geom['coordinates']
                        if len(coords) >= 2:
                            print(f"      方法4成功: {coords}")
                            return [coords]
            
            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                if not points:
                    print(f"      ❌ 没有点数据")
                    return []
                
                coords = []
                
                # 方法1: 字典点
                if isinstance(points[0], dict):
                    coords = [(p.get('x', 0), p.get('y', 0)) for p in points if isinstance(p, dict)]
                    print(f"      字典点方法: {len(coords)} 个坐标")
                
                # 方法2: 元组点
                elif isinstance(points[0], (tuple, list)):
                    coords = points
                    print(f"      元组点方法: {len(coords)} 个坐标")
                
                # 创建连续线段
                if len(coords) >= 2:
                    lines = []
                    for i in range(len(coords) - 1):
                        lines.append([coords[i], coords[i + 1]])
                    print(f"      ✅ 成功提取 {len(lines)} 条线段")
                    return lines
            
            else:
                # 通用坐标提取
                possible_coord_keys = ['coordinates', 'geometry', 'vertices', 'points']
                for key in possible_coord_keys:
                    if key in entity:
                        coord_data = entity[key]
                        if isinstance(coord_data, list) and len(coord_data) >= 2:
                            print(f"      通用方法成功 ({key}): {coord_data[:2]}")
                            return [coord_data[:2]]
            
            print(f"      ❌ 所有方法都失败")
            return []
            
        except Exception as e:
            print(f"      ❌ 提取失败: {e}")
            return []
    
    print("📊 测试各种实体结构:")
    success_count = 0
    total_lines = 0
    
    for i, entity in enumerate(test_entities):
        print(f"\n实体 {i+1}: {entity.get('type', 'UNKNOWN')} - {entity.get('label', '')}")
        result = extract_line_coordinates_test(entity)
        
        if result:
            line_count = len(result)
            total_lines += line_count
            print(f"   ✅ 成功提取 {line_count} 条线段")
            success_count += 1
            
            # 显示提取的坐标
            for j, line in enumerate(result):
                print(f"      线段 {j+1}: {line}")
        else:
            print(f"   ❌ 提取失败")
    
    print(f"\n📊 测试结果:")
    print(f"   成功实体: {success_count}/{len(test_entities)} ({success_count/len(test_entities)*100:.1f}%)")
    print(f"   总线段数: {total_lines}")
    
    # 验证LineString创建
    print(f"\n🔧 验证LineString创建:")
    try:
        from shapely.geometry import LineString
        
        test_coords = [(100, 200), (300, 200)]
        line = LineString(test_coords)
        print(f"   ✅ LineString创建成功: {line}")
        print(f"   长度: {line.length:.2f}")
        print(f"   边界: {line.bounds}")
        
    except ImportError:
        print(f"   ❌ 无法导入Shapely")
    except Exception as e:
        print(f"   ❌ LineString创建失败: {e}")
    
    return success_count == len(test_entities)

def test_wall_group_processing():
    """测试墙体组处理逻辑"""
    print(f"\n" + "="*80)
    print("🔧 测试墙体组处理逻辑...")
    
    # 模拟墙体组数据
    mock_wall_groups = [
        # 墙体组1 - 混合实体类型
        [
            {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 100, 'y': 0}, 'label': '墙'},
            {'type': 'LINE', 'start_x': 100, 'start_y': 0, 'end_x': 100, 'end_y': 100, 'label': '墙'},
            {'type': 'LWPOLYLINE', 'points': [{'x': 100, 'y': 100}, {'x': 0, 'y': 100}], 'label': '墙'},
        ],
        
        # 墙体组2 - 单一类型
        [
            {'type': 'LINE', 'coordinates': [(200, 0), (300, 0)], 'label': '墙'},
            {'type': 'LINE', 'coordinates': [(300, 0), (300, 100)], 'label': '墙'},
        ]
    ]
    
    def process_wall_groups_test(wall_groups):
        """测试墙体组处理"""
        all_line_geometries = []
        wall_count = 0
        
        print(f"   处理 {len(wall_groups)} 个墙体组...")
        
        for group_idx, group in enumerate(wall_groups):
            print(f"   墙体组 {group_idx+1}: {len(group)} 个实体")
            
            for entity_idx, entity in enumerate(group):
                entity_type = entity.get('type', 'UNKNOWN')
                print(f"      实体 {entity_idx+1}: {entity_type}")
                
                # 使用测试版提取方法
                result = extract_line_coordinates_test(entity)
                
                if result:
                    for coord in result:
                        if len(coord) >= 2:
                            try:
                                from shapely.geometry import LineString
                                line = LineString(coord)
                                all_line_geometries.append(line)
                                wall_count += 1
                                print(f"         ✅ 创建LineString成功")
                            except Exception as e:
                                print(f"         ❌ 创建LineString失败: {e}")
                else:
                    print(f"         ❌ 坐标提取失败")
        
        print(f"   📊 处理结果: {wall_count} 条墙体线段")
        return all_line_geometries, wall_count
    
    try:
        geometries, count = process_wall_groups_test(mock_wall_groups)
        
        expected_count = 5  # 预期应该有5条线段
        success = count == expected_count
        
        if success:
            print(f"   ✅ 墙体组处理成功: {count}/{expected_count}")
        else:
            print(f"   ❌ 墙体组处理失败: {count}/{expected_count}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 墙体组处理异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 坐标提取修复测试")
    print("="*80)
    
    try:
        success1 = test_coordinate_extraction()
        success2 = test_wall_group_processing()
        
        print("\n" + "="*80)
        print("📊 修复测试总结:")
        
        if success1 and success2:
            print("🎉 坐标提取修复成功！")
            print("\n🔧 修复内容:")
            print("   1. ✅ 支持4种LINE坐标格式")
            print("   2. ✅ 支持2种POLYLINE点格式")
            print("   3. ✅ 添加通用坐标提取")
            print("   4. ✅ 完善错误处理和调试")
            print("   5. ✅ 验证LineString创建")
            
            print("\n💡 支持的格式:")
            print("   - LINE: {start:{x,y}, end:{x,y}}")
            print("   - LINE: {start_x, start_y, end_x, end_y}")
            print("   - LINE: {coordinates: [(x,y), (x,y)]}")
            print("   - LINE: {geometry: {coordinates: [[x,y], [x,y]]}}")
            print("   - POLYLINE: {points: [{x,y}, {x,y}]}")
            print("   - POLYLINE: {points: [(x,y), (x,y)]}")
            
            print("\n✅ 现在应该可以正确提取墙体线段了！")
        else:
            print("❌ 坐标提取修复失败")
            print("💡 请检查实体数据结构")
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
