#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的图像控制区域布局
验证按红框划分的左右布局：
1. 左边：图层控制下拉菜单
2. 右边：三个按钮水平并列布置
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

def test_new_image_control_structure():
    """测试新图像控制结构"""
    print("🔍 测试新的图像控制区域布局结构...")
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查新的布局方法
        layout_methods = [
            ("图层控制区域", "_create_layer_control_area"),
            ("缩放按钮区域", "_create_zoom_buttons_area"),
            ("图层项创建", "_create_layer_item"),
        ]
        
        print("\n📋 检查新布局方法:")
        for method_name, method_pattern in layout_methods:
            if f"def {method_pattern}(" in content:
                print(f"  ✅ {method_name}")
            else:
                print(f"  ❌ {method_name} - 未找到方法: {method_pattern}")
                return False
        
        # 检查图层控制功能方法
        layer_methods = [
            ("图层切换", "_on_layer_toggle"),
            ("图层顺序变化", "_on_layer_order_change"),
            ("应用图层设置", "_apply_layer_settings"),
            ("应用图层可见性", "_apply_layer_visibility"),
            ("应用图层顺序", "_apply_layer_order"),
            ("刷新所有视图", "_refresh_all_views"),
        ]
        
        print("\n📋 检查图层控制功能:")
        for func_name, func_pattern in layer_methods:
            if f"def {func_pattern}(" in content:
                print(f"  ✅ {func_name}")
            else:
                print(f"  ❌ {func_name} - 未找到方法: {func_pattern}")
                return False
        
        # 检查图层类型切换方法
        toggle_methods = [
            ("CAD线条切换", "_toggle_cad_lines_visibility"),
            ("墙体填充切换", "_toggle_wall_fill_visibility"),
            ("家具填充切换", "_toggle_furniture_fill_visibility"),
            ("房间填充切换", "_toggle_room_fill_visibility"),
        ]
        
        print("\n📋 检查图层类型切换:")
        for toggle_name, toggle_pattern in toggle_methods:
            if f"def {toggle_pattern}(" in content:
                print(f"  ✅ {toggle_name}")
            else:
                print(f"  ❌ {toggle_name} - 未找到方法: {toggle_pattern}")
                return False
        
        # 检查布局容器
        layout_containers = [
            ("图层控制容器", "self.layer_control_container"),
            ("缩放按钮容器", "self.zoom_buttons_container"),
            ("图层状态字典", "self.layer_states"),
            ("图层顺序变量", "self.layer_order_var"),
            ("图层顺序下拉框", "self.layer_order_combo"),
        ]
        
        print("\n📋 检查布局容器:")
        for container_name, container_pattern in layout_containers:
            if container_pattern in content:
                print(f"  ✅ {container_name}")
            else:
                print(f"  ❌ {container_name} - 未找到: {container_pattern}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_image_control_visual_demo():
    """创建图像控制可视化演示"""
    print("\n🎨 创建图像控制可视化演示...")
    
    try:
        # 创建演示窗口
        root = tk.Tk()
        root.title("新图像控制区域布局演示")
        root.geometry("800x400")
        
        # 主容器
        main_frame = tk.Frame(root, bg='lightgray')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="3. 图像控制 - 新布局演示",
                              font=('Arial', 12, 'bold'), bg='lightcoral')
        title_label.pack(fill='x', pady=(0, 5))
        
        # 创建左右分割的演示
        content_frame = tk.Frame(main_frame)
        content_frame.pack(fill='both', expand=True)
        
        # 配置网格权重
        content_frame.grid_rowconfigure(0, weight=1)
        content_frame.grid_columnconfigure(0, weight=1)  # 左侧
        content_frame.grid_columnconfigure(1, weight=1)  # 右侧
        
        # 左边：图层控制区域
        layer_frame = tk.Frame(content_frame, relief='ridge', bd=2, bg='#E6F3FF')
        layer_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 2))
        
        layer_title = tk.Label(layer_frame, text="图层控制", 
                              font=('Arial', 10, 'bold'), bg='#E6F3FF')
        layer_title.pack(pady=5)
        
        # 模拟图层控制项
        layer_items = [
            ('CAD线条', '#2196F3'),
            ('墙体填充', '#4CAF50'),
            ('家具填充', '#FF9800'),
            ('房间填充', '#9C27B0')
        ]
        
        for layer_name, color in layer_items:
            item_frame = tk.Frame(layer_frame, bg='#E6F3FF')
            item_frame.pack(fill='x', padx=10, pady=2)
            
            # 颜色指示器
            color_label = tk.Label(item_frame, text="●", fg=color, 
                                 font=('Arial', 12, 'bold'), bg='#E6F3FF')
            color_label.pack(side='left')
            
            # 复选框
            var = tk.BooleanVar(value=True)
            checkbox = tk.Checkbutton(item_frame, variable=var, bg='#E6F3FF')
            checkbox.pack(side='left', padx=2)
            
            # 图层名称
            name_label = tk.Label(item_frame, text=layer_name, 
                                font=('Arial', 9), bg='#E6F3FF')
            name_label.pack(side='left', padx=5)
        
        # 图层顺序控制
        order_frame = tk.Frame(layer_frame, bg='#E6F3FF')
        order_frame.pack(fill='x', padx=10, pady=10)
        
        order_label = tk.Label(order_frame, text="图层顺序:", 
                             font=('Arial', 9, 'bold'), bg='#E6F3FF')
        order_label.pack(anchor='w')
        
        order_combo = ttk.Combobox(order_frame, 
                                 values=["CAD线条 → 墙体 → 家具 → 房间",
                                        "CAD线条 → 房间 → 墙体 → 家具"], 
                                 state='readonly', width=25)
        order_combo.set("CAD线条 → 墙体 → 家具 → 房间")
        order_combo.pack(anchor='w', pady=2)
        
        # 应用按钮
        apply_btn = tk.Button(order_frame, text="应用图层设置",
                            bg='#FF5722', fg='white', font=('Arial', 8, 'bold'))
        apply_btn.pack(fill='x', pady=5)
        
        # 右边：缩放按钮区域
        buttons_frame = tk.Frame(content_frame, relief='ridge', bd=2, bg='#FFF8DC')
        buttons_frame.grid(row=0, column=1, sticky='nsew', padx=(2, 0))
        
        buttons_title = tk.Label(buttons_frame, text="视图控制", 
                               font=('Arial', 10, 'bold'), bg='#FFF8DC')
        buttons_title.pack(pady=5)
        
        # 创建按钮容器
        buttons_container = tk.Frame(buttons_frame, bg='#FFF8DC')
        buttons_container.pack(expand=True, fill='both')
        
        # 居中放置按钮
        center_frame = tk.Frame(buttons_container, bg='#FFF8DC')
        center_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # 三个按钮水平并列
        button_frame = tk.Frame(center_frame, bg='#FFF8DC')
        button_frame.pack()
        
        # 缩放查看按钮
        zoom_btn = tk.Button(button_frame, text="🔍\n缩放查看",
                           font=('Arial', 9, 'bold'),
                           bg='#FF9800', fg='white',
                           width=8, height=3,
                           relief='raised', bd=2)
        zoom_btn.pack(side='left', padx=2)
        
        # 适应窗口按钮
        fit_btn = tk.Button(button_frame, text="📐\n适应窗口",
                          font=('Arial', 9, 'bold'),
                          bg='#4CAF50', fg='white',
                          width=8, height=3,
                          relief='raised', bd=2)
        fit_btn.pack(side='left', padx=2)
        
        # 重置视图按钮
        reset_btn = tk.Button(button_frame, text="🔄\n重置视图",
                            font=('Arial', 9, 'bold'),
                            bg='#2196F3', fg='white',
                            width=8, height=3,
                            relief='raised', bd=2)
        reset_btn.pack(side='left', padx=2)
        
        # 底部提示
        tip_label = tk.Label(buttons_frame, text="点击按钮控制视图显示",
                           font=('Arial', 7), fg='gray', bg='#FFF8DC')
        tip_label.pack(side='bottom', pady=2)
        
        # 说明文本
        info_frame = tk.Frame(root, bg='#ecf0f1')
        info_frame.pack(fill='x', padx=10, pady=5)
        
        info_text = """
📋 新布局说明：
• 左边红框：图层控制 - 包含4种图层的显示控制、图层顺序设置和应用按钮
• 右边红框：视图控制 - 三个按钮水平并列：缩放查看、适应窗口、重置视图
• 图层类型：1.CAD线条 2.墙体填充 3.家具填充 4.房间填充
        """
        
        info_label = tk.Label(info_frame, text=info_text, 
                             font=('Arial', 9), justify='left',
                             bg='#ecf0f1', fg='#2c3e50')
        info_label.pack(anchor='w', padx=10, pady=5)
        
        print("✅ 图像控制演示窗口创建成功")
        
        # 运行演示
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建图像控制演示失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🧪 新图像控制区域布局验证")
    print("🎯 验证按红框划分的左右布局实现")
    print("=" * 70)
    
    # 测试布局结构
    structure_ok = test_new_image_control_structure()
    
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"  新图像控制结构: {'✅ 通过' if structure_ok else '❌ 失败'}")
    
    if structure_ok:
        print("\n🎉 新图像控制布局验证通过！")
        print("\n✨ 新布局特点:")
        print("  1. 左边红框：图层控制 - 包含4种图层的显示控制和顺序设置")
        print("  2. 右边红框：视图控制 - 三个按钮水平并列布置")
        print("  3. 图层类型：CAD线条、墙体填充、家具填充、房间填充")
        print("  4. 响应式布局：支持窗口大小调整")
        print("  5. 功能集成：图层控制与视图控制分离明确")
        
        # 询问是否显示演示
        try:
            choice = input("\n是否显示图像控制演示窗口？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                test_image_control_visual_demo()
        except:
            pass
    else:
        print("\n❌ 新图像控制布局验证失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
