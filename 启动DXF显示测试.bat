@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    DXF文件显示测试小程序
echo ==========================================
echo.
echo 🎯 程序功能：
echo   - 测试圆弧椭圆重构方案的显示效果
echo   - 对比原始显示和重构后显示
echo   - 验证重构方案是否正确
echo.
echo 📁 相关文件：
echo   - dxf_display_test.py (主程序)
echo   - arc_ellipse_reconstructor.py (重构器)
echo   - test_arcs_ellipses.dxf (测试文件)
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "dxf_display_test.py" (
    echo ❌ 错误：未找到主程序文件 dxf_display_test.py
    pause
    exit /b 1
)

if not exist "arc_ellipse_reconstructor.py" (
    echo ❌ 错误：未找到重构器文件 arc_ellipse_reconstructor.py
    pause
    exit /b 1
)

REM 检查测试DXF文件，如果不存在则创建
if not exist "test_arcs_ellipses.dxf" (
    echo 📝 未找到测试DXF文件，正在创建...
    if exist "create_test_dxf.py" (
        python create_test_dxf.py
        if errorlevel 1 (
            echo ⚠️ 警告：创建测试文件失败，程序将使用模拟数据
        ) else (
            echo ✅ 测试DXF文件创建成功
        )
    ) else (
        echo ⚠️ 警告：未找到测试文件生成器，程序将使用模拟数据
    )
    echo.
)

echo 🚀 启动DXF显示测试程序...
echo.
echo 💡 使用提示：
echo   1. 点击"选择DXF文件"选择要测试的文件
echo   2. 推荐使用 test_arcs_ellipses.dxf 进行测试
echo   3. 对比左右两侧的显示效果
echo   4. 查看下方信息面板了解处理详情
echo.

REM 启动程序
python dxf_display_test.py

REM 检查程序退出状态
if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    echo 请检查错误信息并确保所有依赖库已正确安装
    echo.
    echo 📦 需要的依赖库：
    echo   pip install matplotlib numpy tkinter
    echo   pip install ezdxf  # 可选，用于读取真实DXF文件
) else (
    echo.
    echo ✅ 程序正常退出
)

echo.
pause
