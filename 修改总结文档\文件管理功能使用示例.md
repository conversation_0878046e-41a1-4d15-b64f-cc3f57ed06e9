# 文件管理功能使用示例

## 界面展示

### 新增的界面元素

```
处理状态:
文件选择:
┌─────────────────────────────────────┬────────┬────────┐
│ [当前文件.dxf] (已完成, 标注未完成)    │ 保存   │ 读取   │
└─────────────────────────────────────┴────────┴────────┘
████████████████████████████████████████ 100%
状态: 文件处理完成，可以进行标注操作
文件: 2/3 已处理, 1/3 已标注 | 实体: 150 | 组: 25 | 已标注: 80
```

### 下拉菜单显示格式

```
文件选择下拉菜单内容：
┌─────────────────────────────────────────────┐
│ [building1.dxf] (已完成, 标注完成)           │ ← 当前文件（红色标记）
│ building2.dxf (已完成, 标注未完成)           │
│ building3.dxf (处理中, 未标注)               │
│ building4.dxf (未处理, 未标注)               │
└─────────────────────────────────────────────┘
```

## 使用流程示例

### 场景1：批量处理新项目

**步骤1：选择文件夹**
```
选择包含多个CAD文件的文件夹：
📁 project_folder/
  ├── building1.dxf
  ├── building2.dxf
  ├── building3.dxf
  └── building4.dxf
```

**步骤2：开始处理**
- 点击"开始处理"按钮
- building1.dxf 开始前台处理（用户可交互）
- building2.dxf, building3.dxf, building4.dxf 自动后台处理

**步骤3：实时状态更新**
```
时间轴：
T+0s:  building1.dxf (处理中, 未标注) ← 前台
       building2.dxf (处理中, 未标注) ← 后台
       building3.dxf (未处理, 未标注)
       building4.dxf (未处理, 未标注)

T+30s: building1.dxf (已完成, 未标注) ← 可以开始标注
       building2.dxf (已完成, 未标注) ← 后台完成
       building3.dxf (处理中, 未标注) ← 后台处理中
       building4.dxf (未处理, 未标注)

T+60s: building1.dxf (已完成, 标注未完成) ← 用户正在标注
       building2.dxf (已完成, 未标注)
       building3.dxf (已完成, 未标注) ← 后台完成
       building4.dxf (处理中, 未标注) ← 后台处理中
```

### 场景2：文件切换操作

**当前状态：**
- building1.dxf 已完成分组，正在标注中
- building2.dxf 后台处理完成

**切换操作：**
1. 点击文件选择下拉菜单
2. 选择 "building2.dxf (已完成, 未标注)"
3. 系统自动保存 building1.dxf 的当前标注进度
4. 切换到 building2.dxf 并加载其数据
5. 可以开始对 building2.dxf 进行标注

**切换后状态：**
```
building1.dxf (已完成, 标注未完成) ← 数据已暂存
[building2.dxf] (已完成, 未标注)   ← 当前文件
```

### 场景3：数据保存和恢复

**保存操作：**
```
条件检查：
✅ building1.dxf (已完成, 标注完成)
✅ building2.dxf (已完成, 标注未完成)
✅ building3.dxf (已完成, 未标注)
✅ building4.dxf (已完成, 未标注)

所有文件已处理完成 → 可以保存
```

**保存的数据文件：**
```json
{
  "folder": "C:/project_folder",
  "files": [
    "building1.dxf",
    "building2.dxf", 
    "building3.dxf",
    "building4.dxf"
  ],
  "file_status": {
    "building1.dxf": {
      "processing_status": "completed",
      "annotation_status": "completed"
    },
    "building2.dxf": {
      "processing_status": "completed", 
      "annotation_status": "incomplete"
    }
  },
  "file_data": {
    "building1.dxf": {
      "entities": [...],
      "groups": [...],
      "labeled_entities": [...],
      "timestamp": "2024-01-15T14:30:00"
    }
  },
  "save_time": "2024-01-15T15:00:00",
  "version": "1.0"
}
```

## 实际操作示例

### 示例1：建筑项目批量标注

**项目背景：**
- 5栋建筑的CAD图纸需要标注
- 每个文件包含100-200个实体
- 需要标注墙体、门窗、设备等

**操作流程：**

1. **初始化项目**
   ```
   选择文件夹: C:/建筑项目/CAD图纸/
   扫描结果: 找到 5 个CAD文件
   ```

2. **开始批量处理**
   ```
   前台处理: 1号楼.dxf
   后台队列: 2号楼.dxf, 3号楼.dxf, 4号楼.dxf, 5号楼.dxf
   ```

3. **分组完成后开始标注**
   ```
   1号楼.dxf: 分组完成 → 开始标注墙体
   2号楼.dxf: 后台分组完成 → 等待标注
   ```

4. **灵活切换标注**
   ```
   标注1号楼墙体 → 切换到2号楼标注门窗 → 回到1号楼继续标注
   每次切换都自动保存进度
   ```

5. **项目完成保存**
   ```
   所有文件处理完成 → 点击保存 → 生成项目数据文件
   ```

### 示例2：团队协作场景

**场景：**
- 团队成员A负责分组处理
- 团队成员B负责标注工作

**成员A的工作：**
1. 处理所有文件的分组
2. 保存数据文件
3. 共享给成员B

**成员B的工作：**
1. 读取数据文件
2. 选择需要标注的文件
3. 进行标注工作
4. 保存更新后的数据

### 示例3：增量处理场景

**第一天：**
```
处理文件: file1.dxf, file2.dxf
状态: 已完成分组，部分标注
保存数据: project_day1.json
```

**第二天：**
```
读取数据: project_day1.json
继续标注: file1.dxf, file2.dxf
新增文件: file3.dxf, file4.dxf
保存数据: project_day2.json
```

## 错误处理示例

### 常见错误及解决方案

**错误1：切换到未完成处理的文件**
```
用户操作: 选择 "building3.dxf (处理中, 未标注)"
系统响应: ⚠️ 警告：文件 building3.dxf 尚未处理完成，无法切换
解决方案: 等待后台处理完成
```

**错误2：保存时有未处理文件**
```
用户操作: 点击"保存"按钮
系统检查: building4.dxf 状态为"处理中"
系统响应: ⚠️ 警告：以下文件尚未处理完成，无法保存：building4.dxf
解决方案: 等待所有文件处理完成
```

**错误3：读取不兼容的数据文件**
```
用户操作: 点击"读取"按钮
系统检查: 数据文件版本为 0.9
系统响应: ⚠️ 警告：数据文件版本不兼容
解决方案: 使用对应版本的工具或重新处理
```

## 性能优化建议

### 大文件处理
- 建议单个文件不超过50MB
- 同时处理的文件数量不超过10个
- 复杂文件可能需要更长处理时间

### 内存管理
- 定期保存数据避免内存占用过高
- 不需要的文件数据会自动释放
- 建议处理完成后及时保存

### 网络共享
- 数据文件可以通过网络共享
- 建议使用版本控制管理数据文件
- 避免多人同时修改同一数据文件

这些示例展示了新的文件管理功能如何大大提升CAD分类标注工具的实用性和效率。
