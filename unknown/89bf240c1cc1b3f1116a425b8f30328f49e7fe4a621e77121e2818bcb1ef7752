#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别UI模块
在索引图上方添加房间识别功能界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Polygon as MplPolygon
import numpy as np


class RoomRecognitionUI:
    """房间识别UI模块"""
    
    def __init__(self, parent_frame, room_processor, update_callback=None):
        """
        初始化房间识别UI
        
        Args:
            parent_frame: 父容器
            room_processor: 房间识别处理器
            update_callback: 更新回调函数
        """
        self.parent_frame = parent_frame
        self.room_processor = room_processor
        self.update_callback = update_callback
        
        # UI组件
        self.main_frame = None
        self.control_frame = None
        self.display_frame = None
        self.room_list_frame = None
        
        # 房间列表相关
        self.room_tree = None
        self.room_canvas = None
        self.room_fig = None
        self.room_ax = None
        
        # 状态
        self.is_initialized = False
        self.selected_room_index = None
        
        self._create_ui()
    
    def _create_ui(self):
        """创建UI界面"""
        try:
            # 主容器
            self.main_frame = tk.Frame(self.parent_frame, relief='ridge', bd=1)
            self.main_frame.pack(fill='x', pady=(0, 2))
            
            # 标题
            title_label = tk.Label(self.main_frame, text="房间识别模块",
                                 font=('Arial', 10, 'bold'))
            title_label.pack(anchor='w', padx=5, pady=(2, 0))
            
            # 创建水平布局：左侧控制区，右侧显示区
            content_frame = tk.Frame(self.main_frame)
            content_frame.pack(fill='both', expand=True, padx=5, pady=2)
            
            # 左侧控制区（40%）
            self.control_frame = tk.Frame(content_frame)
            self.control_frame.pack(side='left', fill='both', expand=False, padx=(0, 2))
            
            # 右侧显示区（60%）
            self.display_frame = tk.Frame(content_frame)
            self.display_frame.pack(side='right', fill='both', expand=True, padx=(2, 0))
            
            # 创建控制区内容
            self._create_control_area()
            
            # 创建显示区内容
            self._create_display_area()
            
            self.is_initialized = True
            print("✅ 房间识别UI创建完成")
            
        except Exception as e:
            print(f"❌ 创建房间识别UI失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _create_control_area(self):
        """创建控制区"""
        try:
            # 功能按钮区（第一行）
            button_frame1 = tk.Frame(self.control_frame)
            button_frame1.pack(fill='x', pady=(0, 2))

            # 自动识别按钮（新增 - 主要功能）
            auto_btn = tk.Button(button_frame1, text="🤖 自动房间识别",
                               command=self._auto_room_recognition,
                               bg='#FFE4B5', font=('Arial', 9, 'bold'),
                               relief='raised', bd=2)
            auto_btn.pack(fill='x', pady=(0, 2))

            # 功能按钮区（第二行）
            button_frame2 = tk.Frame(self.control_frame)
            button_frame2.pack(fill='x', pady=(0, 5))

            # 识别建筑外轮廓按钮
            outline_btn = tk.Button(button_frame2, text="识别外轮廓",
                                  command=self._identify_building_outline,
                                  bg='#E6F3FF', font=('Arial', 8))
            outline_btn.pack(side='left', padx=(0, 1))

            # 识别房间按钮
            room_btn = tk.Button(button_frame2, text="识别房间",
                               command=self._identify_rooms,
                               bg='#F0FFF0', font=('Arial', 8))
            room_btn.pack(side='left', padx=1)

            # 房间切分按钮
            split_btn = tk.Button(button_frame2, text="房间切分",
                                command=self._split_rooms,
                                bg='#FFF8DC', font=('Arial', 8))
            split_btn.pack(side='left', padx=1)
            
            # 房间列表区
            list_label = tk.Label(self.control_frame, text="房间列表:",
                                font=('Arial', 9, 'bold'))
            list_label.pack(anchor='w', pady=(5, 2))
            
            # 创建房间列表
            self._create_room_list()
            
            # 房间类型修改区
            self._create_room_type_editor()
            
        except Exception as e:
            print(f"❌ 创建控制区失败: {e}")
    
    def _create_room_list(self):
        """创建房间列表"""
        try:
            # 房间列表容器
            self.room_list_frame = tk.Frame(self.control_frame)
            self.room_list_frame.pack(fill='both', expand=True, pady=(0, 5))
            
            # 创建Treeview
            columns = ('房间号', '类型', '面积')
            self.room_tree = ttk.Treeview(self.room_list_frame, columns=columns, 
                                        show='headings', height=6)
            
            # 设置列标题
            self.room_tree.heading('房间号', text='房间号')
            self.room_tree.heading('类型', text='类型')
            self.room_tree.heading('面积', text='面积(m²)')
            
            # 设置列宽
            self.room_tree.column('房间号', width=50, anchor='center')
            self.room_tree.column('类型', width=80, anchor='center')
            self.room_tree.column('面积', width=80, anchor='center')
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(self.room_list_frame, orient='vertical',
                                    command=self.room_tree.yview)
            self.room_tree.configure(yscrollcommand=scrollbar.set)
            
            # 布局
            self.room_tree.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')
            
            # 绑定选择事件
            self.room_tree.bind('<<TreeviewSelect>>', self._on_room_select)
            
        except Exception as e:
            print(f"❌ 创建房间列表失败: {e}")
    
    def _create_room_type_editor(self):
        """创建房间类型编辑器"""
        try:
            # 房间类型编辑区
            editor_frame = tk.Frame(self.control_frame)
            editor_frame.pack(fill='x', pady=(5, 0))
            
            # 标签
            type_label = tk.Label(editor_frame, text="房间类型:",
                                font=('Arial', 9))
            type_label.pack(anchor='w')
            
            # 类型选择下拉框
            self.room_type_var = tk.StringVar()
            self.room_type_combo = ttk.Combobox(editor_frame, 
                                              textvariable=self.room_type_var,
                                              values=self.room_processor.room_types,
                                              state='readonly', width=15)
            self.room_type_combo.pack(anchor='w', pady=(2, 5))
            
            # 应用按钮
            apply_btn = tk.Button(editor_frame, text="应用修改",
                                command=self._apply_room_type_change,
                                bg='#FFE4E1', font=('Arial', 8))
            apply_btn.pack(anchor='w')
            
        except Exception as e:
            print(f"❌ 创建房间类型编辑器失败: {e}")
    
    def _create_display_area(self):
        """创建显示区"""
        try:
            # 显示区标题
            display_label = tk.Label(self.display_frame, text="房间布局图:",
                                   font=('Arial', 9, 'bold'))
            display_label.pack(anchor='w', pady=(0, 2))
            
            # 创建matplotlib画布
            self.room_fig, self.room_ax = plt.subplots(figsize=(4, 3))
            self.room_ax.set_aspect('equal')
            self.room_ax.set_title('房间识别结果', fontsize=10)
            
            # 创建画布
            self.room_canvas = FigureCanvasTkAgg(self.room_fig, self.display_frame)
            self.room_canvas.get_tk_widget().pack(fill='both', expand=True)
            
            # 初始化空白显示
            self._update_room_display()
            
        except Exception as e:
            print(f"❌ 创建显示区失败: {e}")
    
    def _auto_room_recognition(self):
        """自动房间识别（新增 - 启动完整的房间切分流程）"""
        try:
            print("🤖 开始自动房间识别...")

            # 显示进度提示
            if hasattr(self, 'room_tree'):
                # 清空现有列表并显示进度
                for item in self.room_tree.get_children():
                    self.room_tree.delete(item)
                self.room_tree.insert('', 'end', values=("---", "正在识别...", "---"))

            # 更新显示
            if hasattr(self, 'parent_frame'):
                self.parent_frame.update()

            # 步骤1: 获取墙体组和门窗组数据
            print("📊 步骤1: 获取墙体组和门窗组数据...")
            wall_groups, door_window_groups = self._get_wall_door_data()

            if not wall_groups:
                print("❌ 没有找到墙体组数据")
                messagebox.showwarning("警告", "没有找到墙体组数据，请先进行墙体识别和分组")
                self._clear_progress_display()
                return

            print(f"✅ 找到 {len(wall_groups)} 个墙体组")
            if door_window_groups:
                print(f"✅ 找到 {len(door_window_groups)} 个门窗组")
            else:
                print("⚠️ 没有找到门窗组，将仅使用墙体进行识别")

            # 步骤2: 设置数据到房间处理器
            print("📊 步骤2: 设置数据到房间处理器...")
            self.room_processor.wall_groups = wall_groups
            self.room_processor.door_window_groups = door_window_groups or []

            # 步骤3: 执行完整的房间识别流程
            print("📊 步骤3: 执行房间识别流程...")
            identified_rooms = self.room_processor._identify_rooms()

            if identified_rooms:
                print(f"✅ 房间识别成功: {len(identified_rooms)} 个房间")

                # 设置识别结果
                self.room_processor.rooms = identified_rooms

                # 步骤4: 更新UI显示
                print("📊 步骤4: 更新UI显示...")
                self._update_room_list()
                self._update_room_display()

                # 显示识别结果统计
                self._show_recognition_summary(identified_rooms)

                print("🎉 自动房间识别完成！")
                messagebox.showinfo("成功", f"自动房间识别完成！\n识别到 {len(identified_rooms)} 个房间")

            else:
                print("❌ 房间识别失败")
                messagebox.showerror("错误", "房间识别失败，请检查墙体和门窗数据")
                self._clear_progress_display()

        except Exception as e:
            print(f"❌ 自动房间识别失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"自动房间识别失败: {e}")
            self._clear_progress_display()

    def _get_wall_door_data(self):
        """获取墙体和门窗数据"""
        try:
            wall_groups = []
            door_window_groups = []

            # 通过更新回调获取主应用数据
            if self.update_callback:
                # 调用主应用的数据获取方法
                data = self.update_callback('get_wall_door_data')
                if data:
                    wall_groups = data.get('wall_groups', [])
                    door_window_groups = data.get('door_window_groups', [])

            return wall_groups, door_window_groups

        except Exception as e:
            print(f"❌ 获取墙体门窗数据失败: {e}")
            return [], []

    def _show_recognition_summary(self, rooms):
        """显示识别结果摘要"""
        try:
            # 统计房间类型
            type_counts = {}
            total_area = 0

            for room in rooms:
                room_type = room['type']
                area = room['area']
                type_counts[room_type] = type_counts.get(room_type, 0) + 1
                total_area += area

            # 构建摘要信息
            summary_lines = [f"房间识别完成！共识别到 {len(rooms)} 个房间："]
            for room_type, count in type_counts.items():
                summary_lines.append(f"  {room_type}: {count} 个")

            summary_lines.append(f"\n总面积: {total_area:.1f} 平方单位")

            summary_text = "\n".join(summary_lines)
            print(f"📊 识别摘要:\n{summary_text}")

        except Exception as e:
            print(f"❌ 显示识别摘要失败: {e}")

    def _clear_progress_display(self):
        """清除进度显示"""
        try:
            if hasattr(self, 'room_tree'):
                for item in self.room_tree.get_children():
                    self.room_tree.delete(item)
        except Exception as e:
            print(f"❌ 清除进度显示失败: {e}")

    def _identify_building_outline(self):
        """识别建筑外轮廓"""
        try:
            print("🔍 开始识别建筑外轮廓...")

            # 这里需要从主应用获取墙体组和门窗组数据
            if hasattr(self, 'get_wall_door_data_callback') and self.get_wall_door_data_callback:
                wall_groups, door_window_groups = self.get_wall_door_data_callback()
            else:
                # 临时使用空数据进行测试
                wall_groups = []
                door_window_groups = []
                messagebox.showwarning("警告", "未找到墙体和门窗数据，请先进行墙体识别")
                return
            
            # 调用房间处理器识别建筑外轮廓
            self.room_processor.wall_groups = wall_groups
            self.room_processor.door_window_groups = door_window_groups
            
            outline = self.room_processor._identify_building_outline()
            
            if outline:
                messagebox.showinfo("成功", "建筑外轮廓识别成功！")
                self._update_room_display()
            else:
                messagebox.showerror("失败", "建筑外轮廓识别失败")
                
        except Exception as e:
            print(f"❌ 识别建筑外轮廓失败: {e}")
            messagebox.showerror("错误", f"识别建筑外轮廓失败: {e}")
    
    def _identify_rooms(self):
        """识别房间"""
        try:
            print("🏠 开始识别房间...")
            
            if not self.room_processor.building_outline:
                messagebox.showwarning("警告", "请先识别建筑外轮廓")
                return
            
            # 识别房间
            rooms = self.room_processor._identify_rooms()
            
            if rooms:
                # 自动分类房间
                self.room_processor._classify_rooms_automatically()
                
                # 更新房间列表
                self._update_room_list()
                
                # 更新显示
                self._update_room_display()
                
                messagebox.showinfo("成功", f"房间识别成功！识别到 {len(rooms)} 个房间")
            else:
                messagebox.showerror("失败", "房间识别失败")
                
        except Exception as e:
            print(f"❌ 识别房间失败: {e}")
            messagebox.showerror("错误", f"识别房间失败: {e}")
    
    def _split_rooms(self):
        """房间切分"""
        try:
            print("✂️ 开始房间切分...")
            
            # 这里可以实现更高级的房间切分逻辑
            # 目前显示提示信息
            messagebox.showinfo("提示", "房间切分功能正在开发中...")
            
        except Exception as e:
            print(f"❌ 房间切分失败: {e}")
            messagebox.showerror("错误", f"房间切分失败: {e}")
    
    def _update_room_list(self):
        """更新房间列表（增强版 - 显示详细分类信息）"""
        try:
            # 清空现有项目
            for item in self.room_tree.get_children():
                self.room_tree.delete(item)

            # 添加房间数据
            for i, room in enumerate(self.room_processor.rooms):
                room_id = f"R{i+1:02d}"
                room_type = room['type']
                area = f"{room['area']:.1f}"

                # 添加宽度信息（如果有的话）
                width_info = ""
                if 'width' in room and room['width'] > 0:
                    width_info = f" (宽:{room['width']:.0f})"

                # 组合显示信息
                display_type = f"{room_type}{width_info}"

                self.room_tree.insert('', 'end', values=(room_id, display_type, area))

            print(f"✅ 房间列表已更新，共 {len(self.room_processor.rooms)} 个房间")

        except Exception as e:
            print(f"❌ 更新房间列表失败: {e}")
    
    def _update_room_display(self):
        """更新房间显示"""
        try:
            # 清空画布
            self.room_ax.clear()
            self.room_ax.set_aspect('equal')
            self.room_ax.set_title('房间识别结果', fontsize=10)
            
            # 绘制建筑外轮廓
            if self.room_processor.building_outline:
                x, y = self.room_processor.building_outline.exterior.xy
                self.room_ax.plot(x, y, 'k-', linewidth=2, label='建筑外轮廓')
            
            # 绘制房间
            for i, room in enumerate(self.room_processor.rooms):
                geometry = room['geometry']
                room_type = room['type']
                color = self.room_processor.room_colors.get(room_type, '#F0F8FF')
                
                # 绘制房间多边形
                if hasattr(geometry, 'exterior'):
                    x, y = geometry.exterior.xy
                    polygon = MplPolygon(list(zip(x, y)), facecolor=color, 
                                       edgecolor='black', alpha=0.7, linewidth=1)
                    self.room_ax.add_patch(polygon)
                    
                    # 添加房间标签
                    centroid = geometry.centroid
                    self.room_ax.text(centroid.x, centroid.y, f"R{i+1}\n{room_type}",
                                    ha='center', va='center', fontsize=8,
                                    bbox=dict(boxstyle="round,pad=0.3", 
                                            facecolor='white', alpha=0.8))
            
            # 设置坐标轴
            self.room_ax.set_xlabel('X', fontsize=8)
            self.room_ax.set_ylabel('Y', fontsize=8)
            self.room_ax.tick_params(labelsize=7)
            
            # 自动调整视图
            if self.room_processor.building_outline:
                bounds = self.room_processor.building_outline.bounds
                margin = max(bounds[2] - bounds[0], bounds[3] - bounds[1]) * 0.1
                self.room_ax.set_xlim(bounds[0] - margin, bounds[2] + margin)
                self.room_ax.set_ylim(bounds[1] - margin, bounds[3] + margin)
            
            # 刷新画布
            self.room_canvas.draw()
            
        except Exception as e:
            print(f"❌ 更新房间显示失败: {e}")
    
    def _on_room_select(self, event):
        """房间选择事件处理"""
        try:
            selection = self.room_tree.selection()
            if selection:
                item = self.room_tree.item(selection[0])
                values = item['values']
                
                # 获取房间索引
                room_id = values[0]  # 格式: R01, R02, ...
                self.selected_room_index = int(room_id[1:]) - 1
                
                # 更新房间类型选择框
                if 0 <= self.selected_room_index < len(self.room_processor.rooms):
                    current_type = self.room_processor.rooms[self.selected_room_index]['type']
                    self.room_type_var.set(current_type)
                
        except Exception as e:
            print(f"❌ 房间选择事件处理失败: {e}")
    
    def _apply_room_type_change(self):
        """应用房间类型修改"""
        try:
            if self.selected_room_index is None:
                messagebox.showwarning("警告", "请先选择一个房间")
                return
            
            new_type = self.room_type_var.get()
            if not new_type:
                messagebox.showwarning("警告", "请选择房间类型")
                return
            
            # 更新房间类型
            success = self.room_processor.update_room_type(self.selected_room_index, new_type)
            
            if success:
                # 更新房间列表
                self._update_room_list()
                
                # 更新显示
                self._update_room_display()
                
                # 调用更新回调
                if self.update_callback:
                    self.update_callback()
                
                messagebox.showinfo("成功", f"房间类型已更新为: {new_type}")
            else:
                messagebox.showerror("失败", "房间类型更新失败")
                
        except Exception as e:
            print(f"❌ 应用房间类型修改失败: {e}")
            messagebox.showerror("错误", f"房间类型修改失败: {e}")
    
    def set_data_callback(self, callback):
        """设置数据获取回调函数"""
        self.get_wall_door_data_callback = callback
    
    def update_with_new_data(self, wall_groups, door_window_groups):
        """使用新数据更新房间识别"""
        try:
            # 处理房间识别
            result = self.room_processor.process_room_recognition(wall_groups, door_window_groups)
            
            if result:
                # 更新房间列表
                self._update_room_list()
                
                # 更新显示
                self._update_room_display()
                
                print("✅ 房间识别数据已更新")
            
        except Exception as e:
            print(f"❌ 更新房间识别数据失败: {e}")
