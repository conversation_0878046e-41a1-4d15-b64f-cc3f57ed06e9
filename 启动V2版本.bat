@echo off
chcp 65001 >nul
echo ====================================
echo   CAD分类标注工具 - V2完整版
echo ====================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在检查依赖包...
python -c "import matplotlib, pandas, numpy, ezdxf, shapely, rtree, tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo 检测到缺失的依赖包，正在自动安装...
    echo.
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 依赖包安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
    echo 依赖包安装完成
    echo.
)

echo 依赖包检查通过
echo.

echo 正在启动CAD分类标注工具 - V2完整版...
echo.

REM 启动V2版本主程序
python main_enhanced_with_v2_fill.py

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出错，错误代码: %errorlevel%
    echo.
    echo 请尝试以下解决方案：
    echo 1. 检查Python版本（需要3.8+）
    echo 2. 手动安装依赖：pip install -r requirements.txt
    echo 3. 以管理员身份运行此脚本
    echo 4. 检查杀毒软件是否阻止程序运行
    echo 5. 运行"环境诊断.bat"进行详细检查
    pause
    exit /b 1
)

echo.
echo 程序已退出
pause 