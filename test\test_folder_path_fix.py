#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件夹路径修复效果
"""

import sys
import os
import tempfile
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_scenario():
    """创建测试场景"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建测试文件夹
    test_folder = os.path.join(temp_dir, "test_cad_folder")
    os.makedirs(test_folder)
    
    # 创建多个CAD文件
    test_files = []
    
    # 创建DXF文件
    dxf_file = os.path.join(test_folder, "drawing1.dxf")
    with open(dxf_file, 'w') as f:
        f.write("""0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
LINE
8
0
10
0.0
20
0.0
30
0.0
11
100.0
21
100.0
31
0.0
0
ENDSEC
0
EOF
""")
    test_files.append(dxf_file)
    
    # 创建另一个DXF文件
    dxf_file2 = os.path.join(test_folder, "drawing2.dxf")
    with open(dxf_file2, 'w') as f:
        f.write("""0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
CIRCLE
8
0
10
50.0
20
50.0
30
0.0
40
25.0
0
ENDSEC
0
EOF
""")
    test_files.append(dxf_file2)
    
    # 创建非CAD文件
    txt_file = os.path.join(test_folder, "readme.txt")
    with open(txt_file, 'w') as f:
        f.write("This is a test folder with CAD files")
    
    return temp_dir, test_folder, test_files

def test_folder_path_consistency():
    """测试文件夹路径一致性"""
    print("=== 测试文件夹路径一致性 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        print(f"创建测试文件夹: {test_folder}")
        print(f"创建测试文件: {[os.path.basename(f) for f in test_files]}")
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 步骤1: 模拟选择文件夹
        print(f"\n📁 步骤1: 选择文件夹")
        app.folder_var.set(test_folder)
        app.current_folder = test_folder
        
        # 扫描文件夹
        app._scan_folder_files()
        
        print(f"  选择的文件夹: {app.folder_var.get()}")
        print(f"  当前文件夹: {app.current_folder}")
        print(f"  找到的文件数: {len(app.all_files)}")
        
        # 验证路径一致性
        folder_path_correct = app.folder_var.get() == test_folder
        current_folder_correct = app.current_folder == test_folder
        files_found = len(app.all_files) >= 2
        
        print(f"  文件夹路径正确: {folder_path_correct}")
        print(f"  当前文件夹正确: {current_folder_correct}")
        print(f"  找到文件: {files_found}")
        
        if not (folder_path_correct and current_folder_correct and files_found):
            print("❌ 步骤1失败")
            return False
        
        print("✅ 步骤1通过")
        
        # 步骤2: 模拟开始处理
        print(f"\n🚀 步骤2: 开始处理")
        
        # 记录处理前的路径
        before_folder_var = app.folder_var.get()
        before_current_folder = app.current_folder
        
        print(f"  处理前 folder_var: {before_folder_var}")
        print(f"  处理前 current_folder: {before_current_folder}")
        
        # 模拟开始处理（不实际运行完整处理）
        if app.all_files:
            first_file = app.all_files[0]
            print(f"  准备处理第一个文件: {os.path.basename(first_file)}")
            
            # 调用_start_file_processing方法
            app._start_file_processing(first_file)
            
            # 记录处理后的路径
            after_folder_var = app.folder_var.get()
            after_current_folder = app.current_folder
            
            print(f"  处理后 folder_var: {after_folder_var}")
            print(f"  处理后 current_folder: {after_current_folder}")
            
            # 验证路径是否保持为文件夹路径
            folder_var_unchanged = after_folder_var == test_folder
            current_folder_unchanged = after_current_folder == test_folder
            
            # 检查路径是否被错误设置为文件路径
            folder_var_not_file = not after_folder_var.endswith('.dxf')
            
            print(f"  folder_var 保持文件夹路径: {folder_var_unchanged}")
            print(f"  current_folder 保持不变: {current_folder_unchanged}")
            print(f"  folder_var 不是文件路径: {folder_var_not_file}")
            
            if folder_var_unchanged and current_folder_unchanged and folder_var_not_file:
                print("✅ 步骤2通过 - 路径保持一致")
                step2_result = True
            else:
                print("❌ 步骤2失败 - 路径被错误修改")
                step2_result = False
        else:
            print("❌ 没有找到文件进行测试")
            step2_result = False
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        return step2_result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_file_processing():
    """测试多文件处理时的路径一致性"""
    print("\n=== 测试多文件处理路径一致性 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        print(f"创建测试文件夹: {test_folder}")
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 设置文件夹并扫描
        app.folder_var.set(test_folder)
        app.current_folder = test_folder
        app._scan_folder_files()
        
        print(f"找到 {len(app.all_files)} 个文件")
        
        # 测试处理每个文件时路径是否保持一致
        all_tests_passed = True
        
        for i, file_path in enumerate(app.all_files):
            file_name = os.path.basename(file_path)
            print(f"\n  测试文件 {i+1}: {file_name}")
            
            # 记录处理前的路径
            before_folder_var = app.folder_var.get()
            
            # 模拟处理文件
            app._start_file_processing(file_path)
            
            # 检查处理后的路径
            after_folder_var = app.folder_var.get()
            
            print(f"    处理前: {before_folder_var}")
            print(f"    处理后: {after_folder_var}")
            
            # 验证路径是否保持为文件夹路径
            path_correct = after_folder_var == test_folder
            not_file_path = not after_folder_var.endswith(('.dxf', '.dwg'))
            
            if path_correct and not_file_path:
                print(f"    ✅ 文件 {i+1} 路径正确")
            else:
                print(f"    ❌ 文件 {i+1} 路径错误")
                all_tests_passed = False
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ 多文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件夹路径修复测试")
    print("=" * 50)
    print("目标: 验证处理文件时文件夹路径不会被错误修改为文件路径")
    print()
    
    tests = [
        ("文件夹路径一致性", test_folder_path_consistency),
        ("多文件处理路径一致性", test_multiple_file_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件夹路径修复成功。")
        print("\n📋 修复效果:")
        print("✅ 选择文件夹后路径保持为文件夹路径")
        print("✅ 开始处理时路径不会被修改为文件路径")
        print("✅ 多文件处理时路径保持一致")
        print("✅ folder_var 始终指向文件夹而不是文件")
        
        print("\n🎯 用户使用效果:")
        print("• 选择文件夹后显示正确的文件夹路径")
        print("• 开始处理时路径不会变成文件路径")
        print("• 不会再出现'未找到DXF文件'的错误")
        print("• 界面显示的路径始终保持一致")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        
        if passed > 0:
            print("💡 部分功能正常，可能只是特定场景的问题。")

if __name__ == "__main__":
    main()
