#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件下拉菜单更新修复效果
"""

import sys
import os
import tempfile
import tkinter as tk
import time
import threading

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_scenario():
    """创建测试场景"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建测试文件夹
    test_folder = os.path.join(temp_dir, "test_cad_folder")
    os.makedirs(test_folder)
    
    # 创建多个CAD文件
    test_files = []
    
    for i in range(3):
        dxf_file = os.path.join(test_folder, f"drawing_{i+1}.dxf")
        with open(dxf_file, 'w') as f:
            f.write(f"""0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
LINE
8
0
10
{i * 10}
20
{i * 10}
30
0.0
11
{i * 10 + 100}
21
{i * 10 + 100}
31
0.0
0
ENDSEC
0
EOF
""")
        test_files.append(dxf_file)
    
    return temp_dir, test_folder, test_files

def test_file_combo_initial_state():
    """测试文件下拉菜单的初始状态"""
    print("=== 测试文件下拉菜单初始状态 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        print(f"创建测试文件夹: {test_folder}")
        print(f"创建测试文件: {[os.path.basename(f) for f in test_files]}")
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 设置文件夹并扫描
        app.folder_var.set(test_folder)
        app.current_folder = test_folder
        app._scan_folder_files()
        
        print(f"\n📋 扫描结果:")
        print(f"  找到文件数: {len(app.all_files)}")
        print(f"  文件状态数: {len(app.file_status)}")
        
        # 检查文件状态
        for file_path in app.all_files:
            file_name = os.path.basename(file_path)
            if file_name in app.file_status:
                status = app.file_status[file_name]
                print(f"  {file_name}: 处理={status['processing_status']}, 标注={status['annotation_status']}")
            else:
                print(f"  {file_name}: ❌ 状态缺失")
        
        # 检查下拉菜单内容
        if app.file_combo:
            combo_values = app.file_combo['values']
            print(f"\n📋 下拉菜单内容:")
            print(f"  选项数量: {len(combo_values)}")
            for i, value in enumerate(combo_values):
                print(f"  {i+1}. {value}")
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        # 验证结果
        files_found = len(app.all_files) == 3
        status_complete = len(app.file_status) == 3
        combo_updated = len(combo_values) == 3 if app.file_combo else False
        
        return files_found and status_complete and combo_updated
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_callback_setup():
    """测试状态回调设置"""
    print("\n=== 测试状态回调设置 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 设置文件夹并扫描
        app.folder_var.set(test_folder)
        app.current_folder = test_folder
        app._scan_folder_files()
        
        print(f"📋 准备测试状态回调:")
        print(f"  文件数量: {len(app.all_files)}")
        
        if app.all_files:
            first_file = app.all_files[0]
            file_name = os.path.basename(first_file)
            
            print(f"  测试文件: {file_name}")
            
            # 记录处理前的状态
            before_status = app.file_status[file_name].copy()
            print(f"  处理前状态: {before_status}")
            
            # 模拟开始处理（不实际运行完整处理）
            app._start_file_processing(first_file)
            
            # 检查处理器是否正确设置
            processor_exists = app.processor is not None
            print(f"  处理器存在: {processor_exists}")
            
            if processor_exists:
                # 检查回调是否设置
                has_status_callback = hasattr(app.processor, 'status_callback') and app.processor.status_callback is not None
                has_progress_callback = hasattr(app.processor, 'progress_callback') and app.processor.progress_callback is not None
                
                print(f"  状态回调设置: {has_status_callback}")
                print(f"  进度回调设置: {has_progress_callback}")
                
                # 模拟状态更新
                print(f"\n🔄 模拟状态更新:")
                app.on_status_update("processing", "开始处理")
                
                # 检查状态是否更新
                after_processing_status = app.file_status[file_name].copy()
                print(f"  处理中状态: {after_processing_status}")
                
                # 模拟完成状态
                app.on_status_update("completed", "处理完成")
                
                # 检查最终状态
                final_status = app.file_status[file_name].copy()
                print(f"  最终状态: {final_status}")
                
                # 验证状态变化
                status_changed = final_status['processing_status'] != before_status['processing_status']
                print(f"  状态已更新: {status_changed}")
                
                root.destroy()
                
                # 清理测试目录
                import shutil
                shutil.rmtree(temp_dir)
                
                return processor_exists and has_status_callback and status_changed
            else:
                print("❌ 处理器未创建")
                root.destroy()
                import shutil
                shutil.rmtree(temp_dir)
                return False
        else:
            print("❌ 没有找到测试文件")
            root.destroy()
            import shutil
            shutil.rmtree(temp_dir)
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_update_after_completion():
    """测试完成后下拉菜单更新"""
    print("\n=== 测试完成后下拉菜单更新 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 设置文件夹并扫描
        app.folder_var.set(test_folder)
        app.current_folder = test_folder
        app._scan_folder_files()
        
        print(f"📋 测试下拉菜单更新:")
        
        # 记录初始下拉菜单状态
        initial_combo_values = list(app.file_combo['values']) if app.file_combo else []
        print(f"  初始下拉菜单项数: {len(initial_combo_values)}")
        
        # 模拟多个文件的处理完成
        for i, file_path in enumerate(app.all_files):
            file_name = os.path.basename(file_path)
            print(f"\n  模拟文件 {i+1} 完成: {file_name}")
            
            # 设置当前文件
            app.current_file = file_path
            
            # 模拟处理完成
            app.on_status_update("completed", "处理完成")
            
            # 检查下拉菜单是否更新
            current_combo_values = list(app.file_combo['values']) if app.file_combo else []
            print(f"    下拉菜单项数: {len(current_combo_values)}")
            
            # 检查文件状态
            if file_name in app.file_status:
                status = app.file_status[file_name]
                print(f"    文件状态: 处理={status['processing_status']}, 标注={status['annotation_status']}")
            
            # 检查下拉菜单内容是否包含状态信息
            file_in_combo = any(file_name in item for item in current_combo_values)
            print(f"    文件在下拉菜单中: {file_in_combo}")
        
        # 最终验证
        final_combo_values = list(app.file_combo['values']) if app.file_combo else []
        print(f"\n📋 最终下拉菜单内容:")
        for i, value in enumerate(final_combo_values):
            print(f"  {i+1}. {value}")
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        # 验证结果
        combo_has_all_files = len(final_combo_values) == len(test_files)
        all_files_completed = all(
            app.file_status[os.path.basename(f)]['processing_status'] == 'completed' 
            for f in test_files 
            if os.path.basename(f) in app.file_status
        )
        
        return combo_has_all_files and all_files_completed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件下拉菜单更新修复测试")
    print("=" * 50)
    print("目标: 验证文件处理完成后下拉菜单能正确更新文件状态")
    print()
    
    tests = [
        ("文件下拉菜单初始状态", test_file_combo_initial_state),
        ("状态回调设置", test_status_callback_setup),
        ("完成后下拉菜单更新", test_combo_update_after_completion)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件下拉菜单更新修复成功。")
        print("\n📋 修复效果:")
        print("✅ 文件处理完成后状态正确更新")
        print("✅ 下拉菜单显示最新的文件状态")
        print("✅ 状态回调机制正常工作")
        print("✅ 多文件处理时状态管理正确")
        
        print("\n🎯 用户使用效果:")
        print("• 处理完第一个文件后，下拉菜单显示其完成状态")
        print("• 处理完多个文件后，所有文件状态都正确显示")
        print("• 可以清楚看到每个文件的处理和标注状态")
        print("• 文件状态实时更新，无需手动刷新")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        
        if passed > 0:
            print("💡 部分功能正常，可能只是特定场景的问题。")

if __name__ == "__main__":
    main()
