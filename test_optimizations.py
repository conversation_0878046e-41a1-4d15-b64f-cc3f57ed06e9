#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别处理器优化验证脚本
"""

import time
from room_recognition_processor import RoomRecognitionProcessor
from shapely.geometry import Polygon

def test_optimizations():
    """测试所有优化功能"""
    print("🚀 开始房间识别处理器优化验证")
    print("=" * 50)
    
    # 1. 初始化测试
    print("\n1️⃣ 初始化测试")
    processor = RoomRecognitionProcessor()
    print(f"✅ 房间类型数量: {len(processor.room_types)}")
    print(f"✅ 容差设置: {processor.tolerance}")
    
    # 2. 线段提取优化测试
    print("\n2️⃣ 线段提取优化测试")
    test_entities = [
        {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 100, 'y': 100}},
        {'type': 'LINE', 'start_x': 0, 'start_y': 0, 'end_x': 100, 'end_y': 100},
        {'type': 'LWPOLYLINE', 'points': [{'x': 0, 'y': 0}, {'x': 50, 'y': 50}]},
    ]
    
    for i, entity in enumerate(test_entities):
        coords = processor._extract_line_coordinates(entity)
        print(f"  实体 {i+1}: {'✅' if coords else '❌'} {len(coords) if coords else 0} 条线段")
    
    # 3. 房间分类算法增强测试
    print("\n3️⃣ 房间分类算法增强测试")
    test_polygons = [
        ("小房间", Polygon([(0, 0), (150, 0), (150, 200), (0, 200)])),
        ("中等房间", Polygon([(0, 0), (300, 0), (300, 400), (0, 400)])),
        ("大房间", Polygon([(0, 0), (500, 0), (500, 600), (0, 600)])),
        ("狭长房间", Polygon([(0, 0), (100, 0), (100, 500), (0, 500)])),
    ]
    
    for name, polygon in test_polygons:
        room = processor._classify_single_region(polygon)
        print(f"  {name}: {room['type']}, 面积: {room['area']:.0f}, 长宽比: {room['aspect_ratio']:.2f}")
    
    # 4. 空间索引测试
    print("\n4️⃣ 空间索引测试")
    wall_groups = [[
        {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 1000, 'y': 0}},
        {'type': 'LINE', 'start': {'x': 1000, 'y': 0}, 'end': {'x': 1000, 'y': 800}},
    ]]
    door_window_groups = [[
        {'type': 'LINE', 'start': {'x': 500, 'y': 0}, 'end': {'x': 500, 'y': 100}},
    ]]
    
    processor.wall_groups = wall_groups
    processor.door_window_groups = door_window_groups
    
    start_time = time.time()
    processor._build_spatial_index()
    build_time = time.time() - start_time
    
    print(f"  ✅ 空间索引构建耗时: {build_time:.3f}秒")
    print(f"  ✅ 墙体线段数量: {len(processor.wall_lines)}")
    print(f"  ✅ R树索引: {'可用' if processor.spatial_index else '不可用'}")
    
    # 5. 临时线条生成优化测试
    print("\n5️⃣ 临时线条生成优化测试")
    test_points = [(0, 0), (100, 0), (200, 0), (0, 100), (100, 100)]
    temp_lines = processor._create_temp_lines(test_points)
    print(f"  ✅ 从 {len(test_points)} 个点生成 {len(temp_lines)} 条临时线条")
    
    # 6. 并行处理测试
    print("\n6️⃣ 并行处理测试")
    start_time = time.time()
    temp_lines_parallel = processor._process_door_window_groups_parallel()
    parallel_time = time.time() - start_time
    print(f"  ✅ 并行处理耗时: {parallel_time:.3f}秒")
    print(f"  ✅ 生成临时线条: {len(temp_lines_parallel)} 条")
    
    # 7. Lazy Evaluation测试
    print("\n7️⃣ Lazy Evaluation测试")
    
    # 第一次访问
    start_time = time.time()
    rooms1 = processor.rooms_lazy
    first_time = time.time() - start_time
    
    # 第二次访问（缓存）
    start_time = time.time()
    rooms2 = processor.rooms_lazy
    second_time = time.time() - start_time
    
    print(f"  ✅ 第一次计算耗时: {first_time:.3f}秒")
    print(f"  ✅ 第二次访问耗时: {second_time:.3f}秒")
    if second_time > 0:
        print(f"  📊 性能提升: {first_time/second_time:.1f}倍")
    
    # 8. 完整流程测试
    print("\n8️⃣ 完整流程测试")
    start_time = time.time()
    result = processor.process_room_recognition(wall_groups, door_window_groups)
    total_time = time.time() - start_time
    
    if result:
        print(f"  ✅ 完整流程耗时: {total_time:.3f}秒")
        print(f"  ✅ 识别房间数量: {len(result['rooms'])}")
        print(f"  ✅ 建筑外轮廓: {'已识别' if result['building_outline'] else '未识别'}")
    else:
        print("  ❌ 完整流程失败")
    
    # 9. 调试可视化测试
    print("\n9️⃣ 调试可视化测试")
    try:
        processor.debug_visualization("test_debug.html")
        print("  ✅ 调试可视化文件已生成")
    except Exception as e:
        print(f"  ❌ 调试可视化失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 所有优化验证完成！")

if __name__ == "__main__":
    test_optimizations()
