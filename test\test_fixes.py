#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复的问题
"""

import sys
import os

def test_imports():
    """测试导入是否正常"""
    try:
        from main_enhanced_merged import EnhancedCADProcessor, EnhancedCADApp
        from cad_visualizer import CADVisualizer
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_processor_creation():
    """测试处理器创建"""
    try:
        from main_enhanced_merged import EnhancedCADProcessor
        processor = EnhancedCADProcessor()
        print("✓ 处理器创建成功")
        return True
    except Exception as e:
        print(f"✗ 处理器创建失败: {e}")
        return False

def test_visualizer_creation():
    """测试可视化器创建"""
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        print("✓ 可视化器创建成功")
        return True
    except Exception as e:
        print(f"✗ 可视化器创建失败: {e}")
        return False

def test_visualizer_methods():
    """测试可视化器方法"""
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        # 测试空实体组
        visualizer.visualize_entity_group([])
        print("✓ 空实体组可视化测试通过")
        
        # 测试全图概览方法签名
        visualizer.visualize_overview(
            all_entities=[],
            current_group_entities=None,
            labeled_entities=None,
            processor=None,
            current_group_index=1,
            wall_fills=None,
            wall_fill_processor=None
        )
        print("✓ 全图概览方法签名测试通过")
        
        return True
    except Exception as e:
        print(f"✗ 可视化器方法测试失败: {e}")
        return False

def test_processor_methods():
    """测试处理器方法"""
    try:
        from main_enhanced_merged import EnhancedCADProcessor
        processor = EnhancedCADProcessor()
        
        # 测试组状态更新
        processor._update_groups_info()
        print("✓ 组状态更新测试通过")
        
        # 测试待处理组更新
        processor._update_pending_manual_groups()
        print("✓ 待处理组更新测试通过")
        
        return True
    except Exception as e:
        print(f"✗ 处理器方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_processor_creation,
        test_visualizer_creation,
        test_visualizer_methods,
        test_processor_methods
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
