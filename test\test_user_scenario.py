#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户实际使用场景
"""

import sys
import os
import tempfile
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_real_test_folder():
    """创建真实的测试文件夹"""
    print("=== 创建真实测试环境 ===")
    
    # 在当前目录创建测试文件夹
    current_dir = os.path.dirname(os.path.abspath(__file__))
    test_folder = os.path.join(current_dir, "test_dxf_files")
    
    # 如果文件夹已存在，先删除
    if os.path.exists(test_folder):
        import shutil
        shutil.rmtree(test_folder)
    
    # 创建文件夹
    os.makedirs(test_folder)
    print(f"测试文件夹: {test_folder}")
    
    # 创建测试DXF文件
    test_files = [
        'building1.dxf',
        'building2.DXF',  # 大写扩展名
        'plan.dwg',
        'section.dxf',
        'readme.txt'  # 非CAD文件
    ]
    
    for file_name in test_files:
        file_path = os.path.join(test_folder, file_name)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# 测试DXF文件: {file_name}\n")
            f.write("# 这是一个模拟的CAD文件内容\n")
            f.write("0\nSECTION\n2\nENTITIES\n")
            f.write("0\nLINE\n8\n0\n10\n0.0\n20\n0.0\n11\n10.0\n21\n10.0\n")
            f.write("0\nCIRCLE\n8\n0\n10\n5.0\n20\n5.0\n40\n2.5\n")
            f.write("0\nENDSEC\n0\nEOF\n")
    
    print(f"创建了 {len(test_files)} 个测试文件:")
    for file_name in test_files:
        file_path = os.path.join(test_folder, file_name)
        print(f"  - {file_name} ({os.path.getsize(file_path)} bytes)")
    
    return test_folder

def test_complete_user_workflow():
    """测试完整的用户工作流程"""
    print("\n=== 测试完整用户工作流程 ===")
    
    # 创建测试文件夹
    test_folder = create_real_test_folder()
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = EnhancedCADAppV2(root)
        print("✅ 应用创建成功")
        
        # 步骤1: 模拟选择文件夹
        print(f"\n📁 步骤1: 选择文件夹 {test_folder}")
        app.folder_var.set(test_folder)
        folder_selected = app.folder_var.get()
        print(f"folder_var设置为: '{folder_selected}'")
        
        # 步骤2: 开始处理
        print(f"\n🚀 步骤2: 开始处理")
        
        # 手动调用start_processing的逻辑
        folder = app.folder_var.get()
        if not folder:
            print("❌ 文件夹未设置")
            return False
        
        print(f"处理文件夹: {folder}")
        
        # 初始化文件管理
        app.current_folder = folder
        print(f"设置current_folder: {app.current_folder}")
        
        # 扫描文件
        app._scan_folder_files()
        
        # 检查结果
        print(f"\n📊 扫描结果:")
        print(f"找到 {len(app.all_files)} 个CAD文件")
        print(f"文件状态记录: {len(app.file_status)} 个")
        
        if app.all_files:
            print("✅ 找到CAD文件:")
            for i, file_path in enumerate(app.all_files):
                file_name = os.path.basename(file_path)
                status = app.file_status.get(file_name, {})
                print(f"  {i+1}. {file_name}")
                print(f"     处理状态: {status.get('processing_status', '未知')}")
                print(f"     标注状态: {status.get('annotation_status', '未知')}")
            
            # 步骤3: 测试文件选择下拉菜单
            print(f"\n📋 步骤3: 测试文件选择下拉菜单")
            app.current_file = app.all_files[0]  # 设置当前文件
            app.update_file_combo()
            
            if app.file_combo and hasattr(app.file_combo, 'cget'):
                try:
                    values = app.file_combo.cget('values')
                    print(f"下拉菜单选项: {values}")
                except:
                    print("下拉菜单未完全初始化（正常，因为界面未显示）")
            
            print("✅ 用户工作流程测试成功")
            return True
        else:
            print("❌ 没有找到CAD文件")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            root.destroy()
        except:
            pass

def test_manual_folder_check():
    """手动检查文件夹内容"""
    print("\n=== 手动检查文件夹内容 ===")
    
    # 创建测试文件夹
    test_folder = create_real_test_folder()
    
    print(f"文件夹路径: {test_folder}")
    print(f"文件夹存在: {os.path.exists(test_folder)}")
    print(f"是目录: {os.path.isdir(test_folder)}")
    
    if os.path.exists(test_folder):
        files = os.listdir(test_folder)
        print(f"文件夹中的文件 ({len(files)} 个):")
        
        for file_name in files:
            file_path = os.path.join(test_folder, file_name)
            is_file = os.path.isfile(file_path)
            size = os.path.getsize(file_path) if is_file else 0
            _, ext = os.path.splitext(file_name.lower())
            
            print(f"  - {file_name}")
            print(f"    类型: {'文件' if is_file else '目录'}")
            print(f"    大小: {size} bytes")
            print(f"    扩展名: '{ext}'")
            print(f"    是CAD文件: {ext in ['.dxf', '.dwg']}")
    
    return test_folder

def cleanup_test_folder(test_folder):
    """清理测试文件夹"""
    try:
        if os.path.exists(test_folder):
            import shutil
            shutil.rmtree(test_folder)
            print(f"🧹 测试文件夹已清理: {test_folder}")
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")
        print(f"请手动删除: {test_folder}")

def main():
    """主测试函数"""
    print("用户场景测试")
    print("=" * 50)
    
    test_folder = None
    
    try:
        # 测试1: 手动检查文件夹
        test_folder = test_manual_folder_check()
        
        # 测试2: 完整工作流程
        success = test_complete_user_workflow()
        
        if success:
            print("\n🎉 用户场景测试成功！")
            print("\n📋 测试结果:")
            print("✅ 文件夹创建正常")
            print("✅ 文件扫描正常")
            print("✅ 文件状态管理正常")
            print("✅ 应用逻辑正常")
            
            print(f"\n💡 如果用户仍然遇到问题，可能的原因:")
            print("1. 文件夹路径包含特殊字符")
            print("2. 文件权限问题")
            print("3. DXF文件损坏或格式不正确")
            print("4. 文件夹中确实没有.dxf或.dwg文件")
            
            print(f"\n🔧 建议用户:")
            print("1. 检查文件夹中是否确实有.dxf或.dwg文件")
            print("2. 确认文件扩展名正确（不是.DXF.txt这样的双扩展名）")
            print("3. 尝试在不同的文件夹中测试")
            print("4. 查看控制台输出的调试信息")
        else:
            print("\n❌ 用户场景测试失败")
    
    finally:
        # 清理测试环境
        if test_folder:
            cleanup_test_folder(test_folder)

if __name__ == "__main__":
    main()
