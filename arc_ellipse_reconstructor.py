#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圆弧和椭圆重构器
基于用户提供的思路：获取中心点、角度、完整图形、端点及取样点，重新构建准确的图形
"""

import numpy as np
import math
from typing import List, Dict, Any, Tuple

class ArcEllipseReconstructor:
    """圆弧和椭圆重构器"""
    
    def __init__(self, sample_points_count: int = 50):
        """
        初始化重构器
        
        Args:
            sample_points_count: 采样点数量，影响重构精度
        """
        self.sample_points_count = sample_points_count
    
    def reconstruct_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        重构实体列表中的圆弧和椭圆
        
        Args:
            entities: 原始实体列表
            
        Returns:
            重构后的实体列表
        """
        reconstructed_entities = []
        
        for entity in entities:
            if entity['type'] == 'ARC':
                try:
                    new_entity = self.reconstruct_arc(entity)
                    reconstructed_entities.append(new_entity)
                    print(f"✅ 重构圆弧成功: 中心{entity['center']}, 半径{entity['radius']}")
                except Exception as e:
                    print(f"⚠️ 重构圆弧失败: {e}, 保留原实体")
                    reconstructed_entities.append(entity)
            elif entity['type'] == 'ELLIPSE':
                try:
                    new_entity = self.reconstruct_ellipse(entity)
                    reconstructed_entities.append(new_entity)
                    print(f"✅ 重构椭圆成功: 中心{entity['center']}")
                except Exception as e:
                    print(f"⚠️ 重构椭圆失败: {e}, 保留原实体")
                    reconstructed_entities.append(entity)
            else:
                # 其他实体保持不变
                reconstructed_entities.append(entity)
        
        return reconstructed_entities
    
    def reconstruct_arc(self, arc_entity: Dict[str, Any]) -> Dict[str, Any]:
        """
        重构圆弧实体
        
        Args:
            arc_entity: 原始圆弧实体
            
        Returns:
            重构后的多段线实体
        """
        # 1. 获取完整圆的基本参数
        center = np.array(arc_entity['center'])
        radius = float(arc_entity['radius'])
        start_angle = float(arc_entity.get('start_angle', 0))
        end_angle = float(arc_entity.get('end_angle', 360))
        
        # 2. 处理缩放和镜像变换
        scale_x = arc_entity.get('scale_x', 1.0)
        scale_y = arc_entity.get('scale_y', 1.0)
        is_mirrored = self._check_mirrored(arc_entity)
        
        # 3. 计算实际半径（考虑缩放）
        if scale_x != 1.0 or scale_y != 1.0:
            actual_radius = radius * math.sqrt(abs(scale_x * scale_y))
        else:
            actual_radius = radius
        
        # 4. 生成完整圆的采样点用于定位方向
        full_circle_points = self._generate_full_circle_sample_points(center, actual_radius)

        # 5. 计算原始圆弧的端点
        start_point, end_point = self._calculate_arc_endpoints(
            center, actual_radius, start_angle, end_angle, is_mirrored
        )

        # 6. 通过采样点定位在完整圆上的正确位置
        corrected_start_angle, corrected_end_angle = self._locate_arc_on_full_circle(
            center, actual_radius, start_point, end_point, full_circle_points
        )

        # 7. 从完整图形中截取正确的圆弧段
        final_sample_points = self._extract_arc_from_full_circle(
            center, actual_radius, corrected_start_angle, corrected_end_angle
        )

        # 8. 创建新的多段线实体
        return {
            'type': 'POLYLINE',
            'points': final_sample_points.tolist(),
            'layer': arc_entity.get('layer', 'default'),
            'color': arc_entity.get('color', 'black'),
            'original_type': 'ARC',
            'original_data': arc_entity,
            'reconstruction_info': {
                'center': center.tolist(),
                'radius': actual_radius,
                'start_angle': corrected_start_angle,
                'end_angle': corrected_end_angle,
                'start_point': start_point.tolist(),
                'end_point': end_point.tolist(),
                'sample_points_count': len(final_sample_points),
                'is_mirrored': is_mirrored
            }
        }
    
    def reconstruct_ellipse(self, ellipse_entity: Dict[str, Any]) -> Dict[str, Any]:
        """
        重构椭圆实体 - 基于完整图形和采样点重新截取

        Args:
            ellipse_entity: 原始椭圆实体

        Returns:
            重构后的多段线实体
        """
        # 1. 获取完整椭圆的基本参数
        center = np.array(ellipse_entity['center'])
        major_axis = np.array(ellipse_entity['major_axis'])
        ratio = float(ellipse_entity.get('ratio', 1.0))
        start_param = float(ellipse_entity.get('start_param', 0))
        end_param = float(ellipse_entity.get('end_param', 2 * np.pi))

        # 2. 计算椭圆几何参数
        a = float(np.linalg.norm(major_axis))  # 长轴长度
        b = float(a * ratio)  # 短轴长度
        angle = float(np.arctan2(major_axis[1], major_axis[0]))  # 旋转角度

        # 3. 处理缩放和镜像变换
        scale_x = ellipse_entity.get('scale_x', 1.0)
        scale_y = ellipse_entity.get('scale_y', 1.0)
        is_mirrored = self._check_mirrored(ellipse_entity)

        if is_mirrored:
            # 镜像变换处理
            angle = -angle
            if scale_x != 1.0 or scale_y != 1.0:
                scale_factor = math.sqrt(abs(scale_x * scale_y))
                a *= scale_factor
                b *= scale_factor

        # 4. 生成完整椭圆的采样点用于定位方向
        full_ellipse_points = self._generate_full_ellipse_sample_points(center, a, b, angle)

        # 5. 计算原始椭圆弧的端点
        start_point, end_point = self._calculate_ellipse_endpoints(
            center, a, b, angle, start_param, end_param, is_mirrored
        )

        # 6. 通过采样点定位在完整椭圆上的正确位置
        corrected_start_param, corrected_end_param = self._locate_ellipse_arc_on_full_ellipse(
            center, a, b, angle, start_point, end_point, full_ellipse_points
        )

        # 7. 从完整图形中截取正确的椭圆弧段
        final_sample_points = self._extract_ellipse_arc_from_full_ellipse(
            center, a, b, angle, corrected_start_param, corrected_end_param
        )

        # 8. 创建新的多段线实体
        return {
            'type': 'POLYLINE',
            'points': final_sample_points.tolist(),
            'layer': ellipse_entity.get('layer', 'default'),
            'color': ellipse_entity.get('color', 'black'),
            'original_type': 'ELLIPSE',
            'original_data': ellipse_entity,
            'reconstruction_info': {
                'center': center.tolist(),
                'a': a,
                'b': b,
                'angle': angle,
                'start_param': corrected_start_param,
                'end_param': corrected_end_param,
                'start_point': start_point.tolist(),
                'end_point': end_point.tolist(),
                'sample_points_count': len(final_sample_points),
                'is_mirrored': is_mirrored
            }
        }
    
    def _check_mirrored(self, entity: Dict[str, Any]) -> bool:
        """检查实体是否被镜像"""
        scale_x = entity.get('scale_x', 1.0)
        scale_y = entity.get('scale_y', 1.0)
        
        # 如果缩放因子的乘积为负，说明有镜像
        mirror_effect = scale_x * scale_y
        return mirror_effect < 0
    
    def _get_corrected_arc_angles(self, start_angle: float, end_angle: float, 
                                  is_mirrored: bool) -> Tuple[float, float]:
        """获取修正后的圆弧角度"""
        # 角度单位转换：如果角度值很小，可能是弧度制
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)
        
        # 标准化角度到0-360度范围
        start_angle = start_angle % 360
        end_angle = end_angle % 360
        
        # 处理镜像
        if is_mirrored:
            # 镜像时反转角度方向并交换起止角度
            original_start = start_angle
            original_end = end_angle
            start_angle = (360 - original_end) % 360
            end_angle = (360 - original_start) % 360
        
        # 处理跨越0度的情况
        if end_angle < start_angle:
            if (start_angle - end_angle) > 180:
                end_angle += 360
            else:
                start_angle, end_angle = end_angle, start_angle
        
        return start_angle, end_angle
    
    def _get_corrected_ellipse_params(self, start_param: float, end_param: float, 
                                      is_mirrored: bool) -> Tuple[float, float]:
        """获取修正后的椭圆参数"""
        if is_mirrored and abs(end_param - start_param) < 2 * np.pi - 0.1:
            # 非完整椭圆的镜像处理
            original_start = start_param
            original_end = end_param
            start_param = 2 * np.pi - original_end
            end_param = 2 * np.pi - original_start
        
        return start_param, end_param
    
    def _generate_arc_sample_points(self, center: np.ndarray, radius: float, 
                                    start_angle: float, end_angle: float) -> np.ndarray:
        """生成圆弧采样点"""
        # 计算角度范围
        if end_angle > start_angle:
            angle_range = end_angle - start_angle
        else:
            angle_range = (end_angle + 360) - start_angle
        
        # 根据角度范围调整采样点数量
        num_points = max(int(self.sample_points_count * angle_range / 360), 3)
        
        # 生成角度序列
        if end_angle > start_angle:
            angles = np.linspace(start_angle, end_angle, num_points)
        else:
            angles = np.linspace(start_angle, end_angle + 360, num_points)
        
        # 转换为弧度
        angles_rad = np.radians(angles)
        
        # 计算采样点
        x = center[0] + radius * np.cos(angles_rad)
        y = center[1] + radius * np.sin(angles_rad)
        
        return np.column_stack((x, y))
    
    def _generate_ellipse_sample_points(self, center: np.ndarray, a: float, b: float, 
                                        angle: float, start_param: float, 
                                        end_param: float) -> np.ndarray:
        """生成椭圆采样点"""
        # 计算参数范围
        if end_param > start_param:
            param_range = end_param - start_param
        else:
            param_range = (end_param + 2*np.pi) - start_param
        
        # 根据参数范围调整采样点数量
        num_points = max(int(self.sample_points_count * param_range / (2*np.pi)), 3)
        
        # 生成参数序列
        if end_param > start_param:
            params = np.linspace(start_param, end_param, num_points)
        else:
            params = np.linspace(start_param, end_param + 2*np.pi, num_points)
        
        # 计算椭圆上的点（标准位置）
        x_std = a * np.cos(params)
        y_std = b * np.sin(params)
        
        # 应用旋转变换
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)
        
        x_rot = x_std * cos_angle - y_std * sin_angle
        y_rot = x_std * sin_angle + y_std * cos_angle
        
        # 平移到中心位置
        x = center[0] + x_rot
        y = center[1] + y_rot
        
        return np.column_stack((x, y))

    def _generate_full_circle_sample_points(self, center: np.ndarray, radius: float) -> np.ndarray:
        """生成完整圆的采样点用于定位"""
        angles = np.linspace(0, 2*np.pi, self.sample_points_count * 2, endpoint=False)
        x = center[0] + radius * np.cos(angles)
        y = center[1] + radius * np.sin(angles)
        return np.column_stack((x, y))

    def _generate_full_ellipse_sample_points(self, center: np.ndarray, a: float, b: float, angle: float) -> np.ndarray:
        """生成完整椭圆的采样点用于定位"""
        params = np.linspace(0, 2*np.pi, self.sample_points_count * 2, endpoint=False)

        # 计算椭圆上的点（标准位置）
        x_std = a * np.cos(params)
        y_std = b * np.sin(params)

        # 应用旋转变换
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)

        x_rot = x_std * cos_angle - y_std * sin_angle
        y_rot = x_std * sin_angle + y_std * cos_angle

        # 平移到中心位置
        x = center[0] + x_rot
        y = center[1] + y_rot

        return np.column_stack((x, y))

    def _calculate_arc_endpoints(self, center: np.ndarray, radius: float,
                                start_angle: float, end_angle: float, is_mirrored: bool) -> Tuple[np.ndarray, np.ndarray]:
        """计算圆弧的端点"""
        # 角度单位转换
        if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
            start_angle = np.degrees(start_angle)
            end_angle = np.degrees(end_angle)

        # 处理镜像
        if is_mirrored:
            original_start = start_angle
            original_end = end_angle
            start_angle = (360 - original_end) % 360
            end_angle = (360 - original_start) % 360

        # 计算端点
        start_rad = np.radians(start_angle)
        end_rad = np.radians(end_angle)

        start_point = center + radius * np.array([np.cos(start_rad), np.sin(start_rad)])
        end_point = center + radius * np.array([np.cos(end_rad), np.sin(end_rad)])

        return start_point, end_point

    def _calculate_ellipse_endpoints(self, center: np.ndarray, a: float, b: float, angle: float,
                                   start_param: float, end_param: float, is_mirrored: bool) -> Tuple[np.ndarray, np.ndarray]:
        """计算椭圆弧的端点"""
        # 处理镜像
        if is_mirrored and abs(end_param - start_param) < 2 * np.pi - 0.1:
            original_start = start_param
            original_end = end_param
            start_param = 2 * np.pi - original_end
            end_param = 2 * np.pi - original_start

        # 计算端点（标准位置）
        start_x_std = a * np.cos(start_param)
        start_y_std = b * np.sin(start_param)
        end_x_std = a * np.cos(end_param)
        end_y_std = b * np.sin(end_param)

        # 应用旋转变换
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)

        start_x_rot = start_x_std * cos_angle - start_y_std * sin_angle
        start_y_rot = start_x_std * sin_angle + start_y_std * cos_angle
        end_x_rot = end_x_std * cos_angle - end_y_std * sin_angle
        end_y_rot = end_x_std * sin_angle + end_y_std * cos_angle

        # 平移到中心位置
        start_point = center + np.array([start_x_rot, start_y_rot])
        end_point = center + np.array([end_x_rot, end_y_rot])

        return start_point, end_point

    def _locate_arc_on_full_circle(self, center: np.ndarray, radius: float,
                                  start_point: np.ndarray, end_point: np.ndarray,
                                  full_circle_points: np.ndarray) -> Tuple[float, float]:
        """通过端点在完整圆上定位正确的角度位置"""
        # 找到最接近起点和终点的采样点
        start_distances = np.linalg.norm(full_circle_points - start_point, axis=1)
        end_distances = np.linalg.norm(full_circle_points - end_point, axis=1)

        start_idx = np.argmin(start_distances)
        end_idx = np.argmin(end_distances)

        # 计算对应的角度
        start_angle = np.degrees(np.arctan2(
            full_circle_points[start_idx, 1] - center[1],
            full_circle_points[start_idx, 0] - center[0]
        )) % 360

        end_angle = np.degrees(np.arctan2(
            full_circle_points[end_idx, 1] - center[1],
            full_circle_points[end_idx, 0] - center[0]
        )) % 360

        return start_angle, end_angle

    def _locate_ellipse_arc_on_full_ellipse(self, center: np.ndarray, a: float, b: float, angle: float,
                                           start_point: np.ndarray, end_point: np.ndarray,
                                           full_ellipse_points: np.ndarray) -> Tuple[float, float]:
        """通过端点在完整椭圆上定位正确的参数位置"""
        # 找到最接近起点和终点的采样点
        start_distances = np.linalg.norm(full_ellipse_points - start_point, axis=1)
        end_distances = np.linalg.norm(full_ellipse_points - end_point, axis=1)

        start_idx = np.argmin(start_distances)
        end_idx = np.argmin(end_distances)

        # 计算对应的参数
        params = np.linspace(0, 2*np.pi, len(full_ellipse_points), endpoint=False)
        start_param = params[start_idx]
        end_param = params[end_idx]

        return start_param, end_param

    def _extract_arc_from_full_circle(self, center: np.ndarray, radius: float,
                                     start_angle: float, end_angle: float) -> np.ndarray:
        """从完整圆中截取圆弧段"""
        # 处理角度范围
        if end_angle < start_angle:
            if (start_angle - end_angle) > 180:
                end_angle += 360

        # 计算角度范围
        if end_angle > start_angle:
            angle_range = end_angle - start_angle
        else:
            angle_range = (end_angle + 360) - start_angle

        # 根据角度范围调整采样点数量
        num_points = max(int(self.sample_points_count * angle_range / 360), 3)

        # 生成角度序列
        if end_angle > start_angle:
            angles = np.linspace(start_angle, end_angle, num_points)
        else:
            angles = np.linspace(start_angle, end_angle + 360, num_points)

        # 转换为弧度并计算点
        angles_rad = np.radians(angles)
        x = center[0] + radius * np.cos(angles_rad)
        y = center[1] + radius * np.sin(angles_rad)

        return np.column_stack((x, y))

    def _extract_ellipse_arc_from_full_ellipse(self, center: np.ndarray, a: float, b: float, angle: float,
                                              start_param: float, end_param: float) -> np.ndarray:
        """从完整椭圆中截取椭圆弧段"""
        # 处理参数范围
        if end_param < start_param:
            end_param += 2*np.pi

        # 计算参数范围
        param_range = end_param - start_param

        # 根据参数范围调整采样点数量
        num_points = max(int(self.sample_points_count * param_range / (2*np.pi)), 3)

        # 生成参数序列
        params = np.linspace(start_param, end_param, num_points)

        # 计算椭圆上的点（标准位置）
        x_std = a * np.cos(params)
        y_std = b * np.sin(params)

        # 应用旋转变换
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)

        x_rot = x_std * cos_angle - y_std * sin_angle
        y_rot = x_std * sin_angle + y_std * cos_angle

        # 平移到中心位置
        x = center[0] + x_rot
        y = center[1] + y_rot

        return np.column_stack((x, y))

# 使用示例
def example_usage():
    """使用示例"""
    # 创建重构器
    reconstructor = ArcEllipseReconstructor(sample_points_count=50)
    
    # 示例实体
    test_entities = [
        {
            'type': 'ARC',
            'center': (50, 50),
            'radius': 25,
            'start_angle': 0,
            'end_angle': 90,
            'scale_x': -1.0,
            'scale_y': 1.0,
            'layer': 'test'
        },
        {
            'type': 'ELLIPSE',
            'center': (100, 100),
            'major_axis': (30, 10),
            'ratio': 0.6,
            'start_param': 0,
            'end_param': np.pi,
            'scale_x': 1.0,
            'scale_y': -1.0,
            'layer': 'test'
        }
    ]
    
    # 重构实体
    reconstructed = reconstructor.reconstruct_entities(test_entities)
    
    print(f"原始实体数: {len(test_entities)}")
    print(f"重构后实体数: {len(reconstructed)}")
    
    for i, entity in enumerate(reconstructed):
        if 'reconstruction_info' in entity:
            info = entity['reconstruction_info']
            print(f"实体 {i+1}: {entity['original_type']} -> POLYLINE")
            print(f"  采样点数: {info['sample_points_count']}")

if __name__ == "__main__":
    example_usage()
