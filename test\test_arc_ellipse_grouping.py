#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试圆弧椭圆分组算法优化
"""

import sys
import os
import math

def test_arc_distance_calculation():
    """测试圆弧距离计算优化"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建测试圆弧
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90
        }
        
        # 创建测试直线
        test_line = {
            'type': 'LINE',
            'points': [(0, 150), (200, 150)]  # 水平线，距离圆弧顶部
        }
        
        print("测试圆弧距离计算优化:")
        print(f"圆弧: 中心={test_arc['center']}, 半径={test_arc['radius']}, "
              f"角度={test_arc['start_angle']}°-{test_arc['end_angle']}°")
        print(f"直线: {test_line['points']}")
        
        # 计算距离
        distance = processor._arc_to_line_distance(test_arc, test_line)
        print(f"计算距离: {distance:.2f}")
        
        # 验证：圆弧最高点应该是(100, 150)，到直线y=150的距离应该是0
        expected_distance = 0.0  # 圆弧顶部应该接触直线
        
        if abs(distance - expected_distance) < 1.0:  # 允许1像素误差
            print("✓ 圆弧距离计算正确")
            return True
        else:
            print(f"✗ 圆弧距离计算错误，期望约{expected_distance}，实际{distance}")
            return False
        
    except Exception as e:
        print(f"✗ 圆弧距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_circle_distance_calculation():
    """测试圆形距离计算优化"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建测试圆形
        test_circle = {
            'type': 'CIRCLE',
            'center': (100, 100),
            'radius': 30
        }
        
        # 创建测试直线
        test_line = {
            'type': 'LINE',
            'points': [(0, 130), (200, 130)]  # 水平线，正好接触圆的顶部
        }
        
        print("测试圆形距离计算优化:")
        print(f"圆形: 中心={test_circle['center']}, 半径={test_circle['radius']}")
        print(f"直线: {test_line['points']}")
        
        # 计算距离
        distance = processor._circle_to_line_distance(test_circle, test_line)
        print(f"计算距离: {distance:.2f}")
        
        # 验证：圆的顶部是(100, 130)，到直线y=130的距离应该是0
        expected_distance = 0.0
        
        if abs(distance - expected_distance) < 1.0:  # 允许1像素误差
            print("✓ 圆形距离计算正确")
            return True
        else:
            print(f"✗ 圆形距离计算错误，期望约{expected_distance}，实际{distance}")
            return False
        
    except Exception as e:
        print(f"✗ 圆形距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ellipse_distance_calculation():
    """测试椭圆距离计算"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建测试椭圆
        test_ellipse = {
            'type': 'ELLIPSE',
            'center': (100, 100),
            'major_axis': 60,
            'minor_axis': 40,
            'rotation': 0
        }
        
        # 创建测试直线
        test_line = {
            'type': 'LINE',
            'points': [(0, 140), (200, 140)]  # 水平线，接触椭圆顶部
        }
        
        print("测试椭圆距离计算:")
        print(f"椭圆: 中心={test_ellipse['center']}, 长轴={test_ellipse['major_axis']}, "
              f"短轴={test_ellipse['minor_axis']}")
        print(f"直线: {test_line['points']}")
        
        # 计算距离
        distance = processor._ellipse_to_line_distance(test_ellipse, test_line)
        print(f"计算距离: {distance:.2f}")
        
        # 椭圆顶部应该是(100, 120)，到直线y=140的距离应该是20
        expected_distance = 20.0
        
        if abs(distance - expected_distance) < 5.0:  # 允许5像素误差
            print("✓ 椭圆距离计算正确")
            return True
        else:
            print(f"✗ 椭圆距离计算错误，期望约{expected_distance}，实际{distance}")
            return False
        
    except Exception as e:
        print(f"✗ 椭圆距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arc_sampling_method():
    """测试圆弧采样方法"""
    try:
        print("测试圆弧采样方法:")
        
        # 检查代码中是否包含了采样逻辑
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码
        checks = [
            ("采样点数量", "sample_count"),
            ("角度范围计算", "angle_range"),
            ("采样循环", "for i in range"),
            ("采样点计算", "sample_point"),
            ("不使用圆心距离", "不再使用圆心到直线的距离判断")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已包含")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 圆弧采样方法正确实现")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧采样方法测试失败: {e}")
        return False

def test_circle_sampling_method():
    """测试圆形采样方法"""
    try:
        print("测试圆形采样方法:")
        
        # 检查代码中是否包含了采样逻辑
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在圆形距离计算方法中查找采样逻辑
        circle_method_start = content.find("def _circle_to_line_distance")
        circle_method_end = content.find("def ", circle_method_start + 1)
        if circle_method_end == -1:
            circle_method_end = len(content)
        
        circle_method_content = content[circle_method_start:circle_method_end]
        
        # 检查关键代码
        checks = [
            ("采样点数量", "sample_count"),
            ("采样循环", "for i in range"),
            ("角度计算", "2 * math.pi"),
            ("采样点计算", "sample_point"),
            ("不使用圆心距离", "不再使用圆心到直线的距离判断")
        ]
        
        for check_name, pattern in checks:
            if pattern in circle_method_content:
                print(f"✓ {check_name}: 已包含")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 圆形采样方法正确实现")
        return True
        
    except Exception as e:
        print(f"✗ 圆形采样方法测试失败: {e}")
        return False

def test_grouping_algorithm_improvement():
    """测试分组算法改进效果"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建测试场景：圆弧和直线应该被分为一组
        test_entities = [
            # 圆弧
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 50,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'test'
            },
            # 接触圆弧端点的直线
            {
                'type': 'LINE',
                'points': [(150, 100), (200, 100)],  # 从圆弧右端点延伸
                'layer': 'test'
            },
            # 接触圆弧另一端点的直线
            {
                'type': 'LINE',
                'points': [(100, 150), (100, 200)],  # 从圆弧顶端点延伸
                'layer': 'test'
            }
        ]
        
        print("测试分组算法改进效果:")
        print(f"测试实体数量: {len(test_entities)}")
        
        # 运行分组算法
        groups = processor.group_other_entities(test_entities)
        
        print(f"分组结果: {len(groups)} 个组")
        
        # 检查是否所有实体都在同一组中（因为它们相互连接）
        if len(groups) == 1 and len(groups[0]) == 3:
            print("✓ 分组算法正确：所有连接的实体被分为一组")
            return True
        else:
            print(f"✗ 分组算法可能有问题：期望1组3个实体，实际{len(groups)}组")
            for i, group in enumerate(groups):
                print(f"  组{i+1}: {len(group)}个实体")
            return False
        
    except Exception as e:
        print(f"✗ 分组算法改进测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试圆弧椭圆分组算法优化...")
    print("=" * 60)
    
    tests = [
        ("圆弧采样方法", test_arc_sampling_method),
        ("圆形采样方法", test_circle_sampling_method),
        ("圆弧距离计算", test_arc_distance_calculation),
        ("圆形距离计算", test_circle_distance_calculation),
        ("椭圆距离计算", test_ellipse_distance_calculation),
        ("分组算法改进", test_grouping_algorithm_improvement)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧椭圆分组算法优化测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
