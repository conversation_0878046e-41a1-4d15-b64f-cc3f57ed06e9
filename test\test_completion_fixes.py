#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试标注完成后的修复效果
验证：
1. IndexError修复
2. 正确清除高亮显示
3. 界面状态正确更新
"""

import os
import sys
import tkinter as tk
from unittest.mock import Mock, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_completion_indexerror_fixes():
    """测试完成时的IndexError修复"""
    print("=" * 60)
    print("测试完成时IndexError修复")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置测试数据
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],  # 已标注
            [{'type': 'CIRCLE', 'label': 'door'}],  # 已标注
        ]
        
        processor.groups_info = [
            {'status': 'labeled'},
            {'status': 'labeled'},
        ]
        
        processor.current_file_entities = [{'type': 'LINE'}, {'type': 'CIRCLE'}]
        processor.auto_labeled_entities = []
        processor.labeled_entities = [{'type': 'LINE', 'label': 'wall'}]
        
        # 模拟可视化器
        processor.visualizer = Mock()
        processor.visualizer.ax_detail = Mock()
        processor.visualizer.chinese_font = Mock()
        processor.canvas = Mock()
        
        print("\n🔍 测试1: has_unlabeled_groups方法（所有组已标注）")
        result = processor.has_unlabeled_groups()
        if not result:
            print("✅ 正确识别所有组都已标注")
        else:
            print(f"❌ 应该返回False，但返回了: {result}")
        
        print("\n🔍 测试2: get_next_unlabeled_group方法（无未标注组）")
        next_group = processor.get_next_unlabeled_group()
        if next_group is None:
            print("✅ 正确返回None（无未标注组）")
        else:
            print(f"❌ 应该返回None，但返回了: {next_group}")
        
        print("\n🔍 测试3: _show_completion_message方法")
        try:
            processor._show_completion_message()
            print("✅ 完成信息显示成功，没有IndexError")
        except IndexError as e:
            print(f"❌ 仍然出现IndexError: {e}")
        except Exception as e:
            print(f"✅ 其他异常（可能正常）: {e}")
        
        # 测试异常情况
        print("\n🔍 测试4: 异常数据处理")
        
        # 设置异常数据
        processor.groups_info = None
        processor.all_groups = None
        
        try:
            result = processor.has_unlabeled_groups()
            print(f"✅ 异常数据处理成功: {result}")
        except Exception as e:
            print(f"❌ 异常数据处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_completion_ui_updates():
    """测试完成时的界面更新"""
    print("\n" + "=" * 60)
    print("测试完成时界面更新")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        from main_enhanced import EnhancedCADProcessor
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.processor = EnhancedCADProcessor()
        
        # 模拟界面组件
        app.group_tree = Mock()
        app.status_var = Mock()
        app.canvas = Mock()
        app.update_group_list = Mock()
        
        # 模拟处理器组件
        app.processor.visualizer = Mock()
        app.processor.visualizer.ax_detail = Mock()
        app.processor.visualizer.chinese_font = Mock()
        app.processor.current_file_entities = [{'type': 'LINE'}]
        app.processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],
        ]
        app.processor.groups_info = [
            {'status': 'labeled'},
        ]
        app.processor.labeled_entities = [{'type': 'LINE', 'label': 'wall'}]
        app.processor.auto_labeled_entities = []
        
        print("\n🔍 测试完成状态更新")
        
        try:
            app._update_final_completion_state()
            print("✅ 完成状态更新成功")
            
            # 验证各个方法是否被调用
            if app.update_group_list.called:
                print("✅ 组列表更新被调用")
            else:
                print("⚠️ 组列表更新未被调用")
                
        except Exception as e:
            print(f"❌ 完成状态更新失败: {e}")
        
        print("\n🔍 测试高亮清除")
        
        try:
            app._clear_all_highlights_safe()
            print("✅ 高亮清除成功")
        except Exception as e:
            print(f"❌ 高亮清除失败: {e}")
        
        print("\n🔍 测试可视化更新")
        
        try:
            app._update_completion_visualization()
            print("✅ 完成可视化更新成功")
            
            # 验证可视化方法是否被调用
            if app.processor.visualizer.ax_detail.clear.called:
                print("✅ 详细视图清除被调用")
            if app.processor.visualizer.visualize_overview.called:
                print("✅ 全图概览更新被调用")
                
        except Exception as e:
            print(f"❌ 完成可视化更新失败: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_safe_list_access():
    """测试安全的列表访问"""
    print("\n" + "=" * 60)
    print("测试安全列表访问")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 测试各种异常情况
        test_cases = [
            ("空列表", [], []),
            ("None值", None, None),
            ("混合数据", [{'status': 'labeled'}, None, {'status': 'unlabeled'}], 
             [[{'type': 'LINE'}], None, [{'type': 'CIRCLE'}]]),
        ]
        
        for case_name, groups_info, all_groups in test_cases:
            print(f"\n🔍 测试{case_name}")
            
            processor.groups_info = groups_info
            processor.all_groups = all_groups
            
            try:
                # 测试has_unlabeled_groups
                result1 = processor.has_unlabeled_groups()
                print(f"  has_unlabeled_groups: {result1}")
                
                # 测试get_next_unlabeled_group
                result2 = processor.get_next_unlabeled_group()
                print(f"  get_next_unlabeled_group: {result2}")
                
                print(f"  ✅ {case_name}处理成功")
                
            except Exception as e:
                print(f"  ❌ {case_name}处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_completion_flow():
    """测试完整的完成流程"""
    print("\n" + "=" * 60)
    print("测试完整完成流程")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 设置完成状态的数据
        processor.all_groups = [
            [{'type': 'LINE', 'label': 'wall'}],
            [{'type': 'CIRCLE', 'label': 'door'}],
        ]
        
        processor.groups_info = [
            {'status': 'labeled'},
            {'status': 'labeled'},
        ]
        
        processor.pending_manual_groups = []
        processor.manual_grouping_mode = True
        processor.current_manual_group_index = 2  # 超出范围，模拟完成状态
        
        # 模拟组件
        processor.visualizer = Mock()
        processor.visualizer.ax_detail = Mock()
        processor.visualizer.chinese_font = Mock()
        processor.canvas = Mock()
        processor.current_file_entities = []
        processor.auto_labeled_entities = []
        processor.labeled_entities = []
        processor.status_callback = Mock()
        
        print("\n🔍 测试完整的_show_next_manual_group流程")
        
        try:
            processor._show_next_manual_group()
            print("✅ 完成流程执行成功")
            
            # 验证状态
            if not processor.manual_grouping_mode:
                print("✅ 手动标注模式已关闭")
            else:
                print("⚠️ 手动标注模式仍然开启")
                
            if processor.status_callback.called:
                print("✅ 状态回调被调用")
                call_args = processor.status_callback.call_args
                print(f"  回调参数: {call_args}")
            else:
                print("⚠️ 状态回调未被调用")
                
        except Exception as e:
            print(f"❌ 完成流程执行失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试标注完成修复效果...")
    
    test1 = test_completion_indexerror_fixes()
    test2 = test_completion_ui_updates()
    test3 = test_safe_list_access()
    test4 = test_completion_flow()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = {
        "完成时IndexError修复": test1,
        "完成时界面更新": test2,
        "安全列表访问": test3,
        "完整完成流程": test4,
    }
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！标注完成修复成功。")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码。")
