 实体组预览区域创建完成（已恢复原有尺寸和显示）
✅ 实体全图概览区域创建完成（已恢复原有尺寸和显示）
✅ 原有配色系统功能已恢复
✅ 配色系统区域创建完成（已恢复原有功能）
✅ 原有缩放按钮功能已恢复
✅ 缩放按钮区域创建完成（已恢复原有功能）      
✅ 四区域可视化布局创建完成，支持按比例自动适应
✅ 可视化器已创建，使用原有的单一画布模式      
✅ 可视化器兼容性设置完成（原有模式）
✅ 日志导出器初始化成功
🔍 开始扫描文件夹: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file
📋 文件夹中有 3 个项目
  检查: kitch01.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: kitch01.dxf
  检查: kongqiang.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: kongqiang.dxf
  检查: wall01.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: wall01.dxf
🎯 扫描结果: 找到 3 个CAD文件
✅ 成功找到以下CAD文件:
    kitch01.dxf
    kongqiang.dxf
    wall01.dxf
📁 使用第一个文件: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (未处理, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
🖥️ 更新界面显示文件: kitch01.dxf
📁 使用display_file: kitch01.dxf
📁 设置当前选择: [kitch01.dxf] (处理中, 未标注)
📁 多文件模式: 3 个文件 - 下拉框已启用，显示文件: kitch01.dxf
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
🔄 开始处理文件: kitch01.dxf
  处理文件: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\kitch01.dxf
  显示文件: C:/A-BCXM/CAD分类标注工具C01/txt-dxf-3file\kitch01.dxf
  是否为显示文件: True
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
🔄 开始后台处理 2 个文件...
  处理文件 1/2: kongqiang.dxf
    📋 后台文件状态更新: kongqiang.dxf -> 处理中
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
    开始处理...
    🔄 开始后台CAD处理: kongqiang.dxf
🔍 墙体图层: 1 个图层，39 个实体
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: set()
  栏杆图层: set()
开始合并包含的墙体组，共 5 个墙体组
发现包含关系: 组0 包含 组1
发现包含关系: 组2 包含 组3
墙体组合并完成: 5 -> 3
跳过后台处理的自动标注可视化更新
去重: 62 → 56 个实体
同图层分组阈值: 40
内部优化后组数: 1
包围合并后组数: 1
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/1, 实体数量: 56
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 1
    ✅ 后台CAD文件处理成功
    📊 后台处理结果: 101个实体, 4个组
    ✅ 处理完成，数据已缓存
    📋 后台文件状态更新: kongqiang.dxf -> 已完成
  处理文件 2/2: wall01.dxf
    📋 后台文件状态更新: wall01.dxf -> 处理中
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
    开始处理...
    🔄 开始后台CAD处理: wall01.dxf
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: 3.640 - 4.266
  校正参数: 2.017 - 2.644
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: -1.088 - -0.535
  校正参数: 0.535 - 1.088

============================================================
🔍 专业DXF读取器 - 坐标系手性校正摘要
============================================================
总实体数: 533
圆弧实体: 372 (校正: 0)
椭圆实体: 2 (校正: 2)
右手坐标系 (RHS): 372
左手坐标系 (LHS): 2

📐 校正详情:
  ELLIPSE (左手坐标系):
    原始参数: (3.639540612752146, 4.266436669646277)
    校正参数: (2.0167486375333095, 2.64364469442744)
  ELLIPSE (左手坐标系):
    原始参数: (-1.0883951813113, -0.5350782450734349)
    校正参数: (0.5350782450734348, 1.0883951813113004)
============================================================
特殊图层识别结果:
  墙体图层: set()
  门窗图层: set()
  栏杆图层: set()
🔍 墙体图层: 1 个图层，38 个实体
🔍 门窗图层: 1 个图层，6 个实体
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: {'A-WINDOW'}
  栏杆图层: set()
开始合并包含的墙体组，共 3 个墙体组
发现包含关系: 组1 包含 组2
墙体组合并完成: 3 -> 2
跳过后台处理的自动标注可视化更新
同图层分组阈值: 40
内部优化后组数: 1
包围合并后组数: 1
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/1, 实体数量: 12
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 1
    ✅ 后台CAD文件处理成功
    📊 后台处理结果: 56个实体, 4个组
    ✅ 处理完成，数据已缓存
    📋 后台文件状态更新: wall01.dxf -> 已完成
📊 处理进度: 2/3，剩余1个文件
去重: 533 → 517 个实体
同图层分组阈值: 40
内部优化后组数: 130
包围合并后组数: 4
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/4, 实体数量: 441
🔄 处理状态更新: manual_group
📍 自动滚动到第一个未标注组: 组1
📋 组列表更新完成：kitch01.dxf
✅ 组列表更新完成: status_update
📋 组列表更新完成：kitch01.dxf
✅ 组列表更新完成: status_update
更新详细视图...
更新全图概览...
可视化更新成功
✅ 手动标注模式已启动，待处理组数: 4
🔍 检测到后台处理: 处理器文件(kongqiang.dxf) != 显示文件(kitch01.dxf)
🔍 墙体图层: 1 个图层，39 个实体
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: set()
  栏杆图层: set()
开始合并包含的墙体组，共 5 个墙体组
发现包含关系: 组0 包含 组1
发现包含关系: 组2 包含 组3
墙体组合并完成: 5 -> 3
跳过后台处理的自动标注可视化更新
去重: 62 → 56 个实体
同图层分组阈值: 40
内部优化后组数: 1
包围合并后组数: 1
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/1, 实体数量: 56
🔄 处理状态更新: manual_group
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 1
🔍 检测到后台处理: 处理器文件(wall01.dxf) != 显示文件(kitch01.dxf)
🔍 墙体图层: 1 个图层，38 个实体
🔍 门窗图层: 1 个图层，6 个实体
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: {'A-WINDOW'}
  栏杆图层: set()
开始合并包含的墙体组，共 3 个墙体组
发现包含关系: 组1 包含 组2
墙体组合并完成: 3 -> 2
跳过后台处理的自动标注可视化更新
同图层分组阈值: 40
内部优化后组数: 1
包围合并后组数: 1
调用强制合并SPLINE实体，当前组数: 4
强制合并完成，最终组数: 4
显示手动分组组: 1/1, 实体数量: 12
🔄 处理状态更新: manual_group
跳过后台处理的可视化更新
✅ 手动标注模式已启动，待处理组数: 1
🔄 处理状态更新: completed
🔄 处理状态更新: completed
后台文件处理完成，不更新界面
📝 跳过后台文件界面更新: kitch01.dxf