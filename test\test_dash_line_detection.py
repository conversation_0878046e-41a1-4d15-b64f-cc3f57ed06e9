#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试虚线识别功能
"""

import sys
import os

def test_dash_line_detection():
    """测试虚线识别功能"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建模拟的虚线实体
        mock_dash_entity = {
            'type': 'LINE',
            'layer': 'test_layer',
            'linetype': 'DASHED',
            'is_dashed': True,
            'color': 1,
            'points': [(0, 0), (100, 0)]
        }
        
        # 创建模拟的实线实体
        mock_solid_entity = {
            'type': 'LINE',
            'layer': 'test_layer',
            'linetype': 'CONTINUOUS',
            'is_dashed': False,
            'color': 1,
            'points': [(0, 10), (100, 10)]
        }
        
        print("✓ 虚线实体创建成功:")
        print(f"  类型: {mock_dash_entity['type']}")
        print(f"  线型: {mock_dash_entity['linetype']}")
        print(f"  是否虚线: {mock_dash_entity['is_dashed']}")
        
        print("✓ 实线实体创建成功:")
        print(f"  类型: {mock_solid_entity['type']}")
        print(f"  线型: {mock_solid_entity['linetype']}")
        print(f"  是否虚线: {mock_solid_entity['is_dashed']}")
        
        # 测试实体分组是否能正确处理虚线
        entities = [mock_dash_entity, mock_solid_entity]
        
        # 测试特征提取
        features_dash = processor.extract_features([mock_dash_entity])
        features_solid = processor.extract_features([mock_solid_entity])
        
        print("✓ 特征提取测试完成")
        print(f"  虚线组特征: line_count={features_dash.get('line_count', 0)}")
        print(f"  实线组特征: line_count={features_solid.get('line_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 虚线识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_linetype_detection():
    """测试线型检测逻辑"""
    try:
        # 测试不同线型的识别
        test_cases = [
            ('CONTINUOUS', False),
            ('DASHED', True),
            ('DASH', True),
            ('DASHDOT', True),
            ('DASHDOTDOT', True),
            ('DOT', True),
            ('DOTTED', True),
            ('CUSTOM_LINE', False),
            ('', False),
            (None, False)
        ]
        
        print("测试线型识别逻辑:")
        for linetype, expected_is_dashed in test_cases:
            # 模拟线型检测逻辑
            if linetype and linetype.upper() in ['DASHED', 'DASH', 'DASHDOT', 'DASHDOTDOT', 'DOT', 'DOTTED']:
                is_dashed = True
            else:
                is_dashed = False
            
            if is_dashed == expected_is_dashed:
                print(f"  ✓ {linetype or 'None'}: {is_dashed}")
            else:
                print(f"  ✗ {linetype or 'None'}: 期望{expected_is_dashed}, 实际{is_dashed}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 线型检测测试失败: {e}")
        return False

def test_entity_data_extraction():
    """测试实体数据提取中的线型信息"""
    try:
        print("测试实体数据提取中的线型信息:")
        
        # 检查代码中是否包含了线型提取逻辑
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'linetype' in content and 'is_dashed' in content:
            print("✓ 代码中包含线型提取逻辑")
        else:
            print("✗ 代码中缺少线型提取逻辑")
            return False
        
        if 'DASHED' in content and 'DASH' in content:
            print("✓ 代码中包含虚线类型检测")
        else:
            print("✗ 代码中缺少虚线类型检测")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 实体数据提取测试失败: {e}")
        return False

def test_wall_grouping_with_dash_lines():
    """测试墙体分组是否包含虚线"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建包含虚线和实线的墙体组
        wall_entities = [
            {
                'type': 'LINE',
                'layer': 'wall',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(0, 0), (100, 0)]
            },
            {
                'type': 'LINE',
                'layer': 'wall',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 10), (100, 10)]
            },
            {
                'type': 'LINE',
                'layer': 'wall',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(0, 0), (0, 10)]
            },
            {
                'type': 'LINE',
                'layer': 'wall',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(100, 0), (100, 10)]
            }
        ]
        
        print("测试墙体分组包含虚线:")
        print(f"  总实体数: {len(wall_entities)}")
        print(f"  虚线数: {sum(1 for e in wall_entities if e.get('is_dashed', False))}")
        print(f"  实线数: {sum(1 for e in wall_entities if not e.get('is_dashed', False))}")
        
        # 测试分组
        groups = processor.group_other_entities(wall_entities)
        print(f"  分组结果: {len(groups)} 个组")
        
        # 检查是否所有实体都被包含
        total_entities_in_groups = sum(len(group) for group in groups)
        print(f"  组中实体总数: {total_entities_in_groups}")
        
        if total_entities_in_groups >= len(wall_entities):
            print("✓ 所有实体（包括虚线）都被正确分组")
            return True
        else:
            print("✗ 部分实体未被分组，可能包括虚线")
            return False
        
    except Exception as e:
        print(f"✗ 墙体分组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试虚线识别功能...")
    print("=" * 50)
    
    tests = [
        ("线型检测逻辑", test_linetype_detection),
        ("实体数据提取", test_entity_data_extraction),
        ("虚线识别", test_dash_line_detection),
        ("墙体分组包含虚线", test_wall_grouping_with_dash_lines)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 虚线识别功能测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，虚线识别可能存在问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
