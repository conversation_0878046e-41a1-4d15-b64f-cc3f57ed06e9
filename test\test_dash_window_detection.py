#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试A-window图层dash线识别问题
"""

import sys
import os

def test_door_window_layer_patterns():
    """测试门窗图层模式是否包含A-window"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 检查门窗图层模式
        patterns = processor.door_window_layer_patterns
        
        print("门窗图层识别模式:")
        for i, pattern in enumerate(patterns):
            print(f"  {i+1:2d}. {pattern}")
        
        # 检查是否包含A-WINDOW
        if 'A-WINDOW' in patterns:
            print("\n✓ A-WINDOW 模式已包含在门窗识别模式中")
        else:
            print("\n✗ A-WINDOW 模式未包含在门窗识别模式中")
            return False
        
        # 测试图层检测
        test_entities = [
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 0), (100, 0)]
            },
            {
                'layer': 'A-window',  # 小写测试
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 10), (100, 10)]
            },
            {
                'layer': 'OTHER-LAYER',
                'type': 'LINE',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(0, 20), (100, 20)]
            }
        ]
        
        # 测试图层检测
        detected_layers = processor._detect_special_layers(
            test_entities, 
            processor.door_window_layer_patterns, 
            debug=True, 
            layer_type="门窗"
        )
        
        print(f"\n检测到的门窗图层: {detected_layers}")
        
        if 'A-WINDOW' in detected_layers or 'A-window' in detected_layers:
            print("✓ A-window图层检测正常")
            return True
        else:
            print("✗ A-window图层检测失败")
            return False
        
    except Exception as e:
        print(f"✗ 门窗图层模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dash_line_in_door_window_processing():
    """测试虚线在门窗处理中的表现"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建包含A-WINDOW图层虚线的测试数据
        test_entities = [
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 0), (100, 0)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(100, 0), (100, 50)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(100, 50), (0, 50)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(0, 50), (0, 0)]
            }
        ]
        
        print("测试虚线在门窗处理中的表现:")
        print(f"测试实体数量: {len(test_entities)}")
        print("实体详情:")
        for i, entity in enumerate(test_entities):
            print(f"  {i+1}. 图层: {entity['layer']}, 线型: {entity['linetype']}, 虚线: {entity['is_dashed']}")
        
        # 测试门窗图层检测
        door_window_layers = processor._detect_special_layers(
            test_entities, 
            processor.door_window_layer_patterns, 
            debug=True, 
            layer_type="门窗"
        )
        
        # 筛选门窗实体
        door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
        print(f"\n筛选出的门窗实体数量: {len(door_window_entities)}")
        
        # 检查虚线是否被包含
        dash_entities = [e for e in door_window_entities if e.get('is_dashed', False)]
        print(f"其中虚线实体数量: {len(dash_entities)}")
        
        if len(dash_entities) == 2:  # 应该有2个虚线实体
            print("✓ 虚线实体正确包含在门窗处理中")
            return True
        else:
            print("✗ 虚线实体未正确包含在门窗处理中")
            return False
        
    except Exception as e:
        print(f"✗ 虚线门窗处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_special_entity_grouping():
    """测试特殊实体分组是否正确处理虚线"""
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建混合测试数据
        test_entities = [
            # A-WINDOW图层的虚线
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 0), (100, 0)]
            },
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(100, 0), (100, 50)]
            },
            # 其他图层的实线
            {
                'layer': 'OTHER-LAYER',
                'type': 'LINE',
                'linetype': 'CONTINUOUS',
                'is_dashed': False,
                'points': [(200, 0), (300, 0)]
            }
        ]
        
        print("测试特殊实体分组:")
        
        # 运行实体分组
        groups = processor.group_entities(test_entities, debug=True)
        
        print(f"\n分组结果: {len(groups)} 个组")
        
        # 检查A-WINDOW图层的虚线是否被正确分组
        window_group_found = False
        for i, group in enumerate(groups):
            print(f"组 {i+1}: {len(group)} 个实体")
            for j, entity in enumerate(group):
                print(f"  实体 {j+1}: 图层={entity['layer']}, 线型={entity.get('linetype', 'N/A')}, 虚线={entity.get('is_dashed', False)}")
                if entity['layer'] == 'A-WINDOW' and entity.get('is_dashed', False):
                    window_group_found = True
        
        if window_group_found:
            print("✓ A-WINDOW图层的虚线被正确分组")
            return True
        else:
            print("✗ A-WINDOW图层的虚线未被正确分组")
            return False
        
    except Exception as e:
        print(f"✗ 特殊实体分组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_labeling_logic():
    """测试自动标注逻辑是否正确处理虚线"""
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor()
        
        # 创建测试数据
        test_entities = [
            {
                'layer': 'A-WINDOW',
                'type': 'LINE',
                'linetype': 'DASHED',
                'is_dashed': True,
                'points': [(0, 0), (100, 0)]
            }
        ]
        
        print("测试自动标注逻辑:")
        
        # 模拟自动处理
        processor.current_file_entities = test_entities
        
        # 检查门窗图层识别
        door_window_layers = processor.processor._detect_special_layers(
            test_entities, 
            processor.processor.door_window_layer_patterns, 
            debug=True, 
            layer_type="门窗"
        )
        
        print(f"识别的门窗图层: {door_window_layers}")
        
        if 'A-WINDOW' in door_window_layers:
            print("✓ 自动标注逻辑正确识别A-WINDOW图层")
            return True
        else:
            print("✗ 自动标注逻辑未正确识别A-WINDOW图层")
            return False
        
    except Exception as e:
        print(f"✗ 自动标注逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试A-window图层dash线识别问题...")
    print("=" * 60)
    
    tests = [
        ("门窗图层模式检查", test_door_window_layer_patterns),
        ("虚线门窗处理", test_dash_line_in_door_window_processing),
        ("特殊实体分组", test_special_entity_grouping),
        ("自动标注逻辑", test_auto_labeling_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 A-window图层dash线识别测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
