#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试圆弧椭圆分组检测中心点移除
"""

import sys
import os
import math

def test_arc_center_point_removal():
    """测试圆弧中心点检测移除"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试圆弧中心点检测移除:")
        
        processor = CADDataProcessor()
        
        # 创建测试圆弧
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,
            'layer': 'test'
        }
        
        # 测试获取实体中心点（应该返回None）
        center = processor._get_entity_center(test_arc)
        if center is None:
            print("✓ 圆弧中心点检测已移除")
        else:
            print(f"✗ 圆弧仍返回中心点: {center}")
            return False
        
        # 测试快速距离检查（应该使用端点而不是中心点）
        test_line = {
            'type': 'LINE',
            'points': [(200, 100), (300, 100)],
            'layer': 'test'
        }
        
        quick_distance = processor._quick_distance_check(test_arc, test_line)
        print(f"✓ 圆弧快速距离检查: {quick_distance:.2f} (使用端点方法)")
        
        return True
        
    except Exception as e:
        print(f"✗ 圆弧中心点检测移除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_circle_center_point_removal():
    """测试圆形中心点检测移除"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试圆形中心点检测移除:")
        
        processor = CADDataProcessor()
        
        # 创建测试圆形
        test_circle = {
            'type': 'CIRCLE',
            'center': (100, 100),
            'radius': 30,
            'layer': 'test'
        }
        
        # 测试获取实体中心点（应该返回None）
        center = processor._get_entity_center(test_circle)
        if center is None:
            print("✓ 圆形中心点检测已移除")
        else:
            print(f"✗ 圆形仍返回中心点: {center}")
            return False
        
        # 测试快速距离检查
        test_line = {
            'type': 'LINE',
            'points': [(200, 100), (300, 100)],
            'layer': 'test'
        }
        
        quick_distance = processor._quick_distance_check(test_circle, test_line)
        print(f"✓ 圆形快速距离检查: {quick_distance:.2f} (使用端点方法)")
        
        return True
        
    except Exception as e:
        print(f"✗ 圆形中心点检测移除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ellipse_center_point_removal():
    """测试椭圆中心点检测移除"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试椭圆中心点检测移除:")
        
        processor = CADDataProcessor()
        
        # 创建测试椭圆
        test_ellipse = {
            'type': 'ELLIPSE',
            'center': (100, 100),
            'major_axis': (50, 0),
            'ratio': 0.5,
            'layer': 'test'
        }
        
        # 测试获取实体中心点（应该返回None）
        center = processor._get_entity_center(test_ellipse)
        if center is None:
            print("✓ 椭圆中心点检测已移除")
        else:
            print(f"✗ 椭圆仍返回中心点: {center}")
            return False
        
        # 测试快速距离检查
        test_line = {
            'type': 'LINE',
            'points': [(200, 100), (300, 100)],
            'layer': 'test'
        }
        
        quick_distance = processor._quick_distance_check(test_ellipse, test_line)
        print(f"✓ 椭圆快速距离检查: {quick_distance:.2f} (使用端点方法)")
        
        return True
        
    except Exception as e:
        print(f"✗ 椭圆中心点检测移除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arc_sampling_distance():
    """测试圆弧采样点距离计算"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试圆弧采样点距离计算:")
        
        processor = CADDataProcessor()
        
        # 创建测试圆弧（90度圆弧）
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,
            'layer': 'test'
        }
        
        # 创建接触圆弧顶部的直线
        test_line = {
            'type': 'LINE',
            'points': [(50, 150), (150, 150)],  # y=150的水平线，应该接触圆弧顶部
            'layer': 'test'
        }
        
        # 计算距离（应该使用采样点方法）
        distance = processor._arc_to_line_distance(test_arc, test_line)
        print(f"圆弧到直线距离: {distance:.2f}")
        
        # 验证：圆弧顶部点是(100, 150)，应该接触直线
        if distance < 1.0:  # 允许小误差
            print("✓ 圆弧采样点距离计算正确")
            return True
        else:
            print(f"✗ 圆弧采样点距离计算可能有误，期望接近0，实际{distance}")
            return False
        
    except Exception as e:
        print(f"✗ 圆弧采样点距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_circle_sampling_distance():
    """测试圆形采样点距离计算"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试圆形采样点距离计算:")
        
        processor = CADDataProcessor()
        
        # 创建测试圆形
        test_circle = {
            'type': 'CIRCLE',
            'center': (100, 100),
            'radius': 30,
            'layer': 'test'
        }
        
        # 创建接触圆形顶部的直线
        test_line = {
            'type': 'LINE',
            'points': [(50, 130), (150, 130)],  # y=130的水平线，应该接触圆形顶部
            'layer': 'test'
        }
        
        # 计算距离（应该使用采样点方法）
        distance = processor._circle_to_line_distance(test_circle, test_line)
        print(f"圆形到直线距离: {distance:.2f}")
        
        # 验证：圆形顶部点是(100, 130)，应该接触直线
        if distance < 1.0:  # 允许小误差
            print("✓ 圆形采样点距离计算正确")
            return True
        else:
            print(f"✗ 圆形采样点距离计算可能有误，期望接近0，实际{distance}")
            return False
        
    except Exception as e:
        print(f"✗ 圆形采样点距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ellipse_sampling_distance():
    """测试椭圆采样点距离计算"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试椭圆采样点距离计算:")
        
        processor = CADDataProcessor()
        
        # 创建测试椭圆
        test_ellipse = {
            'type': 'ELLIPSE',
            'center': (100, 100),
            'major_axis': (40, 0),  # 水平长轴，半径40
            'ratio': 0.5,  # 短轴半径20
            'layer': 'test'
        }
        
        # 创建接触椭圆顶部的直线
        test_line = {
            'type': 'LINE',
            'points': [(50, 120), (150, 120)],  # y=120的水平线，应该接触椭圆顶部
            'layer': 'test'
        }
        
        # 计算距离（应该使用采样点方法）
        distance = processor._ellipse_to_line_distance(test_ellipse, test_line)
        print(f"椭圆到直线距离: {distance:.2f}")
        
        # 验证：椭圆顶部点是(100, 120)，应该接触直线
        if distance < 1.0:  # 允许小误差
            print("✓ 椭圆采样点距离计算正确")
            return True
        else:
            print(f"✗ 椭圆采样点距离计算可能有误，期望接近0，实际{distance}")
            return False
        
    except Exception as e:
        print(f"✗ 椭圆采样点距离计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_grouping_without_center():
    """测试不使用中心点的分组效果"""
    try:
        from cad_data_processor import CADDataProcessor
        
        print("测试不使用中心点的分组效果:")
        
        processor = CADDataProcessor()
        
        # 创建测试场景：圆弧和远离的直线
        test_entities = [
            # 圆弧
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 50,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'test'
            },
            # 接触圆弧端点的直线
            {
                'type': 'LINE',
                'points': [(150, 100), (200, 100)],  # 从圆弧右端点延伸
                'layer': 'test'
            },
            # 远离圆弧的直线（如果使用中心点会被错误分组）
            {
                'type': 'LINE',
                'points': [(500, 100), (600, 100)],  # 距离圆心400单位
                'layer': 'test'
            }
        ]
        
        # 执行分组
        groups = processor.group_entities(test_entities, distance_threshold=20, debug=False)
        
        print(f"分组结果: {len(groups)} 个组")
        for i, group in enumerate(groups):
            print(f"  组 {i+1}: {len(group)} 个实体")
            for entity in group:
                if entity['type'] == 'ARC':
                    print(f"    圆弧: 中心{entity['center']}")
                elif entity['type'] == 'LINE':
                    print(f"    直线: {entity['points']}")
        
        # 验证分组结果
        if len(groups) >= 2:
            # 应该至少有2个组：圆弧+近直线一组，远直线单独一组
            print("✓ 分组正确，远离直线未被错误分组")
            return True
        else:
            print("✗ 分组可能仍在使用中心点，远离直线被错误分组")
            return False
        
    except Exception as e:
        print(f"✗ 分组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试圆弧椭圆分组检测中心点移除...")
    print("=" * 60)
    
    tests = [
        ("圆弧中心点检测移除", test_arc_center_point_removal),
        ("圆形中心点检测移除", test_circle_center_point_removal),
        ("椭圆中心点检测移除", test_ellipse_center_point_removal),
        ("圆弧采样点距离计算", test_arc_sampling_distance),
        ("圆形采样点距离计算", test_circle_sampling_distance),
        ("椭圆采样点距离计算", test_ellipse_sampling_distance),
        ("不使用中心点的分组效果", test_grouping_without_center)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧椭圆分组检测中心点移除测试全部通过！")
        print("\n📋 修改总结:")
        print("✅ 圆弧、圆形、椭圆不再返回中心点")
        print("✅ 快速距离检查使用端点而非中心点")
        print("✅ 所有距离计算使用采样点方法")
        print("✅ 分组检测不再依赖中心点距离")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
