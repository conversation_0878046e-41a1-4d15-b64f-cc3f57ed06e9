#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重复操作和滚动位置修复效果
验证：
1. 重复调用控制
2. 滚动位置正确（第四行）
3. 减少冗余日志输出
"""

import os
import sys
import tkinter as tk
from unittest.mock import Mock, MagicMock, patch
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_repetition_control():
    """测试重复调用控制"""
    print("=" * 60)
    print("测试重复调用控制")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        
        print("\n🔍 测试1: 状态更新重复调用控制")
        
        # 模拟快速连续的状态更新
        start_time = time.time()
        
        # 第一次调用
        app._handle_status_update_clean("manual_group", {"index": 1, "total": 5, "entity_count": 10})
        first_call_time = time.time()
        
        # 立即第二次调用（应该被忽略）
        app._handle_status_update_clean("manual_group", {"index": 1, "total": 5, "entity_count": 10})
        second_call_time = time.time()
        
        # 检查时间间隔
        time_diff = second_call_time - first_call_time
        if time_diff < 0.1:
            print(f"✅ 重复调用控制生效，时间间隔: {time_diff:.3f}s")
        else:
            print(f"❌ 重复调用控制可能失效，时间间隔: {time_diff:.3f}s")
        
        print("\n🔍 测试2: 组列表更新调度")
        
        # 模拟组列表更新调度
        app._schedule_group_list_update("test_reason")
        
        # 立即再次调用（应该被合并）
        app._schedule_group_list_update("test_reason2")
        
        if hasattr(app, '_pending_group_list_update'):
            print(f"✅ 组列表更新调度机制工作正常")
        else:
            print("❌ 组列表更新调度机制可能有问题")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scroll_position():
    """测试滚动位置（第四行）"""
    print("\n" + "=" * 60)
    print("测试滚动位置")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 模拟TreeView
        app.group_tree = Mock()
        
        # 创建足够多的组项目（20个组）
        mock_items = [f'item{i}' for i in range(20)]
        app.group_tree.get_children.return_value = mock_items
        
        # 模拟item方法：组10是第一个未标注组
        def mock_item(item_id, key):
            if key == 'text':
                item_index = mock_items.index(item_id)
                return f"组{item_index + 1}"
            elif key == 'values':
                item_index = mock_items.index(item_id)
                if item_index < 9:  # 组1-9：已标注
                    return ['已标注', '墙体', '5']
                elif item_index == 9:  # 组10：未标注（第一个未标注）
                    return ['未标注', '待标注', '4']
                else:  # 组11-20：其他状态
                    return ['未标注', '待标注', '3']
            return []
        
        app.group_tree.item.side_effect = mock_item
        
        print("\n🔍 测试滚动到第四行位置")
        app._scroll_to_first_unlabeled_group()
        
        # 验证滚动调用
        if app.group_tree.see.called:
            called_item = app.group_tree.see.call_args[0][0]
            called_index = mock_items.index(called_item)
            
            # 第一个未标注组是组10（索引9），应该滚动到索引6（组7），让组10显示在第四行
            expected_scroll_index = max(0, 9 - 3)  # 9 - 3 = 6
            
            if called_index == expected_scroll_index:
                print(f"✅ 正确滚动到索引{called_index}（组{called_index+1}），让组10显示在第四行")
            else:
                print(f"❌ 滚动位置不正确，期望索引{expected_scroll_index}，实际索引{called_index}")
        else:
            print("❌ 没有调用滚动方法")
        
        # 验证选中了正确的组
        if app.group_tree.selection_set.called:
            selected_item = app.group_tree.selection_set.call_args[0][0]
            selected_index = mock_items.index(selected_item)
            if selected_index == 9:  # 组10
                print("✅ 正确选中第一个未标注组（组10）")
            else:
                print(f"❌ 选中的组不正确，期望组10，实际组{selected_index+1}")
        
        print("\n🔍 测试行数不够的情况")
        
        # 重置mock
        app.group_tree.reset_mock()
        
        # 模拟只有5个组，组4是第一个未标注组
        short_mock_items = [f'item{i}' for i in range(5)]
        app.group_tree.get_children.return_value = short_mock_items
        
        def mock_item_short(item_id, key):
            if key == 'text':
                item_index = short_mock_items.index(item_id)
                return f"组{item_index + 1}"
            elif key == 'values':
                item_index = short_mock_items.index(item_id)
                if item_index < 3:  # 组1-3：已标注
                    return ['已标注', '墙体', '5']
                else:  # 组4-5：未标注
                    return ['未标注', '待标注', '4']
            return []
        
        app.group_tree.item.side_effect = mock_item_short
        
        app._scroll_to_first_unlabeled_group()
        
        # 验证在行数不够时的滚动行为
        if app.group_tree.see.called:
            called_item = app.group_tree.see.call_args[0][0]
            called_index = short_mock_items.index(called_item)
            print(f"✅ 行数不够时滚动到索引{called_index}（组{called_index+1}）")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reduced_logging():
    """测试减少冗余日志"""
    print("\n" + "=" * 60)
    print("测试减少冗余日志")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.status_var = Mock()
        
        print("\n🔍 测试重复状态更新的日志控制")
        
        # 捕获print输出
        import io
        from contextlib import redirect_stdout
        
        captured_output = io.StringIO()
        
        with redirect_stdout(captured_output):
            # 连续多次相同的状态更新
            for i in range(3):
                app._handle_status_update_clean("completed", "处理完成")
                time.sleep(0.1)  # 短暂延迟
        
        output = captured_output.getvalue()
        completed_count = output.count("🔄 处理状态更新: completed")
        
        if completed_count <= 1:
            print(f"✅ 重复日志控制生效，只输出了{completed_count}次")
        else:
            print(f"❌ 重复日志控制可能失效，输出了{completed_count}次")
        
        root.destroy()
        return completed_count <= 1
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scroll_frequency_control():
    """测试滚动频率控制"""
    print("\n" + "=" * 60)
    print("测试滚动频率控制")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        app.group_tree = Mock()
        app.group_tree.get_children.return_value = ['item1', 'item2', 'item3']
        
        def mock_item(item_id, key):
            if key == 'text':
                return "组1"
            elif key == 'values':
                return ['未标注', '待标注', '4']
            return []
        
        app.group_tree.item.side_effect = mock_item
        
        print("\n🔍 测试快速连续滚动调用")
        
        # 快速连续调用滚动
        start_time = time.time()
        
        app._scroll_to_first_unlabeled_group()
        first_call_count = app.group_tree.see.call_count
        
        # 立即再次调用
        app._scroll_to_first_unlabeled_group()
        second_call_count = app.group_tree.see.call_count
        
        # 短暂延迟后再次调用
        time.sleep(0.1)
        app._scroll_to_first_unlabeled_group()
        third_call_count = app.group_tree.see.call_count
        
        if second_call_count == first_call_count:
            print("✅ 快速重复调用被正确忽略")
        else:
            print("❌ 快速重复调用没有被忽略")
        
        if third_call_count > second_call_count:
            print("✅ 延迟后的调用被正确执行")
        else:
            print("⚠️ 延迟后的调用可能被错误忽略")
        
        root.destroy()
        return second_call_count == first_call_count
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_scenario():
    """测试集成场景：模拟完整的标注流程"""
    print("\n" + "=" * 60)
    print("测试集成场景")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADProcessorWithV2Fill
        
        # 创建模拟的应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADProcessorWithV2Fill(root)
        app.status_var = Mock()
        app.group_tree = Mock()
        app.group_tree.get_children.return_value = ['item1', 'item2', 'item3']
        
        def mock_item(item_id, key):
            if key == 'text':
                return "组1"
            elif key == 'values':
                return ['未标注', '待标注', '4']
            return []
        
        app.group_tree.item.side_effect = mock_item
        
        print("\n🔍 模拟标注流程中的状态更新")
        
        # 捕获输出
        import io
        from contextlib import redirect_stdout
        
        captured_output = io.StringIO()
        
        with redirect_stdout(captured_output):
            # 模拟标注流程
            app._handle_status_update_clean("manual_group", {"index": 1, "total": 5, "entity_count": 10})
            time.sleep(0.05)
            app._handle_status_update_clean("group_labeled", ("group1", "wall", 10))
            time.sleep(0.05)
            app._handle_status_update_clean("completed", "处理完成")
            time.sleep(0.05)
            # 重复调用（应该被忽略）
            app._handle_status_update_clean("completed", "处理完成")
            app._handle_status_update_clean("manual_group", {"index": 1, "total": 5, "entity_count": 10})
        
        output = captured_output.getvalue()
        
        # 统计各种日志的出现次数
        manual_group_count = output.count("🔄 处理状态更新: manual_group")
        completed_count = output.count("🔄 处理状态更新: completed")
        
        print(f"manual_group状态更新次数: {manual_group_count}")
        print(f"completed状态更新次数: {completed_count}")
        
        # 验证重复调用控制
        success = (manual_group_count <= 1 and completed_count <= 1)
        
        if success:
            print("✅ 集成场景测试成功：重复调用得到有效控制")
        else:
            print("❌ 集成场景测试失败：仍有重复调用")
        
        root.destroy()
        return success
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试重复操作和滚动位置修复效果...")
    
    test1 = test_repetition_control()
    test2 = test_scroll_position()
    test3 = test_reduced_logging()
    test4 = test_scroll_frequency_control()
    test5 = test_integration_scenario()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = {
        "重复调用控制": test1,
        "滚动位置正确": test2,
        "减少冗余日志": test3,
        "滚动频率控制": test4,
        "集成场景测试": test5,
    }
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！重复操作和滚动位置修复成功。")
        print("\n📋 修复效果：")
        print("- 重复状态更新调用得到控制")
        print("- 第一个未标注组显示在第四行")
        print("- 减少了冗余的日志输出")
        print("- 滚动频率得到合理控制")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码。")
