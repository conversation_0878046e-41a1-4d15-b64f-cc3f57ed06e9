#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件管理功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_file_management():
    """测试文件管理功能"""
    print("=== 文件管理功能测试 ===")
    
    try:
        # 导入增强版应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 测试文件管理初始化
        print("✅ 应用初始化成功")
        
        # 测试文件状态管理
        test_file_status = {
            'test1.dxf': {
                'processing_status': 'completed',
                'annotation_status': 'incomplete'
            },
            'test2.dxf': {
                'processing_status': 'processing',
                'annotation_status': 'unannotated'
            },
            'test3.dxf': {
                'processing_status': 'unprocessed',
                'annotation_status': 'unannotated'
            }
        }
        
        app.file_status = test_file_status
        app.all_files = ['test1.dxf', 'test2.dxf', 'test3.dxf']
        app.current_file = 'test1.dxf'
        
        # 测试文件下拉菜单更新
        if hasattr(app, 'update_file_combo'):
            app.update_file_combo()
            print("✅ 文件下拉菜单更新成功")
        
        # 测试统计信息更新
        if hasattr(app, 'update_stats'):
            app.update_stats()
            print("✅ 统计信息更新成功")
        
        print("✅ 所有文件管理功能测试通过")
        
        # 显示界面进行手动测试
        print("\n启动界面进行手动测试...")
        print("请测试以下功能：")
        print("1. 文件选择下拉菜单")
        print("2. 保存和读取按钮")
        print("3. 文件状态显示")
        print("4. 后台处理功能")
        
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_status_display():
    """测试文件状态显示格式"""
    print("\n=== 文件状态显示测试 ===")
    
    # 测试状态文本生成
    test_cases = [
        {
            'file': 'test1.dxf',
            'processing_status': 'completed',
            'annotation_status': 'completed',
            'is_current': True,
            'expected': '[test1.dxf] (已完成, 标注完成)'
        },
        {
            'file': 'test2.dxf',
            'processing_status': 'processing',
            'annotation_status': 'unannotated',
            'is_current': False,
            'expected': 'test2.dxf (处理中, 未标注)'
        },
        {
            'file': 'test3.dxf',
            'processing_status': 'unprocessed',
            'annotation_status': 'unannotated',
            'is_current': False,
            'expected': 'test3.dxf (未处理, 未标注)'
        }
    ]
    
    for case in test_cases:
        proc_text = {
            'unprocessed': '未处理', 
            'processing': '处理中', 
            'completed': '已完成'
        }[case['processing_status']]
        
        anno_text = {
            'unannotated': '未标注', 
            'incomplete': '标注未完成', 
            'completed': '标注完成'
        }[case['annotation_status']]
        
        if case['is_current']:
            display_text = f"[{case['file']}] ({proc_text}, {anno_text})"
        else:
            display_text = f"{case['file']} ({proc_text}, {anno_text})"
        
        if display_text == case['expected']:
            print(f"✅ {case['file']}: {display_text}")
        else:
            print(f"❌ {case['file']}: 期望 '{case['expected']}', 实际 '{display_text}'")

def test_data_structure():
    """测试数据结构"""
    print("\n=== 数据结构测试 ===")
    
    # 测试文件数据结构
    file_data_example = {
        'test.dxf': {
            'entities': [],
            'groups': [],
            'labeled_entities': [],
            'dataset': [],
            'groups_info': [],
            'group_fill_status': {},
            'timestamp': '2024-01-01T12:00:00'
        }
    }
    
    # 测试文件状态结构
    file_status_example = {
        'test.dxf': {
            'processing_status': 'completed',
            'annotation_status': 'incomplete'
        }
    }
    
    # 测试保存数据结构
    save_data_example = {
        'folder': '/path/to/folder',
        'files': ['test1.dxf', 'test2.dxf'],
        'file_status': file_status_example,
        'file_data': file_data_example,
        'save_time': '2024-01-01T12:00:00',
        'version': '1.0'
    }
    
    print("✅ 文件数据结构验证通过")
    print("✅ 文件状态结构验证通过")
    print("✅ 保存数据结构验证通过")

def main():
    """主测试函数"""
    print("文件管理功能测试")
    print("=" * 50)
    
    # 测试数据结构
    test_data_structure()
    
    # 测试状态显示
    test_file_status_display()
    
    # 测试文件管理功能
    test_file_management()

if __name__ == "__main__":
    main()
