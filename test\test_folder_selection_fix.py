#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件夹选择修复效果
"""

import sys
import os
import tempfile
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_scenario():
    """创建测试场景"""
    # 创建临时目录结构
    temp_dir = tempfile.mkdtemp()
    
    # 场景1: 包含DXF文件的文件夹
    dxf_folder = os.path.join(temp_dir, "with_dxf")
    os.makedirs(dxf_folder)
    
    # 创建DXF文件
    dxf_file = os.path.join(dxf_folder, "test.dxf")
    with open(dxf_file, 'w') as f:
        f.write("0\nSECTION\n2\nENTITIES\n0\nENDSEC\n0\nEOF\n")
    
    # 创建DWG文件
    dwg_file = os.path.join(dxf_folder, "test.dwg")
    with open(dwg_file, 'w') as f:
        f.write("dummy dwg content")
    
    # 创建非CAD文件
    txt_file = os.path.join(dxf_folder, "readme.txt")
    with open(txt_file, 'w') as f:
        f.write("This is a readme file")
    
    # 场景2: 不包含DXF文件的文件夹
    no_dxf_folder = os.path.join(temp_dir, "no_dxf")
    os.makedirs(no_dxf_folder)
    
    # 只创建非CAD文件
    for i in range(3):
        file_path = os.path.join(no_dxf_folder, f"file_{i}.txt")
        with open(file_path, 'w') as f:
            f.write(f"Content of file {i}")
    
    return temp_dir, dxf_folder, no_dxf_folder

def test_folder_selection_enhanced():
    """测试增强版文件夹选择"""
    print("=== 测试增强版文件夹选择 ===")
    
    try:
        from main_enhanced import EnhancedCADApp
        
        # 创建测试场景
        temp_dir, dxf_folder, no_dxf_folder = create_test_scenario()
        print(f"创建测试目录: {temp_dir}")
        print(f"  有DXF文件的文件夹: {dxf_folder}")
        print(f"  无DXF文件的文件夹: {no_dxf_folder}")
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADApp(root)
        
        # 测试1: 选择有DXF文件的文件夹
        print(f"\n📁 测试1: 选择有DXF文件的文件夹")
        app.folder_var.set(dxf_folder)
        
        # 模拟文件夹选择后的处理
        if os.path.exists(dxf_folder) and os.path.isdir(dxf_folder):
            cad_files = []
            for file_name in os.listdir(dxf_folder):
                file_path = os.path.join(dxf_folder, file_name)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file_name.lower())
                    if ext in ['.dxf', '.dwg']:
                        cad_files.append(file_name)
            
            print(f"  找到 {len(cad_files)} 个CAD文件: {cad_files}")
            
            if len(cad_files) > 0:
                print("  ✅ 测试1通过")
                test1_result = True
            else:
                print("  ❌ 测试1失败")
                test1_result = False
        else:
            print("  ❌ 文件夹不存在或不是目录")
            test1_result = False
        
        # 测试2: 选择无DXF文件的文件夹
        print(f"\n📁 测试2: 选择无DXF文件的文件夹")
        app.folder_var.set(no_dxf_folder)
        
        if os.path.exists(no_dxf_folder) and os.path.isdir(no_dxf_folder):
            cad_files = []
            for file_name in os.listdir(no_dxf_folder):
                file_path = os.path.join(no_dxf_folder, file_name)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file_name.lower())
                    if ext in ['.dxf', '.dwg']:
                        cad_files.append(file_name)
            
            print(f"  找到 {len(cad_files)} 个CAD文件: {cad_files}")
            
            if len(cad_files) == 0:
                print("  ✅ 测试2通过（正确识别无CAD文件）")
                test2_result = True
            else:
                print("  ❌ 测试2失败")
                test2_result = False
        else:
            print("  ❌ 文件夹不存在或不是目录")
            test2_result = False
        
        # 测试3: 选择不存在的文件夹
        print(f"\n📁 测试3: 选择不存在的文件夹")
        fake_folder = os.path.join(temp_dir, "nonexistent")
        app.folder_var.set(fake_folder)
        
        if not os.path.exists(fake_folder):
            print("  ✅ 测试3通过（正确识别不存在的文件夹）")
            test3_result = True
        else:
            print("  ❌ 测试3失败")
            test3_result = False
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        return test1_result and test2_result and test3_result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_v2_folder_scan():
    """测试V2版本的文件夹扫描"""
    print("\n=== 测试V2版本文件夹扫描 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, dxf_folder, no_dxf_folder = create_test_scenario()
        print(f"创建测试目录: {temp_dir}")
        
        # 创建应用（不显示窗口）
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 测试1: 扫描有DXF文件的文件夹
        print(f"\n📁 测试V2扫描: 有DXF文件的文件夹")
        app.current_folder = dxf_folder
        app._scan_folder_files()
        
        print(f"  扫描结果: 找到 {len(app.all_files)} 个文件")
        print(f"  文件状态: {len(app.file_status)} 个文件状态")
        
        if len(app.all_files) >= 2:  # 应该找到test.dxf和test.dwg
            print("  ✅ V2测试1通过")
            v2_test1_result = True
        else:
            print("  ❌ V2测试1失败")
            v2_test1_result = False
        
        # 测试2: 扫描无DXF文件的文件夹
        print(f"\n📁 测试V2扫描: 无DXF文件的文件夹")
        app.current_folder = no_dxf_folder
        app._scan_folder_files()
        
        print(f"  扫描结果: 找到 {len(app.all_files)} 个文件")
        
        if len(app.all_files) == 0:
            print("  ✅ V2测试2通过")
            v2_test2_result = True
        else:
            print("  ❌ V2测试2失败")
            v2_test2_result = False
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        return v2_test1_result and v2_test2_result
        
    except Exception as e:
        print(f"❌ V2测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件夹选择修复测试")
    print("=" * 50)
    print("目标: 验证文件夹选择和扫描功能是否正常")
    print()
    
    tests = [
        ("增强版文件夹选择", test_folder_selection_enhanced),
        ("V2版本文件夹扫描", test_v2_folder_scan)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件夹选择修复成功。")
        print("\n📋 修复效果:")
        print("✅ 文件夹选择时会预检查CAD文件")
        print("✅ 提供详细的扫描调试信息")
        print("✅ 正确处理不存在的文件夹")
        print("✅ 正确处理无CAD文件的文件夹")
        print("✅ 显示详细的诊断对话框")
        
        print("\n🎯 用户使用效果:")
        print("• 选择文件夹时立即知道是否包含CAD文件")
        print("• 如果没有CAD文件会显示详细的诊断信息")
        print("• 控制台会输出详细的扫描过程")
        print("• 能正确识别.dxf和.dwg文件")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
