#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试镜像修复功能
"""

import sys
import os
import numpy as np

def test_mirror_detection():
    """测试镜像检测功能"""
    try:
        from cad_visualizer import CADVisualizer
        
        print("测试镜像检测功能:")
        
        visualizer = CADVisualizer()
        
        # 测试用例：不同的镜像情况
        test_cases = [
            {
                'name': '正常实体（无镜像）',
                'entity': {'scale_x': 1.0, 'scale_y': 1.0},
                'expected': False
            },
            {
                'name': '水平镜像',
                'entity': {'scale_x': -1.0, 'scale_y': 1.0},
                'expected': True
            },
            {
                'name': '垂直镜像',
                'entity': {'scale_x': 1.0, 'scale_y': -1.0},
                'expected': True
            },
            {
                'name': '对角镜像',
                'entity': {'scale_x': -1.0, 'scale_y': -1.0},
                'expected': False  # 两次镜像相当于旋转180度
            },
            {
                'name': '变换矩阵镜像',
                'entity': {'transform_matrix': [[-1, 0], [0, 1]]},
                'expected': True
            },
            {
                'name': '缩放数组格式',
                'entity': {'scale': [-1.0, 1.0]},
                'expected': True
            },
            {
                'name': '块引用镜像',
                'entity': {'block_scale': (-1.0, 1.0)},
                'expected': True
            },
            {
                'name': '显式镜像标记',
                'entity': {'mirrored': True},
                'expected': True
            }
        ]
        
        all_passed = True
        for test_case in test_cases:
            result = visualizer._check_arc_mirrored(test_case['entity'])
            expected = test_case['expected']
            
            if result == expected:
                print(f"✓ {test_case['name']}: {result} (正确)")
            else:
                print(f"✗ {test_case['name']}: 期望{expected}, 实际{result}")
                all_passed = False
        
        if all_passed:
            print("✓ 镜像检测功能测试通过")
            return True
        else:
            print("✗ 镜像检测功能测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 镜像检测功能测试异常: {e}")
        return False

def test_scale_factor_extraction():
    """测试缩放因子提取功能"""
    try:
        from cad_visualizer import CADVisualizer
        
        print("测试缩放因子提取功能:")
        
        visualizer = CADVisualizer()
        
        # 测试用例：不同的缩放因子格式
        test_cases = [
            {
                'name': '直接属性',
                'entity': {'scale_x': -1.5, 'scale_y': 2.0},
                'expected': (-1.5, 2.0)
            },
            {
                'name': '缩放数组',
                'entity': {'scale': [-2.0, 1.5]},
                'expected': (-2.0, 1.5)
            },
            {
                'name': '缩放标量',
                'entity': {'scale': 1.5},
                'expected': (1.5, 1.5)
            },
            {
                'name': '变换矩阵',
                'entity': {'transform_matrix': [[-1.0, 0], [0, 2.0]]},
                'expected': (-1.0, 2.0)
            },
            {
                'name': '块引用缩放',
                'entity': {'block_scale': [0.5, -1.0]},
                'expected': (0.5, -1.0)
            },
            {
                'name': '默认值',
                'entity': {},
                'expected': (1.0, 1.0)
            }
        ]
        
        all_passed = True
        for test_case in test_cases:
            result = visualizer._extract_scale_factors(test_case['entity'])
            expected = test_case['expected']
            
            # 允许小的浮点误差
            if abs(result[0] - expected[0]) < 1e-6 and abs(result[1] - expected[1]) < 1e-6:
                print(f"✓ {test_case['name']}: {result} (正确)")
            else:
                print(f"✗ {test_case['name']}: 期望{expected}, 实际{result}")
                all_passed = False
        
        if all_passed:
            print("✓ 缩放因子提取功能测试通过")
            return True
        else:
            print("✗ 缩放因子提取功能测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 缩放因子提取功能测试异常: {e}")
        return False

def test_arc_angle_correction():
    """测试圆弧角度校正功能"""
    try:
        from cad_visualizer import CADVisualizer
        
        print("测试圆弧角度校正功能:")
        
        visualizer = CADVisualizer()
        
        # 测试用例：不同的角度校正情况
        test_cases = [
            {
                'name': '第一象限圆弧',
                'start_angle': 30,
                'end_angle': 120,
                'description': '30°→120°'
            },
            {
                'name': '跨象限圆弧',
                'start_angle': 330,
                'end_angle': 60,
                'description': '330°→60°'
            },
            {
                'name': '大角度圆弧',
                'start_angle': 45,
                'end_angle': 315,
                'description': '45°→315°'
            },
            {
                'name': '小角度圆弧',
                'start_angle': 80,
                'end_angle': 100,
                'description': '80°→100°'
            }
        ]
        
        for test_case in test_cases:
            start = test_case['start_angle']
            end = test_case['end_angle']
            
            print(f"\n测试: {test_case['name']} ({test_case['description']})")
            print(f"  原始角度: 起点={start}°, 终点={end}°")
            
            # 执行角度校正
            corrected_start, corrected_end = visualizer._fix_mirrored_arc_angles(start, end)
            
            print(f"  校正后: 起点={corrected_start:.1f}°, 终点={corrected_end:.1f}°")
            
            # 验证角度在有效范围内
            if 0 <= corrected_start <= 360 and 0 <= corrected_end <= 720:  # 允许end_angle > 360
                print(f"  ✓ 角度范围有效")
            else:
                print(f"  ✗ 角度范围无效")
                return False
        
        print("\n✓ 圆弧角度校正功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧角度校正功能测试异常: {e}")
        return False

def test_mirror_scenarios():
    """测试镜像场景"""
    try:
        print("测试镜像场景:")
        
        # 模拟不同的镜像场景
        scenarios = [
            {
                'name': '水平镜像',
                'description': '沿垂直轴镜像',
                'scale': (-1, 1),
                'original_arc': {'start': 30, 'end': 120},
                'expected_mirror': True
            },
            {
                'name': '垂直镜像',
                'description': '沿水平轴镜像',
                'scale': (1, -1),
                'original_arc': {'start': 30, 'end': 120},
                'expected_mirror': True
            },
            {
                'name': '对角镜像',
                'description': '沿对角线镜像',
                'scale': (-1, -1),
                'original_arc': {'start': 30, 'end': 120},
                'expected_mirror': False  # 两次镜像
            },
            {
                'name': '非均匀缩放',
                'description': '不同X、Y缩放',
                'scale': (-2, 1.5),
                'original_arc': {'start': 45, 'end': 135},
                'expected_mirror': True
            }
        ]
        
        for scenario in scenarios:
            print(f"\n场景: {scenario['name']} - {scenario['description']}")
            print(f"  缩放: {scenario['scale']}")
            print(f"  原始圆弧: {scenario['original_arc']['start']}° → {scenario['original_arc']['end']}°")
            
            # 计算镜像效果
            scale_x, scale_y = scenario['scale']
            mirror_x = 1 if scale_x >= 0 else -1
            mirror_y = 1 if scale_y >= 0 else -1
            mirror_effect = mirror_x * mirror_y
            
            is_mirrored = mirror_effect < 0
            print(f"  镜像检测: mirror_effect={mirror_effect}, is_mirrored={is_mirrored}")
            
            if is_mirrored == scenario['expected_mirror']:
                print(f"  ✓ 镜像检测正确")
            else:
                print(f"  ✗ 镜像检测错误")
                return False
            
            # 如果是镜像，计算校正后的角度
            if is_mirrored:
                start = scenario['original_arc']['start']
                end = scenario['original_arc']['end']
                
                # 应用角度校正算法
                corrected_start = 360 - start
                corrected_end = 360 - end
                corrected_start, corrected_end = corrected_end, corrected_start
                
                print(f"  校正后圆弧: {corrected_start}° → {corrected_end}°")
        
        print("\n✓ 镜像场景测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 镜像场景测试异常: {e}")
        return False

def test_code_integration():
    """测试代码集成"""
    try:
        print("测试代码集成:")
        
        # 检查关键方法是否存在
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        methods = [
            '_check_arc_mirrored',
            '_fix_mirrored_arc_angles',
            '_extract_scale_factors'
        ]
        
        for method in methods:
            if hasattr(visualizer, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                return False
        
        # 检查方法是否可调用
        test_entity = {'scale_x': -1.0, 'scale_y': 1.0}
        
        try:
            result1 = visualizer._check_arc_mirrored(test_entity)
            print(f"✓ _check_arc_mirrored 可调用，返回: {result1}")
        except Exception as e:
            print(f"✗ _check_arc_mirrored 调用失败: {e}")
            return False
        
        try:
            result2 = visualizer._fix_mirrored_arc_angles(30, 120)
            print(f"✓ _fix_mirrored_arc_angles 可调用，返回: {result2}")
        except Exception as e:
            print(f"✗ _fix_mirrored_arc_angles 调用失败: {e}")
            return False
        
        try:
            result3 = visualizer._extract_scale_factors(test_entity)
            print(f"✓ _extract_scale_factors 可调用，返回: {result3}")
        except Exception as e:
            print(f"✗ _extract_scale_factors 调用失败: {e}")
            return False
        
        print("✓ 代码集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 代码集成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试镜像修复功能...")
    print("=" * 60)
    
    tests = [
        ("镜像检测功能", test_mirror_detection),
        ("缩放因子提取功能", test_scale_factor_extraction),
        ("圆弧角度校正功能", test_arc_angle_correction),
        ("镜像场景", test_mirror_scenarios),
        ("代码集成", test_code_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 镜像修复功能测试全部通过！")
        print("\n📋 修复总结:")
        print("✅ 基于缩放因子的精确镜像检测")
        print("✅ 几何一致性的角度校正算法")
        print("✅ 支持圆弧和椭圆的镜像处理")
        print("✅ 多种缩放因子格式支持")
        print("✅ 详细的调试信息输出")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
