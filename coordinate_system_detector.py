#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系手性检测器
基于DXF文件中的拉伸方向(Extrusion Direction)检测坐标系手性变化并进行角度校正
"""

import numpy as np
import math
from typing import Dict, Any, Tuple, List
from enum import Enum

class CoordinateSystemType(Enum):
    """坐标系类型"""
    RIGHT_HANDED = "右手坐标系"  # RHS - 逆时针为正
    LEFT_HANDED = "左手坐标系"   # LHS - 顺时针为正

class CoordinateSystemDetector:
    """
    坐标系手性检测器
    
    基于DXF规范中的拉伸方向(210, 220, 230)检测坐标系手性：
    - (0, 0, 1): 右手坐标系 (RHS) - 角度正方向逆时针 (CCW)
    - (0, 0, -1): 左手坐标系 (LHS) - 角度正方向顺时针 (CW)
    """
    
    def __init__(self):
        """初始化坐标系检测器"""
        self.default_extrusion = (0, 0, 1)  # 默认拉伸方向 (RHS)
    
    def detect_coordinate_system(self, entity: Dict[str, Any]) -> CoordinateSystemType:
        """
        检测实体的坐标系类型
        
        Args:
            entity: DXF实体数据
            
        Returns:
            坐标系类型
        """
        extrusion_dir = self._get_extrusion_direction(entity)
        z_component = extrusion_dir[2]
        
        if z_component >= 0:
            return CoordinateSystemType.RIGHT_HANDED
        else:
            return CoordinateSystemType.LEFT_HANDED
    
    def _get_extrusion_direction(self, entity: Dict[str, Any]) -> Tuple[float, float, float]:
        """
        获取实体的拉伸方向
        
        Args:
            entity: DXF实体数据
            
        Returns:
            拉伸方向向量 (x, y, z)
        """
        # 检查是否有显式的拉伸方向
        if 'extrusion_direction' in entity:
            return tuple(entity['extrusion_direction'])
        
        # 检查DXF组码 210, 220, 230
        if all(key in entity for key in ['extrusion_x', 'extrusion_y', 'extrusion_z']):
            return (entity['extrusion_x'], entity['extrusion_y'], entity['extrusion_z'])
        
        # 默认拉伸方向
        return self.default_extrusion
    
    def correct_arc_angles_by_handedness(self, arc_entity: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于坐标系手性校正圆弧角度
        
        Args:
            arc_entity: 圆弧实体数据
            
        Returns:
            校正后的圆弧实体数据
        """
        corrected_entity = arc_entity.copy()
        
        # 检测坐标系类型
        coord_system = self.detect_coordinate_system(arc_entity)
        
        if coord_system == CoordinateSystemType.LEFT_HANDED:
            # LHS需要进行角度校正
            original_start = arc_entity.get('start_angle', 0)
            original_end = arc_entity.get('end_angle', 360)
            
            # 角度单位转换（如果需要）
            if abs(original_start) <= 2*np.pi and abs(original_end) <= 2*np.pi:
                original_start = np.degrees(original_start)
                original_end = np.degrees(original_end)
            
            # LHS角度校正公式
            corrected_start = 360.0 - original_end
            corrected_end = 360.0 - original_start
            
            # 角度归一化到[0, 360)范围
            corrected_start = corrected_start % 360
            corrected_end = corrected_end % 360
            
            # 更新实体数据
            corrected_entity['start_angle'] = corrected_start
            corrected_entity['end_angle'] = corrected_end
            
            # 重置拉伸方向为RHS
            corrected_entity['extrusion_direction'] = self.default_extrusion
            
            # 添加校正信息
            corrected_entity['coordinate_system_correction'] = {
                'original_system': coord_system.value,
                'corrected_system': CoordinateSystemType.RIGHT_HANDED.value,
                'original_angles': (original_start, original_end),
                'corrected_angles': (corrected_start, corrected_end),
                'extrusion_direction': self._get_extrusion_direction(arc_entity)
            }
        else:
            # RHS不需要校正，但添加检测信息
            corrected_entity['coordinate_system_correction'] = {
                'original_system': coord_system.value,
                'corrected_system': coord_system.value,
                'correction_needed': False,
                'extrusion_direction': self._get_extrusion_direction(arc_entity)
            }
        
        return corrected_entity
    
    def correct_ellipse_params_by_handedness(self, ellipse_entity: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于坐标系手性校正椭圆参数
        
        Args:
            ellipse_entity: 椭圆实体数据
            
        Returns:
            校正后的椭圆实体数据
        """
        corrected_entity = ellipse_entity.copy()
        
        # 检测坐标系类型
        coord_system = self.detect_coordinate_system(ellipse_entity)
        
        if coord_system == CoordinateSystemType.LEFT_HANDED:
            # LHS需要进行参数校正
            original_start = ellipse_entity.get('start_param', 0)
            original_end = ellipse_entity.get('end_param', 2 * np.pi)
            
            # LHS参数校正公式（类似圆弧角度校正）
            corrected_start = 2 * np.pi - original_end
            corrected_end = 2 * np.pi - original_start
            
            # 参数归一化到[0, 2π)范围
            corrected_start = corrected_start % (2 * np.pi)
            corrected_end = corrected_end % (2 * np.pi)
            
            # 更新实体数据
            corrected_entity['start_param'] = corrected_start
            corrected_entity['end_param'] = corrected_end
            
            # 重置拉伸方向为RHS
            corrected_entity['extrusion_direction'] = self.default_extrusion
            
            # 添加校正信息
            corrected_entity['coordinate_system_correction'] = {
                'original_system': coord_system.value,
                'corrected_system': CoordinateSystemType.RIGHT_HANDED.value,
                'original_params': (original_start, original_end),
                'corrected_params': (corrected_start, corrected_end),
                'extrusion_direction': self._get_extrusion_direction(ellipse_entity)
            }
        else:
            # RHS不需要校正，但添加检测信息
            corrected_entity['coordinate_system_correction'] = {
                'original_system': coord_system.value,
                'corrected_system': coord_system.value,
                'correction_needed': False,
                'extrusion_direction': self._get_extrusion_direction(ellipse_entity)
            }
        
        return corrected_entity
    
    def analyze_entities_coordinate_systems(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析实体列表的坐标系情况
        
        Args:
            entities: 实体列表
            
        Returns:
            分析结果统计
        """
        analysis = {
            'total_entities': len(entities),
            'rhs_count': 0,
            'lhs_count': 0,
            'correction_needed': 0,
            'entities_by_system': {
                CoordinateSystemType.RIGHT_HANDED.value: [],
                CoordinateSystemType.LEFT_HANDED.value: []
            },
            'extrusion_directions': []
        }
        
        for i, entity in enumerate(entities):
            if entity.get('type') in ['ARC', 'ELLIPSE']:
                coord_system = self.detect_coordinate_system(entity)
                extrusion_dir = self._get_extrusion_direction(entity)
                
                if coord_system == CoordinateSystemType.RIGHT_HANDED:
                    analysis['rhs_count'] += 1
                    analysis['entities_by_system'][CoordinateSystemType.RIGHT_HANDED.value].append(i)
                else:
                    analysis['lhs_count'] += 1
                    analysis['entities_by_system'][CoordinateSystemType.LEFT_HANDED.value].append(i)
                    analysis['correction_needed'] += 1
                
                analysis['extrusion_directions'].append({
                    'entity_index': i,
                    'entity_type': entity.get('type'),
                    'extrusion_direction': extrusion_dir,
                    'coordinate_system': coord_system.value
                })
        
        return analysis
    
    def create_handedness_corrected_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        创建手性校正后的实体列表
        
        Args:
            entities: 原始实体列表
            
        Returns:
            校正后的实体列表
        """
        corrected_entities = []
        
        for entity in entities:
            if entity.get('type') == 'ARC':
                corrected_entity = self.correct_arc_angles_by_handedness(entity)
                corrected_entities.append(corrected_entity)
            elif entity.get('type') == 'ELLIPSE':
                corrected_entity = self.correct_ellipse_params_by_handedness(entity)
                corrected_entities.append(corrected_entity)
            else:
                # 其他类型实体不需要校正
                corrected_entities.append(entity.copy())
        
        return corrected_entities
    
    def get_correction_summary(self, entities: List[Dict[str, Any]]) -> str:
        """
        获取校正摘要信息
        
        Args:
            entities: 实体列表
            
        Returns:
            校正摘要文本
        """
        analysis = self.analyze_entities_coordinate_systems(entities)
        
        summary = f"""
🔍 坐标系手性检测结果:
  总实体数: {analysis['total_entities']}
  右手坐标系 (RHS): {analysis['rhs_count']} 个
  左手坐标系 (LHS): {analysis['lhs_count']} 个
  需要校正: {analysis['correction_needed']} 个

📐 拉伸方向分析:"""
        
        for ext_info in analysis['extrusion_directions']:
            ext_dir = ext_info['extrusion_direction']
            summary += f"""
  实体 {ext_info['entity_index']} ({ext_info['entity_type']}): 
    拉伸方向: ({ext_dir[0]:.3f}, {ext_dir[1]:.3f}, {ext_dir[2]:.3f})
    坐标系: {ext_info['coordinate_system']}"""
        
        return summary

# 使用示例和测试函数
def test_coordinate_system_detector():
    """测试坐标系检测器"""
    detector = CoordinateSystemDetector()
    
    # 测试实体
    test_entities = [
        # RHS圆弧
        {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 30,
            'start_angle': 0,
            'end_angle': 90,
            'extrusion_direction': (0, 0, 1)  # RHS
        },
        # LHS圆弧（镜像后）
        {
            'type': 'ARC',
            'center': (200, 100),
            'radius': 30,
            'start_angle': 0,
            'end_angle': 90,
            'extrusion_direction': (0, 0, -1)  # LHS
        },
        # RHS椭圆
        {
            'type': 'ELLIPSE',
            'center': (150, 200),
            'major_axis': (40, 0),
            'ratio': 0.6,
            'start_param': 0,
            'end_param': np.pi,
            'extrusion_direction': (0, 0, 1)  # RHS
        },
        # LHS椭圆（镜像后）
        {
            'type': 'ELLIPSE',
            'center': (250, 200),
            'major_axis': (40, 0),
            'ratio': 0.6,
            'start_param': 0,
            'end_param': np.pi,
            'extrusion_direction': (0, 0, -1)  # LHS
        }
    ]
    
    print("🧪 坐标系手性检测器测试")
    print("=" * 50)
    
    # 分析坐标系
    analysis = detector.analyze_entities_coordinate_systems(test_entities)
    print(detector.get_correction_summary(test_entities))
    
    # 创建校正后的实体
    corrected_entities = detector.create_handedness_corrected_entities(test_entities)
    
    print("\n🔧 校正结果:")
    for i, (original, corrected) in enumerate(zip(test_entities, corrected_entities)):
        if 'coordinate_system_correction' in corrected:
            correction_info = corrected['coordinate_system_correction']
            print(f"\n实体 {i} ({original['type']}):")
            print(f"  原始坐标系: {correction_info['original_system']}")
            print(f"  校正后坐标系: {correction_info['corrected_system']}")
            
            if original['type'] == 'ARC' and 'original_angles' in correction_info:
                print(f"  原始角度: {correction_info['original_angles']}")
                print(f"  校正角度: {correction_info['corrected_angles']}")
            elif original['type'] == 'ELLIPSE' and 'original_params' in correction_info:
                print(f"  原始参数: {correction_info['original_params']}")
                print(f"  校正参数: {correction_info['corrected_params']}")

if __name__ == "__main__":
    test_coordinate_system_detector()
