#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试layer类型安全修复
验证自动房间识别中的layer处理是否正确
"""

def test_layer_safety():
    """测试layer类型安全处理"""
    print("🚀 开始测试layer类型安全处理...")
    print("="*80)
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        print("✅ 创建应用实例成功")
        
        # 测试安全layer方法
        print("\n🔧 测试安全layer方法...")
        
        # 测试不同类型的layer值
        test_entities = [
            # 字符串layer
            {'layer': 'wall_layer', 'label': ''},
            {'layer': 'DOOR_LAYER', 'label': ''},
            
            # 整数layer（问题源）
            {'layer': 0, 'label': ''},
            {'layer': 123, 'label': ''},
            
            # 浮点数layer
            {'layer': 1.5, 'label': ''},
            {'layer': 0.0, 'label': ''},
            
            # None layer
            {'layer': None, 'label': ''},
            
            # 空字符串layer
            {'layer': '', 'label': ''},
            
            # 复杂对象layer
            {'layer': {'complex': 'object'}, 'label': ''},
        ]
        
        success_count = 0
        for i, entity in enumerate(test_entities):
            try:
                layer_name = app._safe_get_layer_name(entity)
                print(f"   ✅ 实体 {i+1}: layer={entity['layer']} -> '{layer_name}'")
                success_count += 1
            except Exception as e:
                print(f"   ❌ 实体 {i+1}: layer={entity['layer']} -> 失败: {e}")
        
        print(f"📊 安全layer方法测试: {success_count}/{len(test_entities)} 成功")
        
        # 测试数据获取方法
        print("\n📊 测试数据获取方法...")
        
        # 创建包含不同layer类型的模拟数据
        mock_groups = [
            [
                # 墙体实体（不同layer类型）
                {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 1000, 'y': 0}, 'label': 'wall', 'layer': 'wall_layer'},
                {'type': 'LINE', 'start': {'x': 1000, 'y': 0}, 'end': {'x': 1000, 'y': 1000}, 'label': 'wall', 'layer': 0},
                {'type': 'LINE', 'start': {'x': 1000, 'y': 1000}, 'end': {'x': 0, 'y': 1000}, 'label': 'wall', 'layer': 1.5},
                {'type': 'LINE', 'start': {'x': 0, 'y': 1000}, 'end': {'x': 0, 'y': 0}, 'label': 'wall', 'layer': None},
            ],
            [
                # 门窗实体（不同layer类型）
                {'type': 'LINE', 'start': {'x': 400, 'y': 0}, 'end': {'x': 600, 'y': 0}, 'label': 'door', 'layer': 'door_layer'},
                {'type': 'LINE', 'start': {'x': 1000, 'y': 400}, 'end': {'x': 1000, 'y': 600}, 'label': 'window', 'layer': 123},
            ],
            [
                # 未标注实体（需要根据layer判断）
                {'type': 'LINE', 'start': {'x': 200, 'y': 500}, 'end': {'x': 800, 'y': 500}, 'label': '', 'layer': 'wall'},
                {'type': 'LINE', 'start': {'x': 500, 'y': 200}, 'end': {'x': 500, 'y': 800}, 'label': 'none', 'layer': 456},
                {'type': 'LINE', 'start': {'x': 300, 'y': 300}, 'end': {'x': 700, 'y': 300}, 'label': 'unlabeled', 'layer': 'door'},
            ]
        ]
        
        # 设置模拟数据
        if hasattr(app, 'processor') and app.processor:
            app.processor.all_groups = mock_groups
            print("✅ 设置模拟数据完成")
        else:
            print("⚠️ 处理器不存在，创建模拟处理器...")
            # 创建模拟处理器
            class MockProcessor:
                def __init__(self):
                    self.all_groups = mock_groups

            app.processor = MockProcessor()
            print("✅ 模拟处理器创建完成")
        
        # 测试数据获取
        try:
            data = app._get_wall_door_data()
            
            if data:
                wall_groups = data.get('wall_groups', [])
                door_window_groups = data.get('door_window_groups', [])
                
                print(f"✅ 数据获取成功:")
                print(f"   墙体组: {len(wall_groups)} 个")
                print(f"   门窗组: {len(door_window_groups)} 个")
                
                # 统计实体数量
                total_wall_entities = sum(len(group) for group in wall_groups)
                total_door_entities = sum(len(group) for group in door_window_groups)
                
                print(f"   墙体实体总数: {total_wall_entities}")
                print(f"   门窗实体总数: {total_door_entities}")
                
                if total_wall_entities > 0:
                    print("✅ 数据获取方法正常工作")
                    return True
                else:
                    print("⚠️ 未获取到墙体实体")
                    return False
            else:
                print("❌ 数据获取返回空")
                return False
                
        except Exception as e:
            print(f"❌ 数据获取测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试房间识别UI
        print("\n🎨 测试房间识别UI...")
        
        if hasattr(app, 'room_ui') and app.room_ui:
            try:
                # 测试自动识别方法（不实际执行，只检查是否存在）
                if hasattr(app.room_ui, '_auto_room_recognition'):
                    print("✅ 自动识别方法存在")
                    
                    # 测试数据获取方法
                    if hasattr(app.room_ui, '_get_wall_door_data'):
                        print("✅ UI数据获取方法存在")
                        
                        # 模拟调用（通过回调）
                        if app.room_ui.update_callback:
                            test_data = app.room_ui.update_callback('get_wall_door_data')
                            if test_data:
                                print("✅ UI回调数据获取正常")
                            else:
                                print("⚠️ UI回调数据获取返回空")
                    else:
                        print("❌ UI数据获取方法不存在")
                        return False
                else:
                    print("❌ 自动识别方法不存在")
                    return False
            except Exception as e:
                print(f"❌ UI测试失败: {e}")
                return False
        else:
            print("❌ 房间识别UI不存在")
            return False
        
        print("✅ 所有测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理
        try:
            root.destroy()
        except:
            pass

def main():
    """主函数"""
    print("🚀 Layer类型安全修复测试")
    print("="*80)
    
    try:
        success = test_layer_safety()
        
        print("\n" + "="*80)
        print("📊 测试总结:")
        
        if success:
            print("🎉 Layer类型安全修复测试成功！")
            print("\n💡 修复的问题:")
            print("   1. ✅ 整数layer值安全处理")
            print("   2. ✅ 浮点数layer值安全处理")
            print("   3. ✅ None layer值安全处理")
            print("   4. ✅ 复杂对象layer值安全处理")
            print("   5. ✅ 数据获取方法正常工作")
            print("   6. ✅ UI回调机制正常")
            
            print("\n🎯 修复方案:")
            print("   - 添加 _safe_get_layer_name() 方法")
            print("   - 类型检查和安全转换")
            print("   - 异常处理和默认值")
            print("   - 在数据获取中使用安全方法")
        else:
            print("❌ Layer类型安全修复测试失败")
            print("💡 请检查修复实现")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
