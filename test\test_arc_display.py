#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试圆弧显示修复
"""

import sys
import os
import matplotlib.pyplot as plt
import numpy as np

def test_arc_visualization():
    """测试圆弧可视化修复"""
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 创建测试圆弧实体
        test_arcs = [
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 50,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'test'
            },
            {
                'type': 'ARC',
                'center': (200, 100),
                'radius': 30,
                'start_angle': 45,
                'end_angle': 180,
                'layer': 'test'
            },
            {
                'type': 'ARC',
                'center': (300, 100),
                'radius': 40,
                'start_angle': 270,
                'end_angle': 45,  # 跨越0度的圆弧
                'layer': 'test'
            }
        ]
        
        print("测试圆弧可视化:")
        print(f"测试圆弧数量: {len(test_arcs)}")
        
        for i, arc in enumerate(test_arcs):
            print(f"圆弧 {i+1}: 中心={arc['center']}, 半径={arc['radius']}, "
                  f"起始角度={arc['start_angle']}°, 结束角度={arc['end_angle']}°")
        
        # 测试可视化
        try:
            # 清除之前的图形
            visualizer.ax_detail.clear()
            
            # 绘制圆弧
            for arc in test_arcs:
                visualizer._draw_entity(visualizer.ax_detail, arc, '#FF0000', 2, 1.0)
            
            # 设置坐标轴
            visualizer.ax_detail.set_xlim(0, 400)
            visualizer.ax_detail.set_ylim(0, 200)
            visualizer.ax_detail.set_aspect('equal')
            visualizer.ax_detail.grid(True, alpha=0.3)
            visualizer.ax_detail.set_title('圆弧显示测试')
            
            print("✓ 圆弧可视化代码执行成功")
            return True
            
        except Exception as e:
            print(f"✗ 圆弧可视化执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"✗ 圆弧可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arc_angle_handling():
    """测试圆弧角度处理"""
    try:
        print("测试圆弧角度处理:")
        
        # 测试角度范围处理逻辑
        test_cases = [
            (0, 90, "正常范围"),
            (45, 180, "正常范围"),
            (270, 45, "跨越0度"),
            (300, 60, "跨越0度"),
            (0, 360, "完整圆"),
            (180, 180, "零度圆弧")
        ]
        
        for start_angle, end_angle, description in test_cases:
            # 模拟角度处理逻辑
            processed_end = end_angle
            if end_angle < start_angle:
                processed_end += 360
            
            print(f"  {description}: {start_angle}° -> {end_angle}° (处理后: {start_angle}° -> {processed_end}°)")
        
        print("✓ 圆弧角度处理逻辑正确")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧角度处理测试失败: {e}")
        return False

def test_arc_drawing_method():
    """测试圆弧绘制方法"""
    try:
        from matplotlib.patches import Arc
        import matplotlib.pyplot as plt
        
        print("测试圆弧绘制方法:")
        
        # 创建测试图形
        fig, ax = plt.subplots(1, 1, figsize=(6, 6))
        
        # 测试Arc patch
        center = (0, 0)
        radius = 1
        start_angle = 0
        end_angle = 90
        
        arc = Arc(center, 2*radius, 2*radius, 
                 theta1=start_angle, theta2=end_angle,
                 edgecolor='red', linewidth=2)
        ax.add_patch(arc)
        
        ax.set_xlim(-2, 2)
        ax.set_ylim(-2, 2)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        
        plt.close(fig)  # 关闭图形以避免显示
        
        print("✓ matplotlib Arc patch 工作正常")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧绘制方法测试失败: {e}")
        return False

def test_arc_entity_structure():
    """测试圆弧实体结构"""
    try:
        print("测试圆弧实体结构:")
        
        # 检查圆弧实体应该包含的字段
        required_fields = ['type', 'center', 'radius', 'start_angle', 'end_angle']
        
        test_arc = {
            'type': 'ARC',
            'center': (100, 100),
            'radius': 50,
            'start_angle': 0,
            'end_angle': 90,
            'layer': 'test'
        }
        
        missing_fields = []
        for field in required_fields:
            if field not in test_arc:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"✗ 圆弧实体缺少字段: {missing_fields}")
            return False
        else:
            print("✓ 圆弧实体结构完整")
            
        # 检查字段类型
        if not isinstance(test_arc['center'], (tuple, list)) or len(test_arc['center']) != 2:
            print("✗ center字段格式错误")
            return False
        
        if not isinstance(test_arc['radius'], (int, float)) or test_arc['radius'] <= 0:
            print("✗ radius字段格式错误")
            return False
        
        if not isinstance(test_arc['start_angle'], (int, float)):
            print("✗ start_angle字段格式错误")
            return False
        
        if not isinstance(test_arc['end_angle'], (int, float)):
            print("✗ end_angle字段格式错误")
            return False
        
        print("✓ 圆弧实体字段类型正确")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧实体结构测试失败: {e}")
        return False

def test_visualizer_arc_method():
    """测试可视化器中的圆弧绘制方法"""
    try:
        print("测试可视化器圆弧绘制方法:")
        
        # 检查代码中是否包含了正确的圆弧绘制逻辑
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码
        checks = [
            ("Arc导入", "from matplotlib.patches import"),
            ("圆弧类型检查", "entity['type'] == 'ARC'"),
            ("Arc patch创建", "Arc(center"),
            ("角度处理", "theta1="),
            ("添加patch", "ax.add_patch(arc)")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已包含")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 可视化器圆弧绘制方法正确实现")
        return True
        
    except Exception as e:
        print(f"✗ 可视化器圆弧方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试圆弧显示修复...")
    print("=" * 50)
    
    tests = [
        ("圆弧角度处理", test_arc_angle_handling),
        ("圆弧绘制方法", test_arc_drawing_method),
        ("圆弧实体结构", test_arc_entity_structure),
        ("可视化器圆弧方法", test_visualizer_arc_method),
        ("圆弧可视化", test_arc_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧显示修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
