#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圆弧变换可视化对比测试
"""

import sys
import os
import math
import matplotlib.pyplot as plt
from matplotlib.patches import Arc, Circle
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import TransformationMatrix, CADDataProcessor

def create_test_arc(center_x=0, center_y=0, radius=1.0, start_angle=0, end_angle=90):
    """创建测试圆弧"""
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    class MockDxf:
        def __init__(self):
            self.center = MockPoint(center_x, center_y)
            self.radius = radius
            self.start_angle = start_angle
            self.end_angle = end_angle
    
    class MockDoc:
        def __init__(self):
            self.header = {'$ANGDIR': 0}  # 逆时针
    
    class MockArc:
        def __init__(self):
            self.dxf = MockDxf()
            self.doc = MockDoc()
    
    return MockArc()

def visualize_arc_transformations():
    """可视化圆弧变换对比"""
    print("=== 生成圆弧变换可视化对比 ===")
    
    processor = CADDataProcessor()
    
    # 测试场景
    test_cases = [
        {
            'name': '无变换',
            'rotation': 0,
            'scale': (1.0, 1.0),
            'color': 'blue'
        },
        {
            'name': 'X轴镜像',
            'rotation': 0,
            'scale': (1.0, -1.0),
            'color': 'red'
        },
        {
            'name': 'Y轴镜像',
            'rotation': 0,
            'scale': (-1.0, 1.0),
            'color': 'green'
        },
        {
            'name': '双轴镜像',
            'rotation': 0,
            'scale': (-1.0, -1.0),
            'color': 'orange'
        },
        {
            'name': '旋转45度',
            'rotation': 45,
            'scale': (1.0, 1.0),
            'color': 'purple'
        },
        {
            'name': 'X轴镜像+45度旋转',
            'rotation': 45,
            'scale': (1.0, -1.0),
            'color': 'brown'
        }
    ]
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    # 原始圆弧
    original_arc = create_test_arc(0, 0, 1, 0, 90)
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    for i, case in enumerate(test_cases):
        ax = axes[i]
        ax.set_xlim(-2, 2)
        ax.set_ylim(-2, 2)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(case['name'], fontsize=12, fontweight='bold')
        
        # 绘制原始圆弧（灰色虚线）
        orig_arc = Arc((0, 0), 2, 2, theta1=0, theta2=90,
                      edgecolor='gray', linestyle='--', linewidth=1, alpha=0.5)
        ax.add_patch(orig_arc)
        
        # 绘制原始圆弧的起点和终点
        start_x = math.cos(math.radians(0))
        start_y = math.sin(math.radians(0))
        end_x = math.cos(math.radians(90))
        end_y = math.sin(math.radians(90))
        ax.plot([start_x], [start_y], 'go', markersize=6, alpha=0.5, label='原始起点')
        ax.plot([end_x], [end_y], 'ro', markersize=6, alpha=0.5, label='原始终点')
        
        # 应用变换
        try:
            matrix = TransformationMatrix(base_point, case['rotation'], case['scale'])
            result = processor._transform_arc(original_arc, matrix)
            
            # 绘制变换后圆弧
            trans_arc = Arc(result['center'], 2*result['radius'], 2*result['radius'],
                           theta1=result['start_angle'], theta2=result['end_angle'],
                           edgecolor=case['color'], linewidth=2)
            ax.add_patch(trans_arc)
            
            # 绘制变换后的起点和终点
            center = result['center']
            radius = result['radius']
            start_rad = math.radians(result['start_angle'])
            end_rad = math.radians(result['end_angle'])
            
            trans_start_x = center[0] + radius * math.cos(start_rad)
            trans_start_y = center[1] + radius * math.sin(start_rad)
            trans_end_x = center[0] + radius * math.cos(end_rad)
            trans_end_y = center[1] + radius * math.sin(end_rad)
            
            ax.plot([trans_start_x], [trans_start_y], 'g^', markersize=8, label='变换后起点')
            ax.plot([trans_end_x], [trans_end_y], 'r^', markersize=8, label='变换后终点')
            
            # 标记中心点
            ax.plot(0, 0, 'ko', markersize=4, alpha=0.5)
            ax.plot(center[0], center[1], 'o', color=case['color'], markersize=4)
            
            # 添加镜像标记
            if matrix.is_mirrored():
                ax.text(0.02, 0.98, '镜像', transform=ax.transAxes, 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                       verticalalignment='top', fontsize=8)
            
            # 添加角度信息
            ax.text(0.02, 0.02, f'角度: {result["start_angle"]:.0f}°-{result["end_angle"]:.0f}°', 
                   transform=ax.transAxes, fontsize=8,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.7))
            
        except Exception as e:
            ax.text(0.5, 0.5, f'变换失败:\n{str(e)}', transform=ax.transAxes,
                   ha='center', va='center', fontsize=10, color='red')
        
        # 添加坐标轴
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
    
    plt.tight_layout()
    plt.savefig('arc_transformation_visual_comparison.png', dpi=150, bbox_inches='tight')
    print("可视化对比图已保存为: arc_transformation_visual_comparison.png")
    plt.show()

def test_complex_arc_scenarios():
    """测试复杂圆弧场景"""
    print("\n=== 复杂圆弧场景测试 ===")
    
    processor = CADDataProcessor()
    
    # 复杂场景
    scenarios = [
        {'name': '跨越0度圆弧', 'arc': create_test_arc(0, 0, 1, 350, 10)},
        {'name': '大角度圆弧', 'arc': create_test_arc(0, 0, 1, 30, 330)},
        {'name': '小角度圆弧', 'arc': create_test_arc(0, 0, 1, 85, 95)},
    ]
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    # 创建图表
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    for i, scenario in enumerate(scenarios):
        ax = axes[i]
        ax.set_xlim(-2, 2)
        ax.set_ylim(-2, 2)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(scenario['name'], fontsize=12, fontweight='bold')
        
        arc = scenario['arc']
        
        # 绘制原始圆弧
        orig_arc = Arc((0, 0), 2, 2, 
                      theta1=arc.dxf.start_angle, theta2=arc.dxf.end_angle,
                      edgecolor='blue', linewidth=2, alpha=0.7)
        ax.add_patch(orig_arc)
        
        # 测试X轴镜像
        try:
            matrix = TransformationMatrix(base_point, 0, (1.0, -1.0))
            result = processor._transform_arc(arc, matrix)
            
            # 绘制镜像后圆弧
            mirror_arc = Arc(result['center'], 2*result['radius'], 2*result['radius'],
                           theta1=result['start_angle'], theta2=result['end_angle'],
                           edgecolor='red', linewidth=2, linestyle='--')
            ax.add_patch(mirror_arc)
            
            print(f"{scenario['name']}: 原始({arc.dxf.start_angle}°-{arc.dxf.end_angle}°) "
                  f"→ 镜像后({result['start_angle']:.1f}°-{result['end_angle']:.1f}°)")
            
        except Exception as e:
            print(f"{scenario['name']}: 镜像变换失败 - {e}")
        
        # 添加坐标轴
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
        
        # 添加图例
        ax.text(0.02, 0.98, '蓝色: 原始\n红色虚线: X轴镜像', 
               transform=ax.transAxes, fontsize=8,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('complex_arc_scenarios.png', dpi=150, bbox_inches='tight')
    print("复杂场景图已保存为: complex_arc_scenarios.png")
    plt.show()

def main():
    """主函数"""
    print("圆弧变换可视化测试")
    print("=" * 40)
    
    try:
        # 基本变换可视化
        visualize_arc_transformations()
        
        # 复杂场景测试
        test_complex_arc_scenarios()
        
        print("\n✅ 可视化测试完成！")
        print("生成的图片文件:")
        print("- arc_transformation_visual_comparison.png")
        print("- complex_arc_scenarios.png")
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
