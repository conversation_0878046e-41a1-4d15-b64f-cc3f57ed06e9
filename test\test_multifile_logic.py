#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多文件处理逻辑测试脚本
测试实体组列表、文件选择的选择及更新逻辑
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_file_combo_logic():
    """测试文件下拉菜单的逻辑"""
    print("🧪 测试文件下拉菜单逻辑...")
    
    class MockCombo:
        def __init__(self):
            self.values = []
            self.current_value = ""
            self.state = "normal"
        
        def __setitem__(self, key, value):
            if key == 'values':
                self.values = value
        
        def set(self, value):
            self.current_value = value
            
        def get(self):
            return self.current_value
            
        def config(self, **kwargs):
            if 'state' in kwargs:
                self.state = kwargs['state']
    
    class MockApp:
        def __init__(self):
            self.file_combo = MockCombo()
            self.all_files = []
            self.file_status = {}
            self.display_file = None
            self.current_file = None
        
        def update_file_combo(self):
            """更新文件选择下拉菜单（测试版）"""
            # 检查file_combo是否已初始化
            if not self.file_combo:
                return

            if not self.all_files:
                self.file_combo['values'] = ["请先选择文件夹或文件"]
                self.file_combo.set("请先选择文件夹或文件")
                self.file_combo.config(state='disabled')
                return

            # 智能下拉框控制：根据文件数量决定是否可下拉
            file_count = len(self.all_files)

            if file_count == 1:
                # 单文件模式
                file_name = self.all_files[0]
                status_info = self.file_status.get(file_name, {
                    'processing_status': 'unprocessed',
                    'annotation_status': 'unannotated'
                })

                proc_status = status_info['processing_status']
                anno_status = status_info['annotation_status']

                proc_text = {'unprocessed': '未处理', 'processing': '处理中', 'completed': '已完成'}[proc_status]
                anno_text = {'unannotated': '未标注', 'incomplete': '标注未完成', 'completed': '标注完成'}[anno_status]

                display_text = f"{file_name} ({proc_text}, {anno_text})"

                self.file_combo['values'] = [display_text]
                self.file_combo.set(display_text)
                self.file_combo.config(state='disabled')

                print(f"📁 单文件模式: {file_name} - 下拉框已禁用")

            else:
                # 多文件模式
                file_display_list = []

                # 确定当前显示的文件
                display_file_name = ""
                if hasattr(self, 'display_file') and self.display_file:
                    display_file_name = os.path.basename(self.display_file)
                    print(f"📁 使用display_file: {display_file_name}")
                elif self.current_file:
                    display_file_name = os.path.basename(self.current_file)
                    print(f"📁 使用current_file: {display_file_name}")
                else:
                    # 如果都没有，使用第一个文件作为显示文件
                    if self.all_files:
                        display_file_name = os.path.basename(self.all_files[0])
                        print(f"📁 使用第一个文件: {display_file_name}")

                for file_path in self.all_files:
                    file_name = os.path.basename(file_path) if os.path.sep in file_path else file_path
                    status_info = self.file_status.get(file_name, {
                        'processing_status': 'unprocessed',
                        'annotation_status': 'unannotated'
                    })

                    # 构建状态显示
                    proc_status = status_info['processing_status']
                    anno_status = status_info['annotation_status']

                    proc_text = {'unprocessed': '未处理', 'processing': '处理中', 'completed': '已完成'}[proc_status]
                    anno_text = {'unannotated': '未标注', 'incomplete': '标注未完成', 'completed': '标注完成'}[anno_status]

                    # 标记当前显示的文件（用方括号）
                    if file_name == display_file_name:
                        display_text = f"[{file_name}] ({proc_text}, {anno_text})"
                    else:
                        display_text = f"{file_name} ({proc_text}, {anno_text})"

                    file_display_list.append(display_text)

                self.file_combo['values'] = file_display_list
                self.file_combo.config(state='readonly')

                # 设置当前选择（基于显示文件，确保选择正确）
                current_selection_set = False
                if display_file_name:
                    for display_text in file_display_list:
                        if display_file_name in display_text and display_text.startswith('['):
                            self.file_combo.set(display_text)
                            current_selection_set = True
                            print(f"📁 设置当前选择: {display_text}")
                            break
                
                # 如果没有设置成功，使用第一个文件
                if not current_selection_set and file_display_list:
                    self.file_combo.set(file_display_list[0])
                    print(f"📁 使用第一个文件作为选择: {file_display_list[0]}")

                print(f"📁 多文件模式: {file_count} 个文件 - 下拉框已启用，显示文件: {display_file_name}")
    
    # 测试场景1：单文件模式
    print("\n📋 测试场景1：单文件模式")
    app1 = MockApp()
    app1.all_files = ["test.dxf"]
    app1.file_status = {
        "test.dxf": {
            'processing_status': 'completed',
            'annotation_status': 'completed'
        }
    }
    app1.update_file_combo()
    print(f"  下拉框状态: {app1.file_combo.state}")
    print(f"  显示内容: {app1.file_combo.get()}")
    assert app1.file_combo.state == 'disabled', "单文件模式应该禁用下拉框"
    print("  ✅ 测试通过")
    
    # 测试场景2：多文件模式，有display_file
    print("\n📋 测试场景2：多文件模式，有display_file")
    app2 = MockApp()
    app2.all_files = ["file1.dxf", "file2.dxf", "file3.dxf"]
    app2.display_file = "file1.dxf"
    app2.current_file = "file2.dxf"  # 当前处理文件不同于显示文件
    app2.file_status = {
        "file1.dxf": {'processing_status': 'completed', 'annotation_status': 'completed'},
        "file2.dxf": {'processing_status': 'processing', 'annotation_status': 'incomplete'},
        "file3.dxf": {'processing_status': 'unprocessed', 'annotation_status': 'unannotated'}
    }
    app2.update_file_combo()
    print(f"  下拉框状态: {app2.file_combo.state}")
    print(f"  显示内容: {app2.file_combo.get()}")
    print(f"  所有选项: {app2.file_combo.values}")
    assert app2.file_combo.state == 'readonly', "多文件模式应该启用下拉框"
    assert '[file1.dxf]' in app2.file_combo.get(), "应该显示display_file作为当前选择"
    print("  ✅ 测试通过")
    
    # 测试场景3：多文件模式，没有display_file
    print("\n📋 测试场景3：多文件模式，没有display_file")
    app3 = MockApp()
    app3.all_files = ["file1.dxf", "file2.dxf"]
    app3.current_file = "file2.dxf"
    app3.file_status = {
        "file1.dxf": {'processing_status': 'completed', 'annotation_status': 'completed'},
        "file2.dxf": {'processing_status': 'processing', 'annotation_status': 'incomplete'}
    }
    app3.update_file_combo()
    print(f"  下拉框状态: {app3.file_combo.state}")
    print(f"  显示内容: {app3.file_combo.get()}")
    print(f"  所有选项: {app3.file_combo.values}")
    assert app3.file_combo.state == 'readonly', "多文件模式应该启用下拉框"
    assert '[file2.dxf]' in app3.file_combo.get(), "应该显示current_file作为当前选择"
    print("  ✅ 测试通过")
    
    print("\n🎉 文件下拉菜单逻辑测试通过！")

def test_group_list_logic():
    """测试组列表更新逻辑"""
    print("\n🧪 测试组列表更新逻辑...")
    
    class MockProcessor:
        def __init__(self):
            self.current_file = None
            self.groups_info = []
    
    class MockApp:
        def __init__(self):
            self.processor = MockProcessor()
            self.display_file = None
            self.file_data = {}
        
        def update_group_list(self):
            """更新组列表显示（测试版）"""
            # 检查是否有处理器和显示文件
            if not self.processor:
                print("⚠️ 没有处理器，跳过组列表更新")
                return
            
            # 确保组列表显示的是当前显示文件的信息
            display_file_name = ""
            if hasattr(self, 'display_file') and self.display_file:
                display_file_name = os.path.basename(self.display_file)
            
            current_file_name = ""
            if hasattr(self.processor, 'current_file') and self.processor.current_file:
                current_file_name = os.path.basename(self.processor.current_file)
            
            # 如果处理器的当前文件不是显示文件，需要特殊处理
            if display_file_name and current_file_name and display_file_name != current_file_name:
                print(f"📋 组列表更新：显示文件({display_file_name}) != 处理器文件({current_file_name})")
                # 检查是否有显示文件的缓存数据
                if display_file_name in self.file_data:
                    print(f"  使用显示文件的缓存数据更新组列表")
                    return "使用缓存数据"
                else:
                    print(f"  显示文件无缓存数据，使用处理器数据")
            
            print(f"📋 组列表更新完成：{current_file_name or '未知文件'}")
            return "使用处理器数据"
    
    # 测试场景1：显示文件与处理器文件相同
    print("\n📋 测试场景1：显示文件与处理器文件相同")
    app1 = MockApp()
    app1.display_file = "file1.dxf"
    app1.processor.current_file = "file1.dxf"
    result1 = app1.update_group_list()
    print(f"  结果: {result1}")
    assert result1 == "使用处理器数据", "相同文件应该使用处理器数据"
    print("  ✅ 测试通过")
    
    # 测试场景2：显示文件与处理器文件不同，有缓存
    print("\n📋 测试场景2：显示文件与处理器文件不同，有缓存")
    app2 = MockApp()
    app2.display_file = "file1.dxf"
    app2.processor.current_file = "file2.dxf"
    app2.file_data = {
        "file1.dxf": {
            "groups_info": [{"status": "labeled"}],
            "all_groups": [[{"label": "wall"}]]
        }
    }
    result2 = app2.update_group_list()
    print(f"  结果: {result2}")
    assert result2 == "使用缓存数据", "不同文件且有缓存应该使用缓存数据"
    print("  ✅ 测试通过")
    
    # 测试场景3：显示文件与处理器文件不同，无缓存
    print("\n📋 测试场景3：显示文件与处理器文件不同，无缓存")
    app3 = MockApp()
    app3.display_file = "file1.dxf"
    app3.processor.current_file = "file2.dxf"
    app3.file_data = {}
    result3 = app3.update_group_list()
    print(f"  结果: {result3}")
    assert result3 == "使用处理器数据", "不同文件且无缓存应该使用处理器数据"
    print("  ✅ 测试通过")
    
    print("\n🎉 组列表更新逻辑测试通过！")

def create_analysis_summary():
    """创建分析总结"""
    print("\n💡 多文件处理逻辑分析总结:")
    print("""
🔍 关键问题分析:
----------------------------------------
1. 文件角色混淆:
   - display_file: 界面显示的文件（用户看到的）
   - current_file: 处理器当前处理的文件（可能在后台）
   - 两者可能不一致，导致界面显示错误

2. 组列表更新问题:
   - 组列表总是显示处理器的当前文件信息
   - 当处理器处理后台文件时，组列表会跳转显示
   - 缺少基于显示文件的缓存数据更新机制

3. 文件选择同步问题:
   - 文件下拉菜单的选择可能与实际显示不符
   - 状态更新时没有正确区分显示文件和处理文件

🔧 修复方案:
----------------------------------------
1. 严格区分文件角色:
   - 优先使用display_file确定界面显示
   - 只有在处理显示文件时才更新界面
   - 后台处理不影响界面显示

2. 智能组列表更新:
   - 检查显示文件与处理器文件是否一致
   - 不一致时优先使用缓存数据
   - 缓存数据不存在时才使用处理器数据

3. 准确的文件选择显示:
   - 基于display_file设置下拉菜单选择
   - 明确标记当前显示的文件
   - 提供详细的状态信息

🎯 预期效果:
----------------------------------------
✅ 界面始终显示第一个文件内容
✅ 组列表正确显示当前显示文件的组信息
✅ 文件下拉菜单正确标记当前显示文件
✅ 后台处理不影响界面显示
✅ 用户可以手动切换查看其他文件

🚀 技术亮点:
----------------------------------------
1. 双重数据源管理（处理器数据 + 缓存数据）
2. 智能界面更新策略
3. 详细的调试日志
4. 防御性编程模式
""")

if __name__ == "__main__":
    print("🚀 开始多文件处理逻辑测试...")
    
    try:
        test_file_combo_logic()
        test_group_list_logic()
        create_analysis_summary()
        
        print("\n🎉 所有测试完成！多文件处理逻辑验证成功。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
