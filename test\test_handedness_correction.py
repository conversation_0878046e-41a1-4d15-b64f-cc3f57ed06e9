#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标系手性校正功能
验证基于DXF拉伸方向的坐标系手性检测和角度校正
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Ellipse as MplEllipse
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_chinese_font():
    """设置中文字体支持"""
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except:
        return False

setup_chinese_font()

def test_handedness_correction():
    """测试坐标系手性校正功能"""
    print("🔄 测试坐标系手性校正功能...")
    print("🎯 基于DXF拉伸方向检测坐标系手性并进行角度校正")
    print("=" * 70)
    
    try:
        from coordinate_system_detector import CoordinateSystemDetector
        from multi_solution_reconstructor import MultiSolutionReconstructor
        
        # 创建检测器和重构器
        detector = CoordinateSystemDetector()
        reconstructor = MultiSolutionReconstructor(sample_points_count=30)
        
        # 创建测试实体 - 包含不同拉伸方向的圆弧和椭圆
        test_entities = [
            # RHS圆弧 (Z+ = 右手坐标系)
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 30,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'arcs',
                'color': 'red',
                'extrusion_direction': (0, 0, 1)  # RHS - 逆时针为正
            },
            # LHS圆弧 (Z- = 左手坐标系，镜像后)
            {
                'type': 'ARC',
                'center': (200, 100),
                'radius': 30,
                'start_angle': 0,
                'end_angle': 90,
                'layer': 'arcs',
                'color': 'red',
                'extrusion_direction': (0, 0, -1)  # LHS - 顺时针为正
            },
            # RHS椭圆弧
            {
                'type': 'ELLIPSE',
                'center': (150, 200),
                'major_axis': (40, 0),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'layer': 'ellipses',
                'color': 'purple',
                'extrusion_direction': (0, 0, 1)  # RHS
            },
            # LHS椭圆弧（镜像后）
            {
                'type': 'ELLIPSE',
                'center': (250, 200),
                'major_axis': (40, 0),
                'ratio': 0.6,
                'start_param': 0,
                'end_param': np.pi,
                'layer': 'ellipses',
                'color': 'purple',
                'extrusion_direction': (0, 0, -1)  # LHS
            },
            # 复杂拉伸方向的圆弧
            {
                'type': 'ARC',
                'center': (300, 150),
                'radius': 25,
                'start_angle': 45,
                'end_angle': 135,
                'layer': 'arcs',
                'color': 'red',
                'extrusion_direction': (0, 0, -0.8)  # LHS变体
            }
        ]
        
        print("🔍 坐标系分析:")
        
        # 分析坐标系情况
        analysis = detector.analyze_entities_coordinate_systems(test_entities)
        print(detector.get_correction_summary(test_entities))
        
        print("\n🔧 手性校正测试:")
        
        # 测试各个实体的校正
        for i, entity in enumerate(test_entities):
            print(f"\n实体 {i} ({entity['type']}):")
            print(f"  拉伸方向: {entity['extrusion_direction']}")
            
            coord_system = detector.detect_coordinate_system(entity)
            print(f"  检测到坐标系: {coord_system.value}")
            
            if entity['type'] == 'ARC':
                corrected = detector.correct_arc_angles_by_handedness(entity)
                if 'coordinate_system_correction' in corrected:
                    correction_info = corrected['coordinate_system_correction']
                    if correction_info.get('correction_needed', True):
                        print(f"  原始角度: {correction_info['original_angles']}")
                        print(f"  校正角度: {correction_info['corrected_angles']}")
                    else:
                        print(f"  无需校正")
            
            elif entity['type'] == 'ELLIPSE':
                corrected = detector.correct_ellipse_params_by_handedness(entity)
                if 'coordinate_system_correction' in corrected:
                    correction_info = corrected['coordinate_system_correction']
                    if correction_info.get('correction_needed', True):
                        print(f"  原始参数: {correction_info['original_params']}")
                        print(f"  校正参数: {correction_info['corrected_params']}")
                    else:
                        print(f"  无需校正")
        
        print("\n🎨 多解决方案对比测试:")
        
        # 使用所有解决方案处理
        solutions = reconstructor.reconstruct_with_all_solutions(test_entities)
        
        for solution_name, entities in solutions.items():
            handedness_corrected_count = 0
            for entity in entities:
                if entity.get('reconstruction_method') == 'handedness_correction':
                    handedness_corrected_count += 1
            
            print(f"  {solution_name}: {len(entities)} 个实体")
            if solution_name == "手性校正法":
                print(f"    手性校正实体: {handedness_corrected_count}")
        
        # 创建可视化对比
        create_handedness_comparison(solutions, test_entities)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_handedness_comparison(solutions, original_entities):
    """创建手性校正对比图"""
    try:
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        fig.suptitle('坐标系手性校正对比 - 基于DXF拉伸方向', fontsize=16)
        
        solution_names = ["原始显示", "角度修正法", "坐标变换法", "几何重建法",
                         "采样定向法", "用户建议法", "手性校正法", "原理说明"]
        
        for i, (ax, solution_name) in enumerate(zip(axes.flat, solution_names)):
            ax.set_title(solution_name, fontsize=12)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            if solution_name == "原理说明":
                # 在最后一个子图显示原理说明
                ax.text(0.1, 0.9, '坐标系手性检测原理:', fontsize=12, weight='bold', 
                       transform=ax.transAxes)
                ax.text(0.1, 0.8, '• 拉伸方向 (0,0,1): 右手系 (RHS)', fontsize=10, 
                       transform=ax.transAxes)
                ax.text(0.1, 0.7, '• 拉伸方向 (0,0,-1): 左手系 (LHS)', fontsize=10, 
                       transform=ax.transAxes)
                ax.text(0.1, 0.6, '• RHS: 角度逆时针为正', fontsize=10, 
                       transform=ax.transAxes)
                ax.text(0.1, 0.5, '• LHS: 角度顺时针为正', fontsize=10, 
                       transform=ax.transAxes)
                ax.text(0.1, 0.4, '校正公式:', fontsize=12, weight='bold', 
                       transform=ax.transAxes)
                ax.text(0.1, 0.3, '• start_corrected = 360° - end_original', fontsize=10, 
                       transform=ax.transAxes)
                ax.text(0.1, 0.2, '• end_corrected = 360° - start_original', fontsize=10, 
                       transform=ax.transAxes)
                ax.text(0.1, 0.1, '• 同时交换起点和终点', fontsize=10, 
                       transform=ax.transAxes)
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                continue
            
            entities = solutions.get(solution_name, [])
            draw_entities_with_handedness_info(entities, ax, original_entities)
        
        # 调整布局
        plt.tight_layout()
        plt.show()
        
        print("📊 手性校正对比图已显示")
        print("\n💡 观察要点:")
        print("  1. 手性校正法应该正确处理LHS实体")
        print("  2. 对比不同方案对镜像圆弧的处理效果")
        print("  3. 注意拉伸方向为(0,0,-1)的实体校正结果")
        
    except Exception as e:
        print(f"⚠️ 创建对比图失败: {e}")

def draw_entities_with_handedness_info(entities, ax, original_entities):
    """绘制实体并显示手性信息"""
    for i, entity in enumerate(entities):
        try:
            entity_type = entity.get('type', 'UNKNOWN')
            color = entity.get('color', 'black')
            
            if entity_type == 'POLYLINE':
                # 多段线 - 重构结果
                points = entity['points']
                if len(points) >= 2:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    
                    # 使用粗线显示重构结果
                    ax.plot(x_coords, y_coords, color=color, linewidth=3, alpha=0.8)
                    
                    # 标记端点
                    if len(x_coords) > 0:
                        ax.scatter([x_coords[0]], [y_coords[0]], color='green', s=40, 
                                 marker='o', edgecolor='darkgreen', linewidth=2, zorder=10)
                        ax.scatter([x_coords[-1]], [y_coords[-1]], color='red', s=40, 
                                 marker='s', edgecolor='darkred', linewidth=2, zorder=10)
                    
                    # 显示手性校正信息
                    if 'coordinate_system_correction' in entity:
                        correction_info = entity['coordinate_system_correction']
                        if i < len(original_entities):
                            original_entity = original_entities[i]
                            extrusion_dir = original_entity.get('extrusion_direction', (0, 0, 1))
                            
                            # 在图形旁边显示拉伸方向信息
                            center_x = sum(x_coords) / len(x_coords)
                            center_y = sum(y_coords) / len(y_coords)
                            
                            z_component = extrusion_dir[2]
                            coord_type = "RHS" if z_component >= 0 else "LHS"
                            
                            ax.text(center_x + 10, center_y, f'Z={z_component:.1f}\n{coord_type}', 
                                   fontsize=8, ha='left', va='center',
                                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
            
            elif entity_type in ['ARC', 'ELLIPSE']:
                # 原生对象（可能在原始显示中）
                if entity_type == 'ARC':
                    center = entity['center']
                    radius = entity['radius']
                    
                    # 绘制完整圆作为参考（虚线）
                    circle = Circle(center, radius, fill=False, edgecolor=color, 
                                   linewidth=1, linestyle='--', alpha=0.5)
                    ax.add_patch(circle)
                    
                    # 显示拉伸方向信息
                    extrusion_dir = entity.get('extrusion_direction', (0, 0, 1))
                    z_component = extrusion_dir[2]
                    coord_type = "RHS" if z_component >= 0 else "LHS"
                    
                    ax.text(center[0], center[1], f'Z={z_component:.1f}\n{coord_type}', 
                           ha='center', va='center', fontsize=8, color='blue', weight='bold')
        
        except Exception as e:
            print(f"⚠️ 绘制实体失败: {entity_type}, 错误: {e}")

def main():
    """主函数"""
    print("=" * 70)
    print("🧪 坐标系手性校正功能测试")
    print("🎯 基于DXF拉伸方向的专业解决方案")
    print("=" * 70)
    
    success = test_handedness_correction()
    
    if success:
        print("\n🎉 测试完成！")
        print("\n💡 手性校正法特点:")
        print("  1. 基于DXF规范的拉伸方向(210,220,230)检测")
        print("  2. 自动识别右手系(RHS)和左手系(LHS)")
        print("  3. 使用专业的角度校正公式")
        print("  4. 正确处理镜像操作导致的坐标系变化")
        print("\n🔍 技术原理:")
        print("  • 拉伸方向Z分量 > 0: 右手坐标系，逆时针为正")
        print("  • 拉伸方向Z分量 < 0: 左手坐标系，顺时针为正")
        print("  • 校正公式: start_new = 360° - end_old")
        print("  •           end_new = 360° - start_old")
        print("\n🚀 现在运行 dxf_display_test.py 体验手性校正法")
    else:
        print("\n❌ 测试失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
