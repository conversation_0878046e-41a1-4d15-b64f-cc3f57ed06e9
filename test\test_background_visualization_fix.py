#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试后台处理时界面更新修复
验证在处理多个文件时，只有第一个文件（显示文件）会更新界面
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_background_visualization_fix():
    """测试后台处理时的界面更新修复"""
    print("=" * 60)
    print("测试后台处理时界面更新修复")
    print("=" * 60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建模拟的状态回调对象
        class MockStatusCallback:
            def __init__(self):
                self.display_file = "file1.dwg"  # 设置显示文件
                self.visualization_updates = []
                self.is_display_file = False  # 默认为后台文件

            def _is_processing_display_file(self):
                """模拟检查是否在处理显示文件"""
                return self.is_display_file

            def __call__(self, status_type, data):
                """状态回调"""
                print(f"状态回调: {status_type}")

        # 创建模拟的UI对象，包含状态回调
        class MockUI:
            def __init__(self):
                self.display_file = "file1.dwg"
                self.is_display_file = False  # 默认为后台文件

            def _is_processing_display_file(self):
                """模拟检查是否在处理显示文件"""
                return self.is_display_file

            def on_status_update(self, status_type, data):
                """状态回调"""
                print(f"状态回调: {status_type}")
        
        # 创建处理器
        processor = EnhancedCADProcessor()
        ui = MockUI()
        processor.status_callback = ui.on_status_update
        
        # 模拟一些数据
        processor.current_file_entities = [
            {'type': 'LINE', 'layer': 'test', 'points': [(0, 0), (10, 10)]}
        ]
        processor.auto_labeled_entities = []
        processor.labeled_entities = []
        processor.all_groups = [[
            {'type': 'LINE', 'layer': 'test', 'points': [(0, 0), (10, 10)]}
        ]]
        processor.pending_manual_groups = processor.all_groups.copy()
        processor.current_manual_group_index = 0
        
        # 创建模拟的可视化器
        class MockVisualizer:
            def __init__(self):
                self.update_count = 0
                
            def visualize_entity_group(self, group, mapping):
                self.update_count += 1
                print(f"详细视图更新 #{self.update_count}")
                
            def visualize_overview(self, entities, current_group, labeled_entities, **kwargs):
                self.update_count += 1
                print(f"全图概览更新 #{self.update_count}")
                
            def update_canvas(self, canvas):
                print("画布更新")
        
        class MockCanvas:
            pass
        
        processor.visualizer = MockVisualizer()
        processor.canvas = MockCanvas()
        
        print("\n1. 测试 _show_group 方法（应该跳过界面更新）")
        print("-" * 40)
        initial_count = processor.visualizer.update_count
        processor._show_group(processor.all_groups[0], 1)
        final_count = processor.visualizer.update_count
        
        if final_count == initial_count:
            print("✅ _show_group 正确跳过了后台文件的界面更新")
        else:
            print(f"❌ _show_group 仍然更新了界面 (更新次数: {final_count - initial_count})")
        
        print("\n2. 测试 _show_next_manual_group 方法（应该跳过界面更新）")
        print("-" * 40)
        initial_count = processor.visualizer.update_count
        processor._show_next_manual_group()
        final_count = processor.visualizer.update_count
        
        if final_count == initial_count:
            print("✅ _show_next_manual_group 正确跳过了后台文件的界面更新")
        else:
            print(f"❌ _show_next_manual_group 仍然更新了界面 (更新次数: {final_count - initial_count})")
        
        print("\n3. 测试自动标注后的可视化更新（应该跳过界面更新）")
        print("-" * 40)
        initial_count = processor.visualizer.update_count
        # 模拟自动标注完成，这会触发可视化更新
        processor.auto_labeled_entities = [{'type': 'LINE', 'layer': 'wall', 'label': 'wall'}]
        # 直接调用自动标注后的可视化更新逻辑
        should_skip_visualization = getattr(processor, '_is_background_processing', False)

        # 如果有状态回调，尝试检查是否在处理显示文件
        if not should_skip_visualization and hasattr(processor, 'status_callback') and processor.status_callback:
            # 检查回调是否是绑定方法，并且有_is_processing_display_file方法
            if hasattr(processor.status_callback, '__self__') and hasattr(processor.status_callback.__self__, '_is_processing_display_file'):
                should_skip_visualization = not processor.status_callback.__self__._is_processing_display_file()
            # 如果回调本身有_is_processing_display_file方法
            elif hasattr(processor.status_callback, '_is_processing_display_file'):
                should_skip_visualization = not processor.status_callback._is_processing_display_file()

        if should_skip_visualization:
            print("✅ 自动标注可视化更新正确跳过了后台文件")
        else:
            print("❌ 自动标注可视化更新没有正确跳过后台文件")
        
        print("\n4. 测试显示文件的情况（应该正常更新界面）")
        print("-" * 40)
        # 修改回调，模拟处理显示文件
        ui.is_display_file = True
        
        initial_count = processor.visualizer.update_count
        processor._show_group(processor.all_groups[0], 1)
        final_count = processor.visualizer.update_count
        
        if final_count > initial_count:
            print(f"✅ 显示文件正确更新了界面 (更新次数: {final_count - initial_count})")
        else:
            print("❌ 显示文件没有更新界面")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_background_visualization_fix()
