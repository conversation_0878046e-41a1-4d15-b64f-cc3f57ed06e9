#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试手动填充修复
验证：
1. 墙体组列表显示所有组（无论是否能识别轮廓）
2. 轮廓识别状态标注
3. 恢复高级轮廓识别逻辑
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_manual_fill_fixes():
    """测试手动填充修复"""
    print("=" * 80)
    print("🧪 测试手动填充修复")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from interactive_wall_fill_window import InteractiveWallFillWindow
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        print("✅ 成功导入必要模块")
        
        # 检查关键方法是否存在
        key_methods = [
            '_identify_all_wall_groups',
            '_try_advanced_fill_group',
            '_get_recognition_status',
            '_can_recognize_contour',
            '_simple_fill_logic_for_group'
        ]
        
        print("\n📋 检查关键方法:")
        for method in key_methods:
            if hasattr(InteractiveWallFillWindow, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - 缺失")
                return False
        
        # 检查墙体填充处理器的高级方法
        processor_methods = [
            '_identify_outer_contour_improved',
            'identify_wall_entities',
            'group_wall_entities'
        ]
        
        print("\n📋 检查墙体填充处理器方法:")
        for method in processor_methods:
            if hasattr(EnhancedWallFillProcessorV2, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - 缺失")
                return False
        
        # 测试轮廓识别状态检测
        success = test_recognition_status_detection()
        if not success:
            return False
        
        # 测试墙体组识别
        success = test_wall_group_identification()
        if not success:
            return False
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_recognition_status_detection():
    """测试轮廓识别状态检测"""
    print("\n" + "=" * 60)
    print("🔍 测试轮廓识别状态检测")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from interactive_wall_fill_window import InteractiveWallFillWindow
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        # 创建测试数据
        test_entities = [
            {
                'type': 'LINE',
                'points': [(0, 0), (100, 0)],
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(100, 0), (100, 100)],
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(100, 100), (0, 100)],
                'layer': 'WALL'
            },
            {
                'type': 'LINE',
                'points': [(0, 100), (0, 0)],
                'layer': 'WALL'
            }
        ]
        
        processor = EnhancedWallFillProcessorV2()
        
        # 创建交互式窗口实例（但不显示）
        window = InteractiveWallFillWindow(root, test_entities, processor)
        
        # 测试轮廓识别状态检测
        test_group = test_entities
        
        print("🔧 测试轮廓识别状态检测...")
        
        # 测试可识别的组
        can_recognize = window._can_recognize_contour(test_group)
        print(f"   完整矩形组识别状态: {'可识别' if can_recognize else '无法识别'}")
        
        if not can_recognize:
            print("❌ 完整矩形组应该可以识别")
            root.destroy()
            return False
        
        # 测试不完整的组
        incomplete_group = test_entities[:2]  # 只有两条线
        can_recognize_incomplete = window._can_recognize_contour(incomplete_group)
        print(f"   不完整组识别状态: {'可识别' if can_recognize_incomplete else '无法识别'}")
        
        # 测试状态获取
        status_complete = window._get_recognition_status(0, test_group, '未填充')
        status_incomplete = window._get_recognition_status(1, incomplete_group, '未填充')
        
        print(f"   完整组状态: {status_complete}")
        print(f"   不完整组状态: {status_incomplete}")
        
        root.destroy()
        print("✅ 轮廓识别状态检测测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 轮廓识别状态检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wall_group_identification():
    """测试墙体组识别"""
    print("\n" + "=" * 60)
    print("🏗️ 测试墙体组识别")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from interactive_wall_fill_window import InteractiveWallFillWindow
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        # 创建测试数据（两个独立的矩形组）
        test_entities = [
            # 第一个矩形组
            {'type': 'LINE', 'points': [(0, 0), (100, 0)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 0), (100, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 100), (0, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(0, 100), (0, 0)], 'layer': 'WALL'},
            
            # 第二个矩形组（有间隙）
            {'type': 'LINE', 'points': [(200, 0), (300, 0)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(300, 0), (300, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(300, 100), (200, 100)], 'layer': 'WALL'},
            # 缺少一条边，模拟识别困难的情况
            
            # 第三个不完整组
            {'type': 'LINE', 'points': [(400, 0), (500, 0)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(500, 0), (500, 50)], 'layer': 'WALL'},
        ]
        
        processor = EnhancedWallFillProcessorV2()
        
        # 创建交互式窗口实例
        window = InteractiveWallFillWindow(root, test_entities, processor)
        
        print("🔧 测试墙体组识别...")
        
        # 检查是否识别到墙体组
        if hasattr(window, 'wall_groups') and window.wall_groups:
            print(f"✅ 识别到 {len(window.wall_groups)} 个墙体组")
            
            # 检查每个组的状态
            for i, group in enumerate(window.wall_groups):
                status = window.group_status.get(i, '未知')
                recognition_status = window._get_recognition_status(i, group, status)
                print(f"   组 {i+1}: {len(group)} 个实体, 填充状态: {status}, 识别状态: {recognition_status}")
        else:
            print("❌ 未识别到墙体组")
            root.destroy()
            return False
        
        # 测试高级填充方法
        print("\n🔧 测试高级填充方法...")
        if window.wall_groups:
            test_group = window.wall_groups[0]
            result = window._try_advanced_fill_group(0, test_group)
            
            if result:
                print(f"✅ 高级填充成功，面积: {result.area:.2f}")
            else:
                print("⚠️ 高级填充失败，但这可能是正常的")
        
        root.destroy()
        print("✅ 墙体组识别测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 墙体组识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_improvements():
    """测试UI改进"""
    print("\n" + "=" * 60)
    print("🎨 测试UI改进")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from interactive_wall_fill_window import InteractiveWallFillWindow
        from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        test_entities = [
            {'type': 'LINE', 'points': [(0, 0), (100, 0)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 0), (100, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(100, 100), (0, 100)], 'layer': 'WALL'},
            {'type': 'LINE', 'points': [(0, 100), (0, 0)], 'layer': 'WALL'},
        ]
        
        processor = EnhancedWallFillProcessorV2()
        window = InteractiveWallFillWindow(root, test_entities, processor)
        
        print("🔧 检查UI组件...")
        
        # 检查TreeView列
        columns = window.group_tree['columns']
        expected_columns = ('序号', '状态', '实体数', '识别状态', '操作')
        
        if columns == expected_columns:
            print("✅ TreeView列配置正确")
        else:
            print(f"❌ TreeView列配置错误: 期望 {expected_columns}, 实际 {columns}")
            root.destroy()
            return False
        
        # 检查列标题
        headings = {}
        for col in columns:
            headings[col] = window.group_tree.heading(col)['text']
        
        expected_headings = {
            '序号': '序号',
            '状态': '填充状态', 
            '实体数': '实体数',
            '识别状态': '轮廓识别',
            '操作': '操作'
        }
        
        for col, expected_text in expected_headings.items():
            if headings.get(col) == expected_text:
                print(f"✅ 列标题 '{col}' 正确: {expected_text}")
            else:
                print(f"❌ 列标题 '{col}' 错误: 期望 '{expected_text}', 实际 '{headings.get(col)}'")
                root.destroy()
                return False
        
        root.destroy()
        print("✅ UI改进测试通过")
        return True
        
    except Exception as e:
        print(f"❌ UI改进测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始手动填充修复测试...")
    
    # 执行所有测试
    tests = [
        ("基本功能测试", test_manual_fill_fixes),
        ("UI改进测试", test_ui_improvements)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！手动填充修复已成功实现。")
        print("\n📝 修复内容:")
        print("1. ✅ 墙体组列表显示所有组（无论是否能识别轮廓）")
        print("2. ✅ 增加轮廓识别状态标注列")
        print("3. ✅ 恢复高级轮廓识别逻辑")
        print("4. ✅ 改进重新填充功能")
        print("5. ✅ 增强状态检测和显示")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    print("=" * 80)
