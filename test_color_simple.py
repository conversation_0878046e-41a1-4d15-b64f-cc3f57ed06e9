#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的配色方案测试
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_color_scheme_logic():
    """测试配色方案逻辑"""
    print("🚀 开始简单配色方案测试...")
    
    try:
        # 导入应用
        print("📦 导入应用模块...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("✅ 应用模块导入成功")
        
        # 创建一个模拟的配色方案
        test_scheme_1 = {
            'wall': '#FF0000',      # 红色
            'door_window': '#0000FF', # 蓝色
            'furniture': '#00FF00',   # 绿色
            'sofa': '#800080',       # 紫色
            'unlabeled': '#808080',  # 灰色
            'background': '#FFFFFF', # 白色背景
            'highlight': '#FFFF00'   # 黄色高亮
        }
        
        test_scheme_2 = {
            'wall': '#8B4513',      # 棕色
            'door_window': '#FF6347', # 番茄红
            'furniture': '#32CD32',   # 酸橙绿
            'sofa': '#9370DB',       # 中紫色
            'unlabeled': '#A9A9A9',  # 深灰色
            'background': '#F0F8FF', # 爱丽丝蓝背景
            'highlight': '#FFD700'   # 金色高亮
        }
        
        print("🎨 配色方案创建完成")
        print(f"   方案1: {test_scheme_1}")
        print(f"   方案2: {test_scheme_2}")
        
        # 创建模拟实体
        test_entity_wall = {'label': 'wall', 'type': 'LINE', 'layer': 'WALL'}
        test_entity_door = {'label': 'door_window', 'type': 'LINE', 'layer': 'DOOR'}
        test_entity_furniture = {'label': 'furniture', 'type': 'LWPOLYLINE', 'layer': 'FURNITURE'}
        test_entity_sofa = {'label': 'sofa', 'type': 'LWPOLYLINE', 'layer': 'SOFA'}
        
        print("🔧 测试实体创建完成")
        
        # 模拟颜色获取逻辑
        def get_color_from_scheme(entity, scheme):
            """模拟颜色获取"""
            label = entity.get('label', 'unlabeled')
            return scheme.get(label, scheme.get('other', '#808080'))
        
        # 测试颜色获取
        entities = [
            ('wall', test_entity_wall),
            ('door_window', test_entity_door),
            ('furniture', test_entity_furniture),
            ('sofa', test_entity_sofa)
        ]
        
        print("\n📊 颜色变化测试:")
        print("="*50)
        
        for name, entity in entities:
            color1 = get_color_from_scheme(entity, test_scheme_1)
            color2 = get_color_from_scheme(entity, test_scheme_2)
            
            print(f"{name}:")
            print(f"   方案1: {color1}")
            print(f"   方案2: {color2}")
            
            if color1 != color2:
                print(f"   ✅ 颜色已改变: {color1} → {color2}")
            else:
                print(f"   ❌ 颜色未改变")
            print()
        
        print("🎉 简单配色方案测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_color_method():
    """测试应用的颜色方法"""
    print("\n🔍 测试应用的颜色方法...")
    
    try:
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建最小化的应用实例（不显示界面）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        print("🔄 创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("✅ 应用实例创建成功")
        
        # 检查颜色方法是否存在
        if hasattr(app, '_get_entity_color_from_scheme_enhanced'):
            print("✅ _get_entity_color_from_scheme_enhanced 方法存在")
        else:
            print("❌ _get_entity_color_from_scheme_enhanced 方法不存在")
        
        # 检查配色方案属性
        if hasattr(app, 'current_color_scheme'):
            print(f"✅ current_color_scheme 存在: {app.current_color_scheme}")
        else:
            print("❌ current_color_scheme 不存在")
        
        if hasattr(app, 'color_scheme'):
            print(f"✅ color_scheme 存在: {getattr(app, 'color_scheme', None)}")
        else:
            print("❌ color_scheme 不存在")
        
        # 测试颜色获取
        if hasattr(app, '_get_entity_color_from_scheme_enhanced') and hasattr(app, 'current_color_scheme'):
            test_entity = {'label': 'wall', 'type': 'LINE', 'layer': 'WALL'}
            
            print("\n🎨 测试颜色获取...")
            try:
                color = app._get_entity_color_from_scheme_enhanced(test_entity)
                print(f"✅ 墙体颜色: {color}")
            except Exception as e:
                print(f"❌ 颜色获取失败: {e}")
        
        # 测试配色方案设置
        print("\n🔧 测试配色方案设置...")
        new_scheme = {
            'wall': '#FF0000',
            'door_window': '#0000FF',
            'furniture': '#00FF00',
            'sofa': '#800080'
        }
        
        # 设置新配色方案
        app.current_color_scheme.update(new_scheme)
        print("✅ 配色方案设置完成")
        
        # 再次测试颜色获取
        if hasattr(app, '_get_entity_color_from_scheme_enhanced'):
            test_entity = {'label': 'wall', 'type': 'LINE', 'layer': 'WALL'}
            try:
                color = app._get_entity_color_from_scheme_enhanced(test_entity)
                print(f"✅ 更新后墙体颜色: {color}")
            except Exception as e:
                print(f"❌ 更新后颜色获取失败: {e}")
        
        root.destroy()
        print("✅ 应用颜色方法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 应用颜色方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始配色方案测试...")
    print("="*80)
    
    # 测试1: 简单配色逻辑
    success1 = test_color_scheme_logic()
    
    # 测试2: 应用颜色方法
    success2 = test_app_color_method()
    
    print("\n" + "="*80)
    print("📊 测试结果总结:")
    print(f"   简单配色逻辑测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"   应用颜色方法测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")

if __name__ == "__main__":
    main()
