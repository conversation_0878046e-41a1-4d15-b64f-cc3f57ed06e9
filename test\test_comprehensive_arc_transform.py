#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的圆弧变换测试 - 多种变换组合（多方式镜像，多角度旋转）
"""

import sys
import os
import math

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import TransformationMatrix, CADDataProcessor

def create_test_arc(center_x=0, center_y=0, radius=1.0, start_angle=0, end_angle=90):
    """创建测试圆弧"""
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    class MockDxf:
        def __init__(self):
            self.center = MockPoint(center_x, center_y)
            self.radius = radius
            self.start_angle = start_angle
            self.end_angle = end_angle
    
    class MockDoc:
        def __init__(self):
            self.header = {'$ANGDIR': 0}  # 逆时针
    
    class MockArc:
        def __init__(self):
            self.dxf = MockDxf()
            self.doc = MockDoc()
    
    return MockArc()

def test_comprehensive_transformations():
    """测试全面的变换组合"""
    print("=== 全面变换测试 ===")
    
    processor = CADDataProcessor()
    
    # 测试不同的圆弧
    test_arcs = [
        {'name': '第一象限圆弧', 'arc': create_test_arc(0, 0, 1, 0, 90)},
        {'name': '第二象限圆弧', 'arc': create_test_arc(0, 0, 1, 90, 180)},
        {'name': '半圆弧', 'arc': create_test_arc(0, 0, 1, 0, 180)},
        {'name': '大圆弧', 'arc': create_test_arc(0, 0, 1, 45, 315)},
    ]
    
    # 测试变换组合
    transformations = [
        # 基本变换
        {'name': '无变换', 'rotation': 0, 'scale': (1.0, 1.0)},
        
        # 纯旋转
        {'name': '旋转45度', 'rotation': 45, 'scale': (1.0, 1.0)},
        {'name': '旋转90度', 'rotation': 90, 'scale': (1.0, 1.0)},
        {'name': '旋转180度', 'rotation': 180, 'scale': (1.0, 1.0)},
        {'name': '旋转270度', 'rotation': 270, 'scale': (1.0, 1.0)},
        
        # 纯镜像
        {'name': 'X轴镜像', 'rotation': 0, 'scale': (1.0, -1.0)},
        {'name': 'Y轴镜像', 'rotation': 0, 'scale': (-1.0, 1.0)},
        {'name': '双轴镜像', 'rotation': 0, 'scale': (-1.0, -1.0)},
        
        # 镜像+旋转组合
        {'name': 'X轴镜像+45度旋转', 'rotation': 45, 'scale': (1.0, -1.0)},
        {'name': 'Y轴镜像+90度旋转', 'rotation': 90, 'scale': (-1.0, 1.0)},
        {'name': '双轴镜像+180度旋转', 'rotation': 180, 'scale': (-1.0, -1.0)},
        
        # 缩放变换
        {'name': '均匀缩放2倍', 'rotation': 0, 'scale': (2.0, 2.0)},
        {'name': '非均匀缩放', 'rotation': 0, 'scale': (2.0, 0.5)},
        {'name': '镜像+缩放', 'rotation': 0, 'scale': (-2.0, 1.5)},
    ]
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    print(f"测试 {len(test_arcs)} 种圆弧 × {len(transformations)} 种变换 = {len(test_arcs) * len(transformations)} 个组合")
    print("=" * 80)
    
    success_count = 0
    total_count = 0
    
    for arc_info in test_arcs:
        print(f"\n🔵 {arc_info['name']}")
        arc = arc_info['arc']
        
        for transform in transformations:
            total_count += 1
            print(f"\n  📐 {transform['name']}")
            
            try:
                matrix = TransformationMatrix(base_point, transform['rotation'], transform['scale'])
                result = processor._transform_arc(arc, matrix)
                
                # 验证结果
                is_valid = True
                error_msgs = []
                
                # 检查半径是否为正
                if result['radius'] <= 0:
                    is_valid = False
                    error_msgs.append("半径为负或零")
                
                # 检查角度范围
                if result['start_angle'] < 0 or result['end_angle'] < 0:
                    error_msgs.append("角度为负")
                
                # 检查角度差是否合理
                angle_diff = (result['end_angle'] - result['start_angle']) % 360
                original_diff = (arc.dxf.end_angle - arc.dxf.start_angle) % 360
                if abs(angle_diff - original_diff) > 1:  # 允许1度误差
                    error_msgs.append(f"角度差异过大: 原始{original_diff:.1f}° vs 变换后{angle_diff:.1f}°")
                
                if is_valid:
                    success_count += 1
                    print(f"    ✅ 成功: 中心({result['center'][0]:.2f}, {result['center'][1]:.2f}), "
                          f"半径={result['radius']:.2f}, 角度={result['start_angle']:.1f}°-{result['end_angle']:.1f}°")
                    if matrix.is_mirrored():
                        print(f"    🪞 镜像变换已检测并处理")
                else:
                    print(f"    ❌ 验证失败: {'; '.join(error_msgs)}")
                    
            except Exception as e:
                print(f"    💥 变换失败: {e}")
    
    print(f"\n" + "=" * 80)
    print(f"测试总结: {success_count}/{total_count} 个变换成功 ({success_count/total_count*100:.1f}%)")
    
    return success_count, total_count

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===")
    
    processor = CADDataProcessor()
    
    edge_cases = [
        {'name': '完整圆(0-360度)', 'arc': create_test_arc(0, 0, 1, 0, 360)},
        {'name': '微小圆弧(1度)', 'arc': create_test_arc(0, 0, 1, 0, 1)},
        {'name': '跨越0度圆弧', 'arc': create_test_arc(0, 0, 1, 350, 10)},
        {'name': '大半径圆弧', 'arc': create_test_arc(0, 0, 100, 0, 90)},
        {'name': '小半径圆弧', 'arc': create_test_arc(0, 0, 0.01, 0, 90)},
    ]
    
    class MockPoint:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    base_point = MockPoint(0, 0)
    
    for case in edge_cases:
        print(f"\n🔸 {case['name']}")
        arc = case['arc']
        
        # 测试镜像变换
        try:
            matrix = TransformationMatrix(base_point, 0, (1.0, -1.0))
            result = processor._transform_arc(arc, matrix)
            print(f"  ✅ X轴镜像成功: 角度={result['start_angle']:.1f}°-{result['end_angle']:.1f}°")
        except Exception as e:
            print(f"  ❌ X轴镜像失败: {e}")

def main():
    """主函数"""
    print("全面圆弧变换测试")
    print("=" * 50)
    print("测试目标: 验证变换矩阵在多种变换组合下的正确性")
    print("包括: 多方式镜像、多角度旋转、缩放等组合变换")
    print()
    
    try:
        # 全面变换测试
        success, total = test_comprehensive_transformations()
        
        # 边界情况测试
        test_edge_cases()
        
        print(f"\n🎉 测试完成！")
        print(f"成功率: {success}/{total} ({success/total*100:.1f}%)")
        
        if success == total:
            print("✅ 所有测试通过！变换矩阵功能正常。")
        else:
            print("⚠️ 部分测试失败，需要进一步优化。")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
