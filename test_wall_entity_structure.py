#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试墙体实体数据结构
检查为什么无法提取墙体线段坐标
"""

def test_wall_entity_structure():
    """测试墙体实体数据结构"""
    print("🚀 测试墙体实体数据结构...")
    print("="*80)
    
    try:
        import sys
        import os
        
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 尝试导入主应用来获取实际数据
        try:
            from main_enhanced_with_v2_fill import EnhancedCADAppV2
            print("✅ 成功导入主应用")
        except ImportError as e:
            print(f"❌ 无法导入主应用: {e}")
            return False
        
        # 模拟可能的实体数据结构
        print("\n🔍 分析可能的实体数据结构...")
        
        # 根据CAD处理经验，可能的实体结构
        possible_structures = [
            # 结构1: 标准LINE实体
            {
                'type': 'LINE',
                'start': {'x': 100, 'y': 200},
                'end': {'x': 300, 'y': 200},
                'label': '墙'
            },
            
            # 结构2: 扁平化坐标
            {
                'type': 'LINE',
                'start_x': 100,
                'start_y': 200,
                'end_x': 300,
                'end_y': 200,
                'label': '墙'
            },
            
            # 结构3: 坐标数组
            {
                'type': 'LINE',
                'coordinates': [(100, 200), (300, 200)],
                'label': '墙'
            },
            
            # 结构4: 几何对象
            {
                'type': 'LINE',
                'geometry': {
                    'type': 'LineString',
                    'coordinates': [[100, 200], [300, 200]]
                },
                'label': '墙'
            },
            
            # 结构5: POLYLINE实体
            {
                'type': 'LWPOLYLINE',
                'points': [
                    {'x': 100, 'y': 200},
                    {'x': 300, 'y': 200},
                    {'x': 300, 'y': 400}
                ],
                'label': '墙'
            },
            
            # 结构6: 简化点数组
            {
                'type': 'POLYLINE',
                'points': [(100, 200), (300, 200), (300, 400)],
                'label': '墙'
            },
        ]
        
        # 测试提取逻辑
        def test_extract_line_coordinates(entity):
            """测试线段坐标提取"""
            try:
                entity_type = entity.get('type', '')
                print(f"   测试实体类型: {entity_type}")
                
                if entity_type == 'LINE':
                    # 方法1: 标准结构
                    start = entity.get('start', {})
                    end = entity.get('end', {})
                    if start and end and isinstance(start, dict) and isinstance(end, dict):
                        coords = [(start.get('x', 0), start.get('y', 0)), 
                                 (end.get('x', 0), end.get('y', 0))]
                        print(f"      方法1成功: {coords}")
                        return [coords]
                    
                    # 方法2: 扁平化坐标
                    if 'start_x' in entity and 'start_y' in entity and 'end_x' in entity and 'end_y' in entity:
                        coords = [(entity['start_x'], entity['start_y']), 
                                 (entity['end_x'], entity['end_y'])]
                        print(f"      方法2成功: {coords}")
                        return [coords]
                    
                    # 方法3: 坐标数组
                    if 'coordinates' in entity:
                        coords = entity['coordinates']
                        if len(coords) >= 2:
                            print(f"      方法3成功: {coords}")
                            return [coords]
                    
                    # 方法4: 几何对象
                    if 'geometry' in entity:
                        geom = entity['geometry']
                        if isinstance(geom, dict) and 'coordinates' in geom:
                            coords = geom['coordinates']
                            if len(coords) >= 2:
                                print(f"      方法4成功: {coords}")
                                return [coords]
                
                elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                    points = entity.get('points', [])
                    if points:
                        # 方法1: 字典点
                        if isinstance(points[0], dict):
                            coords = [(p.get('x', 0), p.get('y', 0)) for p in points]
                            lines = []
                            for i in range(len(coords) - 1):
                                lines.append([coords[i], coords[i + 1]])
                            print(f"      POLY方法1成功: {len(lines)} 条线段")
                            return lines
                        
                        # 方法2: 元组点
                        elif isinstance(points[0], (tuple, list)):
                            coords = points
                            lines = []
                            for i in range(len(coords) - 1):
                                lines.append([coords[i], coords[i + 1]])
                            print(f"      POLY方法2成功: {len(lines)} 条线段")
                            return lines
                
                print(f"      ❌ 所有方法都失败")
                return []
                
            except Exception as e:
                print(f"      ❌ 提取失败: {e}")
                return []
        
        print(f"\n📊 测试各种实体结构:")
        success_count = 0
        
        for i, entity in enumerate(possible_structures):
            print(f"\n结构 {i+1}: {entity.get('type', 'UNKNOWN')}")
            result = test_extract_line_coordinates(entity)
            if result:
                print(f"   ✅ 成功提取 {len(result)} 条线段")
                success_count += 1
            else:
                print(f"   ❌ 提取失败")
        
        print(f"\n📊 测试结果: {success_count}/{len(possible_structures)} 成功")
        
        # 建议改进方案
        print(f"\n💡 建议改进方案:")
        print("1. 添加多种坐标提取方法")
        print("2. 支持扁平化坐标结构")
        print("3. 支持几何对象结构")
        print("4. 添加更详细的调试信息")
        print("5. 容错处理各种数据格式")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_improved_extract_method():
    """创建改进的坐标提取方法"""
    print("\n" + "="*80)
    print("🔧 创建改进的坐标提取方法...")
    
    improved_method = '''
def _extract_line_coordinates_improved(self, entity):
    """改进的线段坐标提取方法"""
    try:
        entity_type = entity.get('type', '')
        
        if entity_type == 'LINE':
            # 方法1: 标准结构 {start: {x, y}, end: {x, y}}
            start = entity.get('start', {})
            end = entity.get('end', {})
            if start and end and isinstance(start, dict) and isinstance(end, dict):
                coords = [(start.get('x', 0), start.get('y', 0)), 
                         (end.get('x', 0), end.get('y', 0))]
                return [coords]
            
            # 方法2: 扁平化坐标 {start_x, start_y, end_x, end_y}
            if all(key in entity for key in ['start_x', 'start_y', 'end_x', 'end_y']):
                coords = [(entity['start_x'], entity['start_y']), 
                         (entity['end_x'], entity['end_y'])]
                return [coords]
            
            # 方法3: 坐标数组 {coordinates: [(x1,y1), (x2,y2)]}
            if 'coordinates' in entity:
                coords = entity['coordinates']
                if len(coords) >= 2:
                    return [coords]
            
            # 方法4: 几何对象 {geometry: {coordinates: [[x1,y1], [x2,y2]]}}
            if 'geometry' in entity:
                geom = entity['geometry']
                if isinstance(geom, dict) and 'coordinates' in geom:
                    coords = geom['coordinates']
                    if len(coords) >= 2:
                        return [coords]
        
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            points = entity.get('points', [])
            if not points:
                return []
            
            # 方法1: 字典点 [{x, y}, {x, y}, ...]
            if isinstance(points[0], dict):
                coords = [(p.get('x', 0), p.get('y', 0)) for p in points if isinstance(p, dict)]
            # 方法2: 元组/列表点 [(x, y), (x, y), ...]
            elif isinstance(points[0], (tuple, list)):
                coords = points
            else:
                return []
            
            # 创建连续线段
            if len(coords) >= 2:
                lines = []
                for i in range(len(coords) - 1):
                    lines.append([coords[i], coords[i + 1]])
                return lines
        
        return []
        
    except Exception as e:
        print(f"提取线段坐标失败: {e}")
        return []
    '''
    
    print("✅ 改进方法创建完成")
    print("\n🔧 改进特点:")
    print("1. 支持4种LINE坐标格式")
    print("2. 支持2种POLYLINE点格式") 
    print("3. 完善的错误处理")
    print("4. 详细的调试信息")
    
    return improved_method

def main():
    """主函数"""
    print("🚀 墙体实体数据结构分析")
    print("="*80)
    
    try:
        success1 = test_wall_entity_structure()
        improved_method = create_improved_extract_method()
        
        print("\n" + "="*80)
        print("📊 分析总结:")
        
        if success1:
            print("🎉 实体结构分析完成！")
            print("\n💡 发现的问题:")
            print("   1. 当前方法只支持标准字典结构")
            print("   2. 实际数据可能使用扁平化坐标")
            print("   3. 需要支持多种数据格式")
            print("   4. 缺少详细的调试信息")
            
            print("\n🔧 建议修复:")
            print("   1. 使用改进的坐标提取方法")
            print("   2. 添加多种格式支持")
            print("   3. 增强错误处理和调试")
            print("   4. 测试实际数据结构")
        else:
            print("❌ 实体结构分析失败")
            print("💡 请检查数据格式")
        
        return success1
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
