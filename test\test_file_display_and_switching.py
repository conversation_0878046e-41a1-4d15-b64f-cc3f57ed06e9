#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件显示和切换修复效果
"""

import sys
import os
import tempfile
import tkinter as tk
from unittest.mock import Mock

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_scenario():
    """创建测试场景"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建测试文件夹
    test_folder = os.path.join(temp_dir, "test_cad_folder")
    os.makedirs(test_folder)
    
    # 创建多个CAD文件
    test_files = []
    
    for i in range(3):
        dxf_file = os.path.join(test_folder, f"drawing_{i+1}.dxf")
        with open(dxf_file, 'w') as f:
            f.write(f"""0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
LINE
8
0
10
{i * 10}
20
{i * 10}
30
0.0
11
{i * 10 + 100}
21
{i * 10 + 100}
31
0.0
0
ENDSEC
0
EOF
""")
        test_files.append(dxf_file)
    
    return temp_dir, test_folder, test_files

def test_file_display_logic():
    """测试文件显示逻辑"""
    print("=== 测试文件显示逻辑 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        
        print(f"创建测试环境: {test_folder}")
        print(f"测试文件: {[os.path.basename(f) for f in test_files]}")
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 模拟文件管理初始化
        app.current_folder = test_folder
        app.all_files = test_files
        app.file_status = {
            'drawing_1.dxf': {'processing_status': 'completed', 'annotation_status': 'incomplete'},
            'drawing_2.dxf': {'processing_status': 'processing', 'annotation_status': 'unannotated'},
            'drawing_3.dxf': {'processing_status': 'unprocessed', 'annotation_status': 'unannotated'}
        }
        
        # 创建模拟的文件下拉菜单
        app.file_combo = Mock()
        app.file_combo.get = Mock(return_value="")
        app.file_combo.set = Mock()
        
        # 测试1：设置显示文件为第一个文件
        print(f"\n🔄 测试1: 设置显示文件")
        app.display_file = test_files[0]
        app.current_file = test_files[0]
        
        # 更新文件下拉菜单
        app.update_file_combo()
        
        # 检查文件下拉菜单的值
        if hasattr(app.file_combo, '__setitem__'):
            values = app.file_combo['values']
            print(f"  文件下拉菜单选项:")
            for i, value in enumerate(values):
                print(f"    {i+1}. {value}")
                if value.startswith('['):
                    print(f"      ✅ 当前显示文件: {value}")
        
        print(f"  显示文件: {os.path.basename(app.display_file)}")
        print(f"  当前文件: {os.path.basename(app.current_file)}")
        
        success1 = app.display_file == test_files[0]
        print(f"✅ 测试1结果: {'通过' if success1 else '失败'}")
        
        # 测试2：模拟后台处理其他文件
        print(f"\n🔄 测试2: 模拟后台处理")
        
        # 模拟处理第二个文件（不应该改变显示文件）
        original_display_file = app.display_file
        
        # 更新第二个文件的状态
        app.file_status['drawing_2.dxf']['processing_status'] = 'completed'
        
        # 更新文件下拉菜单
        app.update_file_combo()
        
        # 检查显示文件是否保持不变
        display_unchanged = app.display_file == original_display_file
        print(f"  原始显示文件: {os.path.basename(original_display_file)}")
        print(f"  当前显示文件: {os.path.basename(app.display_file)}")
        print(f"  显示文件保持不变: {display_unchanged}")
        
        success2 = display_unchanged
        print(f"✅ 测试2结果: {'通过' if success2 else '失败'}")
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_switching_logic():
    """测试文件切换逻辑"""
    print("\n=== 测试文件切换逻辑 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试场景
        temp_dir, test_folder, test_files = create_test_scenario()
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 模拟文件管理初始化
        app.current_folder = test_folder
        app.all_files = test_files
        app.file_status = {
            'drawing_1.dxf': {'processing_status': 'completed', 'annotation_status': 'incomplete'},
            'drawing_2.dxf': {'processing_status': 'processing', 'annotation_status': 'unannotated'},
            'drawing_3.dxf': {'processing_status': 'completed', 'annotation_status': 'completed'}
        }
        
        # 创建模拟的文件下拉菜单和状态变量
        app.file_combo = Mock()
        app.file_combo.get = Mock(return_value="[drawing_1.dxf] (已完成, 标注未完成)")
        app.file_combo.set = Mock()
        app.file_combo.__getitem__ = Mock(return_value=[
            "[drawing_1.dxf] (已完成, 标注未完成)",
            "drawing_2.dxf (处理中, 未标注)",
            "drawing_3.dxf (已完成, 标注完成)"
        ])
        
        app.status_var = Mock()
        app.status_var.set = Mock()
        
        # 设置初始状态
        app.display_file = test_files[0]
        app.current_file = test_files[0]
        
        # 测试1：尝试切换到未完成处理的文件
        print(f"\n🔄 测试1: 切换到未完成处理的文件")
        
        original_display_file = app.display_file
        
        # 模拟messagebox
        import tkinter.messagebox as messagebox
        original_showwarning = messagebox.showwarning
        warning_called = False
        
        def mock_showwarning(title, message):
            nonlocal warning_called
            warning_called = True
            print(f"  警告对话框: {title}")
            print(f"  消息: {message}")
        
        messagebox.showwarning = mock_showwarning
        
        # 尝试切换到处理中的文件
        app.switch_to_file('drawing_2.dxf')
        
        # 检查是否显示了警告并且没有切换
        switch_blocked = warning_called and app.display_file == original_display_file
        print(f"  警告显示: {warning_called}")
        print(f"  显示文件保持不变: {app.display_file == original_display_file}")
        
        success1 = switch_blocked
        print(f"✅ 测试1结果: {'通过' if success1 else '失败'}")
        
        # 测试2：成功切换到已完成处理的文件
        print(f"\n🔄 测试2: 切换到已完成处理的文件")
        
        # 模拟_save_current_file_data和_load_file_data方法
        app._save_current_file_data = Mock()
        app._load_file_data = Mock()
        
        # 尝试切换到已完成的文件
        app.switch_to_file('drawing_3.dxf')
        
        # 检查是否成功切换
        switch_success = app.display_file == test_files[2]
        print(f"  切换成功: {switch_success}")
        print(f"  新显示文件: {os.path.basename(app.display_file) if app.display_file else 'None'}")
        
        success2 = switch_success
        print(f"✅ 测试2结果: {'通过' if success2 else '失败'}")
        
        # 恢复原始方法
        messagebox.showwarning = original_showwarning
        
        root.destroy()
        
        # 清理测试目录
        import shutil
        shutil.rmtree(temp_dir)
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_revert_mechanism():
    """测试回退机制"""
    print("\n=== 测试回退机制 ===")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 创建模拟的文件下拉菜单
        app.file_combo = Mock()
        app.file_combo.get = Mock(return_value="[drawing_1.dxf] (已完成, 标注未完成)")
        app.file_combo.set = Mock()
        app.file_combo.__getitem__ = Mock(return_value=[
            "[drawing_1.dxf] (已完成, 标注未完成)",
            "drawing_2.dxf (处理中, 未标注)"
        ])
        
        # 测试回退机制
        print(f"🔄 测试回退机制")
        
        original_selection = "[drawing_1.dxf] (已完成, 标注未完成)"
        current_file_name = "drawing_1.dxf"
        
        # 调用回退方法
        app._revert_file_selection(original_selection, current_file_name)
        
        # 检查是否调用了set方法
        set_called = app.file_combo.set.called
        print(f"  文件下拉菜单set方法被调用: {set_called}")
        
        if set_called:
            call_args = app.file_combo.set.call_args[0][0]
            print(f"  设置的值: {call_args}")
            revert_success = call_args == original_selection
        else:
            revert_success = False
        
        print(f"✅ 回退机制测试: {'通过' if revert_success else '失败'}")
        
        root.destroy()
        
        return revert_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件显示和切换修复测试")
    print("=" * 50)
    print("目标: 验证文件显示始终为第一个文件，切换时的状态检查和回退")
    print()
    
    tests = [
        ("文件显示逻辑", test_file_display_logic),
        ("文件切换逻辑", test_file_switching_logic),
        ("回退机制", test_revert_mechanism)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件显示和切换修复成功。")
        print("\n📋 修复效果:")
        print("✅ 界面始终显示第一个文件的信息")
        print("✅ 后台处理不影响界面显示")
        print("✅ 切换时正确检查文件状态")
        print("✅ 切换失败时正确回退")
        
        print("\n🎯 用户使用效果:")
        print("• 多文件处理时界面始终显示第一个文件")
        print("• 只有处理完成的文件才能切换查看")
        print("• 切换失败时弹出详细提示并回退")
        print("• 文件状态实时更新但不影响显示")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
