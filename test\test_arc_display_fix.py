#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试圆弧显示修复
"""

import sys
import os
import numpy as np

def test_arc_angle_conversion():
    """测试圆弧角度转换"""
    try:
        print("测试圆弧角度转换:")
        
        # 测试角度转换逻辑
        test_cases = [
            # (start_angle, end_angle, description)
            (0, np.pi/2, "弧度制：0到π/2"),
            (0, 90, "角度制：0到90度"),
            (np.pi, 2*np.pi, "弧度制：π到2π"),
            (180, 360, "角度制：180到360度"),
            (270, 90, "跨越0度：270到90度"),
            (5*np.pi/4, np.pi/4, "弧度制跨越0度"),
            (-np.pi/2, np.pi/2, "负角度弧度制"),
            (-90, 90, "负角度角度制")
        ]
        
        for start, end, desc in test_cases:
            print(f"\n  {desc}")
            print(f"    原始: start={start:.3f}, end={end:.3f}")
            
            # 模拟角度处理逻辑
            start_angle = start
            end_angle = end
            
            # 角度单位转换
            if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                start_angle = np.degrees(start_angle)
                end_angle = np.degrees(end_angle)
                print(f"    转换后: start={start_angle:.1f}°, end={end_angle:.1f}°")
            
            # 标准化角度
            start_angle = start_angle % 360
            end_angle = end_angle % 360
            print(f"    标准化: start={start_angle:.1f}°, end={end_angle:.1f}°")
            
            # 处理跨越0度的圆弧
            if end_angle < start_angle:
                if (start_angle - end_angle) > 180:
                    end_angle += 360
                    print(f"    跨越0度调整: end={end_angle:.1f}°")
                else:
                    start_angle, end_angle = end_angle, start_angle
                    print(f"    角度交换: start={start_angle:.1f}°, end={end_angle:.1f}°")
            
            print(f"    最终: start={start_angle:.1f}°, end={end_angle:.1f}°")
        
        return True
        
    except Exception as e:
        print(f"✗ 圆弧角度转换测试失败: {e}")
        return False

def test_arc_visualization():
    """测试圆弧可视化"""
    try:
        from cad_visualizer import CADVisualizer
        import matplotlib.pyplot as plt
        
        print("测试圆弧可视化:")
        
        visualizer = CADVisualizer()
        
        # 创建测试圆弧
        test_arcs = [
            {
                'type': 'ARC',
                'center': (100, 100),
                'radius': 50,
                'start_angle': 0,
                'end_angle': np.pi/2,  # 弧度制
                'layer': 'test'
            },
            {
                'type': 'ARC',
                'center': (250, 100),
                'radius': 40,
                'start_angle': 0,
                'end_angle': 90,  # 角度制
                'layer': 'test'
            },
            {
                'type': 'ARC',
                'center': (400, 100),
                'radius': 30,
                'start_angle': 270,
                'end_angle': 90,  # 跨越0度
                'layer': 'test'
            },
            {
                'type': 'ARC',
                'center': (100, 250),
                'radius': 35,
                'start_angle': 3*np.pi/2,
                'end_angle': np.pi/2,  # 弧度制跨越0度
                'layer': 'test'
            }
        ]
        
        print(f"测试圆弧数量: {len(test_arcs)}")
        
        for i, arc in enumerate(test_arcs):
            print(f"  圆弧 {i+1}: 中心={arc['center']}, 半径={arc['radius']}")
            print(f"           起始角度={arc['start_angle']}, 结束角度={arc['end_angle']}")
        
        # 测试绘制
        try:
            # 清除之前的图形
            visualizer.ax_detail.clear()
            
            # 绘制圆弧
            for arc in test_arcs:
                visualizer._draw_entity(arc, '#FF0000', 2, 1.0, visualizer.ax_detail)
            
            # 设置坐标轴
            visualizer.ax_detail.set_xlim(0, 500)
            visualizer.ax_detail.set_ylim(0, 300)
            visualizer.ax_detail.set_aspect('equal')
            visualizer.ax_detail.grid(True, alpha=0.3)
            visualizer.ax_detail.set_title('圆弧显示测试')
            
            print("✓ 圆弧绘制执行成功")
            return True
            
        except Exception as e:
            print(f"✗ 圆弧绘制执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"✗ 圆弧可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arc_edge_cases():
    """测试圆弧边界情况"""
    try:
        print("测试圆弧边界情况:")
        
        # 测试各种边界情况
        edge_cases = [
            {
                'name': '完整圆',
                'start_angle': 0,
                'end_angle': 2*np.pi
            },
            {
                'name': '半圆',
                'start_angle': 0,
                'end_angle': np.pi
            },
            {
                'name': '四分之一圆',
                'start_angle': 0,
                'end_angle': np.pi/2
            },
            {
                'name': '很小的圆弧',
                'start_angle': 0,
                'end_angle': np.pi/36  # 5度
            },
            {
                'name': '负角度开始',
                'start_angle': -np.pi/4,
                'end_angle': np.pi/4
            },
            {
                'name': '大角度值',
                'start_angle': 450,  # 超过360度
                'end_angle': 540
            }
        ]
        
        for case in edge_cases:
            print(f"\n  {case['name']}:")
            start = case['start_angle']
            end = case['end_angle']
            
            print(f"    原始角度: {start:.3f} -> {end:.3f}")
            
            # 模拟处理逻辑
            start_angle = start
            end_angle = end
            
            # 角度单位转换
            if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                start_angle = np.degrees(start_angle)
                end_angle = np.degrees(end_angle)
            
            # 标准化角度
            start_angle = start_angle % 360
            end_angle = end_angle % 360
            
            # 处理跨越0度的圆弧
            if end_angle < start_angle:
                if (start_angle - end_angle) > 180:
                    end_angle += 360
                else:
                    start_angle, end_angle = end_angle, start_angle
            
            print(f"    处理后角度: {start_angle:.1f}° -> {end_angle:.1f}°")
            
            # 计算圆弧角度范围
            arc_span = end_angle - start_angle
            print(f"    圆弧跨度: {arc_span:.1f}°")
        
        return True
        
    except Exception as e:
        print(f"✗ 圆弧边界情况测试失败: {e}")
        return False

def test_arc_display_code():
    """测试圆弧显示代码"""
    try:
        print("测试圆弧显示代码:")
        
        # 检查代码中是否包含了正确的圆弧处理逻辑
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码
        checks = [
            ("角度单位转换", "np.degrees"),
            ("角度标准化", "% 360"),
            ("跨越0度处理", "end_angle += 360"),
            ("Arc patch创建", "Arc(center"),
            ("角度范围检查", "2*np.pi")
        ]
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✓ {check_name}: 已包含")
            else:
                print(f"✗ {check_name}: 未找到")
                return False
        
        print("✓ 圆弧显示代码检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 圆弧显示代码测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试圆弧显示修复...")
    print("=" * 60)
    
    tests = [
        ("圆弧角度转换", test_arc_angle_conversion),
        ("圆弧边界情况", test_arc_edge_cases),
        ("圆弧显示代码", test_arc_display_code),
        ("圆弧可视化", test_arc_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"结果: ✅ 通过")
            else:
                print(f"结果: ❌ 失败")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 圆弧显示修复测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
